package com.cmpay.code.framework.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.lang.reflect.Method;

import static com.cmpay.code.framework.common.exception.GlobalErrorCodePc.*;
import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-11 9:45:56
 * @version: 1.0
 */
@Slf4j
public class ParameterCheckConfig<T> implements Serializable {


    /**
     * 根据平台判断参数不能为空
     * @param obj 对象
     */
    public static <T> void getValue(T obj) {

        // 通过反射获取方法
        String platform = getString(obj, "getPlatform");
        // 通过反射获取方法
        String agentId = getString(obj, "getAgentId");
        // 通过反射获取方法
        String acceptId = getString(obj, "getAcceptId");
        // 通过反射获取方法
        String partnerId = getString(obj, "getPartnerId");
        // 通过反射获取方法
        String branchId = getString(obj, "getBranchId");

        // 校验参数
        if (StringUtils.isEmpty(platform)) {
            throw exception(PLATFORM_NOT_NULL);
        }
        if ("3".equals(platform)) {
            if (StringUtils.isEmpty(agentId)) {
                throw exception(AGENT_ID_NOT_NULL);
            }
        }
        if ("4".equals(platform)) {
            if (StringUtils.isEmpty(acceptId)) {
                throw exception(ACCEPT_ID_NOT_NULL);
            }
        }
        if ("2".equals(platform)) {
            if (StringUtils.isEmpty(partnerId)) {
                throw exception(PARTNER_ID_NOT_NULL);
            }
        }
        if ("1".equals(platform)) {
            if (StringUtils.isEmpty(branchId)) {
                throw exception(BRANCH_ID_NOT_NULL);
            }
        }
    }

    /**
     * 使用反射获取这个对象的某一个值
     * @param obj 对象
     * @param methodName  方法名称
     * @return  某个参数的值
     */
    private static <T> String getString(T obj,String methodName)  {
        String result = "";
        try {
            Method method = obj.getClass().getMethod(methodName);
            result = (String) method.invoke(obj);
        } catch (Exception e) {
            log.info("没有找到该方法:{}", e.getMessage());
        }
        return result;
    }
}
