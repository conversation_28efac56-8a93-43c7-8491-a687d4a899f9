package com.cmpay.code.framework.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Title: SystemConfig
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.config
 * @Date 2024/6/20 11:30
 * @description: 系统配置对象
 */
@Setter
@Getter
@Component
public class SystemConfig {

    /**
     * 系统运行环境
     */
    @Value("${platform}")
    private String platform;

    /**
     * 系统版本
     */
    @Value("${spring.profiles.active}")
    private String environment;

}
