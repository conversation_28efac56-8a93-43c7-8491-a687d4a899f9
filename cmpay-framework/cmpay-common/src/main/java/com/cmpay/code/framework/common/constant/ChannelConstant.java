package com.cmpay.code.framework.common.constant;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/12/8 9:26
 * version 1.0
 * 通道常量
 */
public class ChannelConstant {

    // 商户通道表 状态禁用
    public static final String TOMORROW_MERCHANT_UPDATE_CHANNEL = "1";
    public static final String NO_TOMORROW_MERCHANT_UPDATE_CHANNEL = "0";

    // 商户通道表 状态正常
    public static final Integer MERCHANT_CHANNEL_STATUS_NORMAL = 1;

    // 随行付通道值
    public static final String CHANNEL_SXF_TQ_VALUE = "sxf_tq";


    //-------------------------------瑞银信相关常量-----------------------------------------

    // 瑞银信通道
    public static final String CHANNEL_RUIYINXIN_VALUE = "ruiyinxin";
    // 修改结算卡请求地址后缀，需要拼接域名或ip
    public static final String CHANGE_RYX_DEBIT_CARD_REQUEST_URL_SUFFIX = "/internal/signinryx.do";
    // 确认开通请求地址后缀，需要拼接域名或ip
    public static final String CHANGE_RYX_CONFIRM_OPENED_URL_SUFFIX = "/internal/confirminruiyinxin.do";
    // 修改结算卡类型
    public static final String CHANGE_RYX_DEBIT_CARD_TYPE_CARD = "card";

    //-------------------------------瑞银信(H)相关常量-----------------------------------------

    // 瑞银信(H)通道
    public static final String CHANNEL_HULU_VALUE = "hulu";
    // 瑞银信(H)基础修改请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_BASE_CHANGE = "/internal/changehulubaseinfomermsg.do";
    // 瑞银信(H)添加终端请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_SAVE_TERMINAL = "/internal/increasehulumerterminal.do";
    // 瑞银信(H)修改结算卡请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_CHANGE_SETTLEMENT_CARD_TERMINAL = "/internal/changehulupayinfomermsg.do";
    // 瑞银信(H)签约地址请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_GET_ELECTRON_SIGN_URL = "/internal/gethulubillagreementsign.do";
    // 瑞银信(H)查询商户状态请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_GET_MERCHANT_STATUS = "/internal/searchhulumerchantstatus.do";
    // 瑞银信(H)支付修改请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_CHANGE_PAYMENT = "/internal/changehulupayinfomermsg.do";
    // 瑞银信(H)查询修改状态请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_QUERY_CHANGE_STATUS = "/internal/searchhuluupdatemerchantresult.do";
    // 瑞银信(H)查询终端状态请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_QUERY_TERMINAL_STATUS = "/internal/searchhuluterminal.do";
    // 瑞银信(H)查询电子签约状态请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_GET_SIGN_STATUS = "/internal/gethulubillagreementsignstatus.do";
    // 瑞银信(H)申请开通请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_APPLY_OPENED = "/internal/signinhulu.do";
    // 瑞银信(H)确认开通请求地址后缀，需要拼接域名或ip
    public static final String CHANNEL_HULU_CONFIRM_OPENED = "/internal/confirminhulu.do";


    //------------------------------- 银联商务相关常量-----------------------------------------
    // 银联商务申请开通请求地址后缀，需要拼接域名或ip
    public static final String YLSW_APPLY_OPENED = "/internal/signinylsw.do";
    // 银联商务查询状态请求地址后缀，需要拼接域名或ip
    public static final String YLSW_GET_STATUS = "/internal/searchylswstatus.do";
    // 银联商务获取签约地址请求地址后缀，需要拼接域名或ip
    public static final String YLSW_GET_SIGN_URL = "/internal/ylswsigncontract.do";
    // 银联商务对公账户验证请求地址后缀，需要拼接域名或ip
    public static final String YLSW_CORPORATE_ACCOUNT_VERIFICATION = "/internal/ylswaccountcheck.do";
    // 银联商务对公账户认证请求地址后缀，需要拼接域名或ip
    public static final String YLSW_CORPORATE_ACCOUNT_AUTHENTICATION = "/internal/ylswauthentication.do";
    // 银联商务确认开通请求地址后缀，需要拼接域名或ip
    public static final String YLSW_CONFIRM_OPENED = "/internal/confirminylsw.do";

    //--------------------------------国通相关常量----------------------------------------------------
    // 国通发送签约验证码
    public static final String GT_SIGN_CODE = "/internal/guotongsendcode.do";
    // 国通修改提交
    public static final String GT_MODIFICATION_SUBMISSION = "/internal/signinguotong.do";
    // 国通修改结算卡
    public static final String GT_MODIFY_SETTLEMENT_CARD = "/internal/signinguotong.do";
    // 国通安心签开户
    public static final String GT_OPEN_AXQ_CARD = "/internal/guotonganxinqian.do";
    // 国通电子合同签约
    public static final String GT_ELECTRONIC_CONTRACT_SIGNING = "/internal/guotongsupplementary.do";
    // 国通查询商户状态
    public static final String QUERY_MERCHANT_STATUS = "/internal/searchguotongstatus.do";

    //--------------------------------通道通用常量----------------------------------------------------
    public static final String UPDATE_MERCHANT_CARD = "/internal/updatemerchantcard.do";

    public static final String SEARCH_UPDATE_MERCHANT_CARD_RESULT = "/internal/searchupdatemerchantcardresult.do";

    //--------------------------------问题处理----------------------------------------------------
    // 问题处理修改或添加银行信息
    public static final String QUESTION_UPDATE_BANK_MSG = "/internal/questionupdatebankmsg.do";
    // 二维码导出
    public static final String EXPORT_QRCODE_EXCEL = "/internal/exportqrcodeexcel.do";
    // 查结算统计异常处理
    public static final String RENEW_PAIDOUT_AND_STATISTICS = "/internal/renewpaidoutandstatistics.do";
    // 查询邮米账户余额
    public static final String SEARCH_SXF_TQ_BALANCE = "/internal/searchsxftqbalance.do";
    // 结算查询
    public static final String SEARCH_MERCHANT_PAIDOUT_MSG = "/internal/searchmerchantpaidoutmsg.do";
    // 复制商户
    public static final String COPY_MERCHANT = "/internal/copyMerchant.do";
    // 导出瑞银信补贴账单
    public static final String EXPORT_RYX_SUBSIDY = "/internal/exportryxsubsidy.do";
    // 商户活动报名：随行付分润活动
    public static final String CHANGE_MERCHANT_PROFIT_ACTIVITY = "/internal/changemerchantprofitactivity.do";
    // 查询账户风险等级
    public static final String SEARCH_ACCOUNT_RISK = "/internal/searchaccountrisk.do";
    // 批量修改费率
    public static final String BATCH_XY_EXCHANGE_MERCHANT = "/internal/batchxyexchangemerchant.do";
    // 多线程批量导入入口
    public static final String BATCH_IMPORT_FOR_MQ_SEND_TEST = "/internal/batchImportForMqSendTest.do";
    // 批量开通提现按钮
    public static final String BATCH_HULU_MERCHANT_IMPORT = "/internal/batchhulumerchantimport.do";
    // 批量注销商户
    public static final String BATCH_DELETE_MERCHANT_IMPORT = "/internal/batchdeletemerchantimport.do";
    // 批量删除费率包
    public static final String BATCH_DELETE_MERCHANT_RATE_PACKAGE = "/internal/batchdeletemerchantraterackage.do";
    // 批量关停、开启商户交易
    public static final String BATCH_UPDATE_MERCHANT_STATUS = "/internal/batchupdatemerchantstatus44.do";
    // 邮米结算处理
    public static final String YOU_MI_PAID_OUT_DISPOSE = "/internal/youmipaidoutdispose.do";
    // 批量查询商户状态
    public static final String SEARCH_BATCH_MERCHANT_STATUS = "/internal/searchbatchmerchantstatus.do";
    // 批量进件
    public static final String SAVE_BATCH_MERCHANT_STATUS = "/internal/savebatchmerchantstatus.do";
    // 批量查询测试支付
    public static final String SEARCH_BATCH_TEST_CHANNEL_PAY = "/internal/searchbatchtestchannelpay.do";

    //--------------------------------发送通知接口----------------------------------------------------
    // public static final String SEND_NOTICE = "/merchantminiapp/api/notification/save";

    // 修改费率
    public static final String CHANGE_RATES = "/internal/updatemerchantrate.do";
    // 测试支付接口
    public static final String TEST_CURRENT_CHANNEL_PAYMENT = "/internal/testcurrentchannelpayment.do";

    //--------------------------------补资料----------------------------------------------------
    public static final String MERCHANT_INFO_VERIFY = "/internal/merchantInfoVerify";

    //--------------------------------数据报表----------------------------------------------------
    public static final String SAVE_SUBSIDY_MONEY = "/internal/saveSubsidyMoney.do";

    //--------------------------------喇叭----------------------------------------------------
    // 支付宝云喇叭解绑
    // public static final String UNBINDING_ALIPAY_AUDIO = "/internal/unbindingalipayaudio.do";
    // 支付宝云喇叭绑定
    // public static final String BINDING_ALIPAY_AUDIO = "/internal/bindingalipayaudio.do";

    //--------------------------------市级分公司----------------------------------------------------
    // 新增市级分公司
    public static final String SAVE_NEW_PARENT_CHANNEL = "/internal";
    // 修改市级分公司
    public static final String SAVE_PARENT_CHANNEL_MSG = "/internal/saveparentchannelmsg.do";

    //--------------------------------商户管理----------------------------------------------------
    // 切换商户支付场景
    public static final String OPEN_MERCHANT_SENCE = "/internal/openmerchantsence.do";

}
