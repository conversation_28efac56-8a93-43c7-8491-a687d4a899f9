package com.cmpay.code.framework.common.constant;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/11/17 17:10
 * version 1.0
 * 管控常量
 */
public class ControlConstant {

    // 管控返回状态  成功
    public static final Integer SUCCESS = 0;
    // 管控返回状态  失败
    public static final Integer FAIL = 1;

    // oss路径地址
    public static final String MERCHANT_CONTROL_OSS_ROOT_PATH = "merchant_control";
    // oss管控白名单文件前缀名
    public static final String MERCHANT_CONTROL_OSS_WHITE_LIST_PREFIX = "白名单_";

    // 商户管控保留状态key值
    public static final String NEW_MERCHANT_CONTROL_RETAIN_STATUS = "new_merchant_control_retain_status";
    // 商户管控审核状态key值
    public static final String NEW_MERCHANT_CONTROL_AUDIT_STATUS = "new_merchant_control_audit_status";
    // 商户费率类型
    public static final String MERCHANT_SUBSDIES_TYPE = "merchant_subsdies_type";
    // 管控批次状态
    public static final String NEW_MERCHANT_CONTROL_BATCH_STATUS = "new_merchant_control_batch_status";

    // 修改商户补贴包月固定一次性的值，知识前端select标签的值，不是真实值
    // 月固定
    public static final Integer MONTHLY_FIXED_SELECT_VALUE = 0;
    // 一次性
    public static final Integer MONTHLY_DISPOSABLE_SELECT_VALUE = 1;


    // 接受定时任务请求，修改批次状态为生效中，并且调用定时生效接口
    public static final String CHANGE_BATCH_STATUS_AND_PRODUCER_SEND_MESSAGE_MQ = "https://newmanage.gdwxyf.com/admin-api/openness-api/scheduled/changeBatchStatusAndProducerSendMessageMq";

    // 接受定时任务请求，批量添加商户费率包或更改费率
    public static final String BATCH_CHANGE_MERCHANT_RATE_SETTING = "https://newmanage.gdwxyf.com/admin-api/openness-api/scheduled/batch/changeMerchantRateSetting";

    //定时任务，修改管控批次状态
    public static final String CONTROL_BATCH_UPDATE_STATUS = "/control/update-status";

    // 批次状态  0未开始
    public static final Integer CONTROL_BATCH_0 = 0;
    // 批次状态  1审核中
    public static final Integer CONTROL_BATCH_1 = 1;
    // 批次状态  2生效中
    public static final Integer CONTROL_BATCH_2 = 2;
    // 批次状态  3已生效
    public static final Integer CONTROL_BATCH_3 = 3;

    // 批次状态  4已撤销
    public static final Integer CONTROL_BATCH_4 = 4;

    //调用类型；0系统内部调用；
    public static final Integer CONTROL_BATCH_CALL_TYPE_0 = 0;
    //调用类型；1外部api调用；
    public static final Integer CONTROL_BATCH_CALL_TYPE_1 = 1;


    // 商户数据表状态    0 未执行
    public static final Integer CONTROL_IMPORT_DATA_0 = 0;
    // 商户数据表状态    1 已执行
    public static final Integer CONTROL_IMPORT_DATA_1 = 1;

    // 商户数据表状态    -1不处理
    public static final Integer CONTROL_DATA_AUDIT_STATUS_NOT = -1;
    // 商户数据表状态     1处理
    public static final Integer CONTROL_DATA_AUDIT_STATUS_1 = 1;

    //管控商户状态 - 保留状态
    public static final Integer CONTROL_DATA_0 = 0;//默认
    public static final Integer CONTROL_DATA_1 = 1;//审核中
    public static final Integer CONTROL_DATA_2 = 2;//已通过
    public static final Integer CONTROL_DATA_3 = 3;//不通过

    //管控商户处理状态  未进行操作
    public static final Integer CONTROL_DATA_RESULT_STATUS_DEFAULT = 0;
    //管控商户处理状态  操作完成
    public static final Integer CONTROL_DATA_RESULT_STATUS_SUCCESS = 1;
    //管控商户处理状态  操作失败
    public static final Integer CONTROL_DATA_RESULT_STATUS_FAIL = 2;


    // 保留状态 ：0默认
    public static final Integer CONTROL_DATA_RETAIN_STATUS_DEFAULT = 0;
    // 保留状态 ：1审核中；
    public static final Integer CONTROL_DATA_RETAIN_STATUS_PROGRESS = 1;
    // 保留状态 2已通过；
    public static final Integer CONTROL_DATA_RETAIN_STATUS_PASS = 2;
    // 保留状态 3不通过；
    public static final Integer CONTROL_DATA_RETAIN_STATUS_REJECT = 3;


    // 修改类型 1审核操作
    public static final Integer CONTROL_OPERATION_RECORDS_CHANGE_TYPE_AUDIT = 1;
    // 修改类型 2申请保留操作
    public static final Integer CONTROL_OPERATION_RECORDS_CHANGE_TYPE_RETAIN = 2;
    // 修改类型 3撤销操作
    public static final Integer CONTROL_OPERATION_RECORDS_CHANGE_TYPE_REVOKE = 3;
    //修改类型 4修改信息操作
    public static final Integer CONTROL_OPERATION_RECORDS_CHANGE_TYPE_UPDATE = 4;

    //修改类型 5修改处理状态
    public static final Integer CONTROL_OPERATION_RECORDS_CHANGE_TYPE_UPDATE_AUDIT_STATUS = 5;


    // 撤销状态 未撤销
    public static final Integer CONTROL_DATA_REVOKE_STATUS_NOT = 0;
    // 撤销状态 已撤销
    public static final Integer CONTROL_DATA_REVOKE_STATUS_ALREADY = 1;

    // 白名单 状态启用
    public static final Integer CONTROL_WHITE_LIST_STATUS_OPEN = 1;
    // 白名单 状态停用
    public static final Integer CONTROL_WHITE_LIST_STATUS_CLOSE = 0;

    // 白名单来源  pc
    public static final String CONTROL_WHITE_LIST_SOURCE_PC = "pc";
    // 白名单来源  api
    public static final String CONTROL_WHITE_LIST_SOURCE_API = "api";

    // 白名单删除  1删除
    public static final Integer CONTROL_WHITE_LIST_IS_DELETE_REMOVE = 1;
    // 白名单删除  0正常
    public static final Integer CONTROL_WHITE_LIST_IS_DELETE_NORMAL = 0;

}
