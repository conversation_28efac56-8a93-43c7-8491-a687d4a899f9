package com.cmpay.code.framework.common.constant;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/11/22 11:38
 * version 1.0
 * 字典常量
 */
public class DictConstant {
    // 商户补贴类型
    public static final String MERCHANT_SUBSIDES_TYPE_DICT_TYPE = "merchant_subsdies_type";
    // D0提现费率转换成符号形式
    public static final String D0_WITHDRAWAL_RATE_SYMBOL_DICT_TYPE = "d0_withdrawal_rate_symbol";
    // D0提现费率转换成中文形式
    public static final String D0_WITHDRAWAL_RATE_CHINESE_DICT_TYPE = "d0_withdrawal_rate_chinese";
    // 图片防伪校验，由咱们系统的图片名称转换为阿里云类型
    public static final String IMAGE_ANTI_FAKE_VERIFY_DICT_TYPE = "image_anti_fake_verify";
    // 备用通道交易级别   中文 -> 数字
    public static final String TRANSACTION_CHINESE_CONVERT_INT_LEVEL_TYPE = "TRANSACTION_CHINESE_CONVERT_INT_LEVEL";
    // 备用通道交易级别   数字 -> 中文
    public static final String TRANSACTION_INT_CONVERT_CHINESE_LEVEL_TYPE = "TRANSACTION_INT_CONVERT_CHINESE_LEVEL";
}
