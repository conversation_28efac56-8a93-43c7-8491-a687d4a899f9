package com.cmpay.code.framework.common.constant;

/**
 * @Title: EnvironmentTypeConstant
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.constant
 * @Date 2024/6/20 11:11
 * @description: 系统环境类型配置
 */
public class EnvironmentTypeConstant {
    // 阿里云凭证校验 AccessKeyId，AccessKeySecret
    public static final String CREDENTIAL_VERIFY_ACCESS_KEY_ID_TYPE = "CREDENTIAL_VERIFY_ACCESS_KEY_ID";
    public static final String CREDENTIAL_VERIFY_ACCESS_KEY_SECRET_TYPE = "CREDENTIAL_VERIFY_ACCESS_KEY_SECRET";
    // 内部查询设备品牌信息
    public static final String INTERNAL_QUERY_EQUIPMENT_BRAND_INFO = "INTERNAL_QUERY_EQUIPMENT_BRAND_INFO";
    // 推送设备图片接口
    public static final String DEVICE_PUSH_IMAGE = "DEVICE_PUSH_IMAGE";
    // 设备绑定业务异常处理列表
    public static final String QUERY_DEVICE_INFO = "QUERY_DEVICE_INFO";
    // 设备解绑de
    public static final String UNBIN_DEVICE = "UNBIN_DEVICE";
    // 配置设备广告
    public static final String CONFIGURE_EQUIPMENT_ADVERT = "CONFIGURE_EQUIPMENT_ADVERT";
    // 删除商户设备
    public static final String REMOVE_MERCHANT_EQUIPMENT = "REMOVE_MERCHANT_EQUIPMENT";
    // 瑞银新重新签约电子合同
    public static final String SEARCH_RYX_SIGNIN_URL = "SEARCH_RYX_SIGNIN_URL";
    // 申请开通分账回调地址
    public static final String APPLY_PROFIT_SHARE_CALLBACK_URL = "APPLY_PROFIT_SHARE_CALLBACK_URL";
    // 设备绑定解绑地址
    public static final String SAVE_QR_CODE_MSG = "SAVE_QR_CODE_MSG";
    // 保存单商户费率包设置
    public static final String SAVE_MERCHANT_RATE_PACKAGE = "SAVE_MERCHANT_RATE_PACKAGE";
    // 修改政策审核信息
    public static final String V2_UPDATE_POLICY_INFO = "V2_UPDATE_POLICY_INFO";
    // D0提现费率审核
    public static final String HULU_POLICY_REVIEW = "HULU_POLICY_REVIEW";
    // 批量保存商户费率包
    public static final String BATCH_SAVE_MERCHANT_RATE_PACKAGE = "BATCH_SAVE_MERCHANT_RATE_PACKAGE";
    // 商户信息校验
    public static final String INTERNAL_MERCHANT_INFO_VERIFY = "INTERNAL_MERCHANT_INFO_VERIFY";
    //初始化商户图片加密列表
    public static final String INIT_MERCHANT_IMAGES = "INIT_MERCHANT_IMAGES";

    //撤销实名认证
    public static final String WX_AUTH_CHANNEL = "WX_AUTH_CHANNEL";

    //初始化单个商户图片加密
    public static final String MERCHANT_IMAGE_SHOW = "MERCHANT_IMAGE_SHOW";

    public static final String VERIFY_MERCHANT_INFO_REVIEW_INFO = "VERIFY_MERCHANT_INFO_REVIEW_INFO";
    //国通修改提现
    public static final String GUOTONG_UPDATE_D0 = "GUOTONG_UPDATE_D0";
    //修改费率包上游费率
    public static final String CHANGE_MERCHANT_RATE_PACKAGE_TOUP = "CHANGE_MERCHANT_RATE_PACKAGE_TOUP";
    public static final String UPDATE_MERCHANT_RATE = "UPDATE_MERCHANT_RATE";
    public static final String CHANGE_MERCHANT_CHANNEL = "CHANGE_MERCHANT_CHANNEL";
    public static final String CHANGE_MERCHANT_CHANNEL_TOUP = "CHANGE_MERCHANT_CHANNEL_TOUP";

    // ————————————————————————————————————数据报表————————————————————————————————————————————————————
    // 收支结算汇总表（省内）
    public static final String QUERY_NEW_GD_RECEIVE = "QUERY_NEW_GD_RECEIVE";
    // 收支结算汇总表（省外）
    public static final String QUERY_NEW_OTH_SHARE = "QUERY_NEW_OTH_SHARE";

    // 机构数据表——机构数据收入表——外省分页查询
    public static final String SEARCH_OTHER_ORAGN_INFO = "SEARCH_OTHER_ORAGN_INFO";

    // 机构数据收入表——外省导出机构数据收入Excel
    public static final String EXPORT_OTHER_ORAGN_INFO = "EXPORT_OTHER_ORAGN_INFO";
    // 机构数据收入表——省内分页查询
    public static final String SEARCH_ORAGN_INFO = "SEARCH_ORAGN_INFO";
    // 机构数据收入表——省内导出机构数据收入Excel
    public static final String EXPORT_ORAGN_INFO = "EXPORT_ORAGN_INFO";
    // 收入结算表
    public static final String SEARCH_REPORT_ONE = "SEARCH_REPORT_ONE";
    // 收入结算表(北京)
    public static final String SEARCH_BJ_REPORT_ONE = "SEARCH_BJ_REPORT_ONE";
    // 微邮付商户明细
    public static final String SEARCH_WYF_REPORT = "SEARCH_WYF_REPORT";
    // 网点清分汇总列表
    public static final String QUICK_SEARCH_AREA_REPORT = "QUICK_SEARCH_AREA_REPORT";
    // 网点清分汇总导出
    public static final String QUICK_EXPORT_AREA_REPORT = "QUICK_EXPORT_AREA_REPORT";
    // 技术服务费
    public static final String QUICK_SEARCH_SKILL_MONEY = "QUICK_SEARCH_SKILL_MONEY";
    // 技术服务费导出
    public static final String QUICK_EXPORT_SKILL_MONEY = "QUICK_EXPORT_SKILL_MONEY";
    // 月度结算报表导出列表
    public static final String SEARCH_DOWNLOAD_REPORT_URL = "SEARCH_DOWNLOAD_REPORT_URL";
    // 月度结算报表导出
    public static final String EXPORT_REPORT_DOWNLOAD = "EXPORT_REPORT_DOWNLOAD";
    // 结算表下载年份
    public static final String SEARCH_WYF_YEAR = "SEARCH_WYF_YEAR";
    // 结算表下载月份
    public static final String SEARCH_WYF_MONTH = "SEARCH_WYF_MONTH";
    // 普通商户推广服务费明细
    public static final String SEARCH_SUBSIDY_INFO = "SEARCH_SUBSIDY_INFO";
    // 结算表查询权限处理
    public static final String SEARCH_ORGAN_AUTHORITY = "SEARCH_ORGAN_AUTHORITY";
    // 结算表查询权限处理删除
    public static final String DELETE_ORGAN_AUTHORITY = "DELETE_ORGAN_AUTHORITY";
    // 结算表查询权限处理新增
    public static final String SAVE_ORGAN_AUTHORITY = "SAVE_ORGAN_AUTHORITY";
    //签约链接
    public static final String CHANNEL_LONG_TO_SHORTURL = "CHANNEL_LONG_TO_SHORTURL";
    //申请开通拉卡拉通道
    public static final String OPEN_LAKALE_CHANNEL = "OPEN_LAKALE_CHANNEL";
    //查询拉卡拉电子合约状态
    public static final String LAKALA_EC_SEARCH = "LAKALA_EC_SEARCH";
    //查询拉卡拉商户通道状态
    public static final String LAKALA_STATUS_SEARCH = "LAKALA_STATUS_SEARCH";
    //查询拉卡拉报备结果
    public static final String LAKAL_REPORT_SEARCH = "LAKAL_REPORT_SEARCH";
    //拉卡拉确认开通
    public static final String CONIRMIN_MERCHANT_CHANNEL = "CONIRMIN_MERCHANT_CHANNEL";
    // 处理普通商户推广服务费明细
    public static final String SAVE_SUBSIDY_MONEY = "SAVE_SUBSIDY_MONEY";
    // 结算汇总表处理数据
    public static final String UPLOAD_SETTLE_MENT_INFO = "UPLOAD_SETTLE_MENT_INFO";
    // 邮政结算报表
    public static final String QUERY_SETTLE_MENT_ONE = "QUERY_SETTLE_MENT_ONE";
    // 省内查询网点清分成本汇总
    public static final String QUERY_NEW_AREA_REPORT = "QUERY_NEW_AREA_REPORT";
    // 通道更新上游结算卡
    public static final String UPDATE_MERCHANT_CARD = "UPDATE_MERCHANT_CARD";
    // 解绑喇叭
    public static final String UNBINDING_ALIPAY_AUDIO = "UNBINDING_ALIPAY_AUDIO";
    // 绑定支付宝云喇叭
    public static final String BINDING_ALIPAY_AUDIO = "BINDING_ALIPAY_AUDIO";
    // 发送商户通知
    public static final String NOTIFICATION_SAVE = "NOTIFICATION_SAVE";

    public static final String ADD_DUTY = "ADD_DUTY";
    // 更新上游结算卡状态
    public static final String SEARCH_UPDATE_MERCHANT_CARD_RESULT = "SEARCH_UPDATE_MERCHANT_CARD_RESULT";
    public static final String CHANGE_BATCH_MERCHANT_CHANNEL = "CHANGE_BATCH_MERCHANT_CHANNEL";
    public static final String EDIT_NFC_DEVICE = "EDIT_NFC_DEVICE";
    //进件接口
    public static final String REGISTER_NEW_MERCHANT = "REGISTER_NEW_MERCHANT";
    // ————————————————————————————————————通道信息————————————————————————————————————————————————————
    // 开通国通通道
    public static final String SIGNING_GUO_TONG = "SIGNING_GUO_TONG";

    // 开通国通通道
    public static final String SEND_BATCH_OPEN_CHANNEL = "SEND_BATCH_OPEN_CHANNEL";

    //批量修改设备状态
    public static final String UPDATE_USED_DEVICE = "UPDATE_USED_DEVICE";

    //工单查询
    public static final String QUERY_MCH_ID = "QUERY_MCH_ID";

    //加密接口
    public static final String ENCRYPTION_RES = "ENCRYPTION_RES";

    //预授权开通/关闭
    public static final String CHANGE_SXF_TQ_ACCOUNT = "CHANGE_SXF_TQ_ACCOUNT";

    //解绑pos
    public static final String UN_BIN_DING_DEVICEGT  = "UN_BIN_DING_DEVICEGT";

    //测试支付
    public static final String TEST_CURRENT_CHANNEL_PAYMENT  = "TEST_CURRENT_CHANNEL_PAYMENT";

    //修改商户上游资料
    public static final String CHANGE_MERCHANT_DATE = "CHANGE_MERCHANT_DATE";


    //创建补贴充值任务调用
    public static final String PROCESS_ACCOUNT_TASK  = "PROCESS_ACCOUNT_TASK";
    //查询邮米账户余额
    public static final String SEARCH_BALANCE = "SEARCH_BALANCE";
    //创建商户补贴
    public static final String CREATE_MERCHANT_PACKAGE = "CREATE_MERCHANT_PACKAGE";
    //修改白名单商户费率
    public static final String CHANGE_WHILTE_MERCHANT_RATE = "CHANGE_WHILTE_MERCHANT_RATE";

    // 插件调用地址
    public static final String YHC_PRODUCE = "YHC_PRODUCE";
    public static final String YHC_SOFT_MSG = "YHC_SOFT_MSG";
    public static final String BIND_THC_V5 = "BIND_THC_V5";
    // 登录校验IP，间隔等信息
    public static final String GET_IP_INFO = "GET_IP_INFO";
    public static final String LAST_LOGIN_INTERVAL_TIME = "LAST_LOGIN_INTERVAL_TIME";

    //查询国通报备接口
    public static final String SEARCH_GUOTONG_REPORT = "SEARCH_GUOTONG_REPORT";

    // 微信授权地址
    public static final String WECHAT_BIND_URL = "WECHAT_BIND_URL";
    public static final String WECHAT_VERIFY_URL = "WECHAT_VERIFY_URL";
    public static final String WECHAT_UNBING_URL = "WECHAT_UNBING_URL";
    public static final String GET_OPENID = "GET_OPENID";
    public static final String WECHAT_QR_CODE_TIME = "WECHAT_QR_CODE_TIME";

    public static final String SEARCH_WX_AL_ACCOUNT_STATUS = "SEARCH_WX_AL_ACCOUNT_STATUS";

    //查询商户提现
    public static final String HULU_GET_WITH_DRAW_HTML = "HULU_GET_WITH_DRAW_HTML";
    //开通关闭提现
    public static final String HULU_MER_MODIFY_WITH_DRAW = "HULU_MER_MODIFY_WITH_DRAW";
    //开通国通提现
    public static final String WITH_DRAWAL_APPLICATION = "WITH_DRAWAL_APPLICATION";

    //查询国通提现金额
    public static final String DRAWCASH_QUERY_LIMIT = "DRAWCASH_QUERY_LIMIT";
    //国通提现按钮
    public static final String DRAWCASH_WITHDRAW = "DRAWCASH_WITHDRAW";

    //提现记录列表
    public static final String WITHDRAWAL_RECORD = "WITHDRAWAL_RECORD";

    //开通d0
    public static final String GUOTONG_WITHDRAWAL_CONFIG = "GUOTONG_WITHDRAWAL_CONFIG";
    // 获取地区编码
    public static final String GET_AREA = "GET_AREA";
    //支付通道挂账查询
    public static final String REFUND_REIMBURSEMENT_TOUP = "REFUND_REIMBURSEMENT_TOUP";

    //支付通道补付
    public static final String  PAYMENT_CHANNEL_TOPUP= "PAYMENT_CHANNEL_TOPUP";
    //查询银联状态
    public static final String SEARCH_YLSW_STATUS = "SEARCH_YLSW_STATUS";

    //图片获取对应域名
    public static final String MERCHANT_INAGE_DOMAIN = "MERCHANT_INAGE_DOMAIN";

    public static final String UPDATE_MERCHANT_SHORT_NUM = "UPDATE_MERCHANT_SHORT_NUM";



    //定时任务批量删除费率包
    public static final String BATCH_DELETE_MERCHANT_RATE_PACKAGE_SEND = "BATCH_DELETE_MERCHANT_RATE_PACKAGE";

    //删除费率包
    public static final String DELETE_MERCHANT_RATE_PACKAGE = "DELETE_MERCHANT_RATE_PACKAGE";

    //银联商务确认开通
    public static final String CON_FIRMIN_YLSW = "CON_FIRMIN_YLSW";
    //银联商务申请开通
    public static final String SIGN_IN_YLSW = "SIGN_IN_YLSW";

    //查询国通提现金额
    public static final String SHOP_AUTH_CALL_BACK = "SHOP_AUTH_CALL_BACK";

    //随行付进件
    public static final String SIGN_IN_SXF_TQ  = "SIGN_IN_SXF_TQ";

    //葫芦进件
    public static final String SIGN_HULU = "SIGN_HULU";

    //瑞银信进件
    public static final String SIGN_RYX = "SIGN_RYX";

    //微信小程序配置黑名单
    public static final String WX_QRCODE_BLACK_LIST = "WX_QRCODE_BLACK_LIST";

    // 停止补贴+恢复费率接口
    public static final String STOP_RATE_PACKAGE_RECOVER_RATE = "STOP_RATE_PACKAGE_RECOVER_RATE";

    //申请国通微信活动
    public static final String GUOTONG_APPLY_WEIXIN_ACT = "GUOTONG_APPLY_WEIXIN_ACT";

    //查询国通微信活动状态
    public static final String GUOTONG_QUERY_WEIXIN_ACT = "GUOTONG_QUERY_WEIXIN_ACT";

    //商户开通邮政数币
    public static final String MERCHANT_SIGN_YZ_PAY = "MERCHANT_SIGN_YZ_PAY";

    //申请开通易生通道
    public static final String YI_SHENG_CAPPLY = "YI_SHENG_CAPPLY";

    //易生三方通道发送签约验证码
    public static final String YI_SHENG_SEND_CODE = "YI_SHENG_SEND_CODE";

    //易生三方通道签约
    public static final String YI_SHENG_SIGN_ELETRONIC = "YI_SHENG_SIGN_ELETRONIC";

    //报备结果查询
    public static final String YI_SHENG_GREPORT_SEARCH = "YI_SHENG_GREPORT_SEARCH";

    //校验并上传商户图片
    public static final String ID_CARD_DISTINGISH = "ID_CARD_DISTINGISH";

    //修改身份或删除团队成员强制app下线
    public static final String FORCE_APP_OFFLINE = "FORCE_APP_OFFLINE";

    //
    public static final String SEARCH_SHOP_SETAPPID = "SEARCH_SHOP_SETAPPID";
}
