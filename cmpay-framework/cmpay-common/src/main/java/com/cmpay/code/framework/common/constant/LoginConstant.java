package com.cmpay.code.framework.common.constant;

/**
 * @Title: LoginConstant
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.constant
 * @Date 2025/4/11 16:34
 * @description: 登录常量
 */
public class LoginConstant {
    public static final String VERIFY_LOGIN_AUTH_REDIS_KEY = "auth";
    public static final String VERIFY_AUTH_ID_REDIS_KEY = "authId";
    public static final String VERIFY_LOGIN_ID_REDIS_KEY = "loginId";
    public static final String VERIFY_LOGIN_EXIST_PHONE_REDIS_KEY = "existPhone";
    public static final String VERIFY_LOGIN_EXIST_WECHAT_AUTH_REDIS_KEY = "existWeChatAuth";
    public static final String VERIFY_LOGIN_LOGIN_HISTORY_REDIS_KEY = "loginHistory";
    public static final String VERIFY_LOGIN_LOGIN_SECURITY_REDIS_KEY = "loginSecurity";
    public static final String VERIFY_LOGIN_PHONE_REDIS_KEY = "phone";
    public static final String VERIFY_LOGIN_USERNAME_REDIS_KEY = "username";
    public static final String VERIFY_PASS_REDIS_KEY = "pass";
    public static final String TYPE_REDIS_KEY = "type";
    public static final String VERIFY_LOGIN_EXIST_PHONE_REDIS_VALUE_NOT_EXIST = "0";
    public static final String VERIFY_LOGIN_EXIST_PHONE_REDIS_VALUE_EXIST = "1";


    public static final String LOGIN_SEND_CODE_PHONE_KEY = "phone";
    public static final String LOGIN_SEND_CODE_ID_KEY = "codeId";
    public static final String LOGIN_SEND_CODE_CODE_KEY = "code";

    public static final String WECHAT_NICKNAME = "nickname";
    public static final String WECHAT_HEADIMGURL = "headimgurl";
    public static final String WECHAT_OPENID = "openid";
    public static final String TYPE_LOGIN = "login";
    public static final String TYPE_PROFILE = "profile";


    // 微信授权状态
    public static final String EXPIRE = "0";
    public static final String NOT_USE = "1";
    public static final String SCAN_CODE = "2";
    public static final String PASS = "3";


}
