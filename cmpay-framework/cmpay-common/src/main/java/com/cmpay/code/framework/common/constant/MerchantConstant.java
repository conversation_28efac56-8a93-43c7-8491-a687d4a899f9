package com.cmpay.code.framework.common.constant;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/11/21 14:32
 * version 1.0
 * 商户常量
 */
public class MerchantConstant {
    // 商户状态 正常
    public static final Integer MERCHANT_NORMAL = 1;
    // 商户状态 驳回
    public static final Integer MERCHANT_REJECT = 4;
    // 商户状态  复审驳回
    public static final Integer MERCHANT_TO_BE_SIGNED = 6;
    // 商户状态 待签约
    public static final Integer MERCHANT_REEXAMINATION_REJECTION = 9;
    // 商户状态 关闭支付权限
    public static final Integer MERCHANT_CLOSE_PAYMENT_PERMISSION = 44;
    // 商户状态 注销
    public static final Integer MERCHANT_LOG_OFF = 99;


}

