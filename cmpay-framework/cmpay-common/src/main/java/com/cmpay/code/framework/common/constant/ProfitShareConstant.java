package com.cmpay.code.framework.common.constant;


import java.util.HashMap;
import java.util.Map;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/12/8 11:48
 * version 1.0
 * 分账常量
 */
public class ProfitShareConstant {

    // 已开通分账
    public static final Integer MERCHANT_EXTEND_IS_PROFIT_SHARE_SUCCESS = 1;
    // 未开通分账
    public static final Integer MERCHANT_EXTEND_IS_PROFIT_SHARE_UNSIGNED = 0;


    // 商户分账oss根目录
    public static final String PROFIT_SHARE_OSS_ROOT_PATH = "profit_share";


    /**
     * 开通分账回调地址
     */
    public static final String APPLY_PROFIT_SHARE_CALLBACK_URL = "https://newmanage.gdwxyf.com/openness-api/openness-api/profitShare/applyCallback";

    // 开通状态   0未开通
    public static final Integer OPEN_STATUS_NOT_OPEN = 0;
    // 开通状态   1开通中
    public static final Integer OPEN_STATUS_OPENING = 1;
    // 开通状态   2开通成功
    public static final Integer OPEN_STATUS_SUCCESS = 2;
    // 开通状态   3开通失败
    public static final Integer OPEN_STATUS_FAIL = 3;
    // 开通状态   4开通取消
    public static final Integer OPEN_STATUS_CANCEL = 4;

    // 签约状态   0未签约
    public static final Integer SIGN_STATUS_NOT_SIGN = 0;
    // 签约状态   1待签约
    public static final Integer SIGN_STATUS_WAIT = 1;
    // 签约状态   2审核中
    public static final Integer SIGN_STATUS_SIGNING = 2;
    // 签约状态   3签约成功
    public static final Integer SIGN_STATUS_SUCCESS = 3;
    // 签约状态   4签约失败
    public static final Integer SIGN_STATUS_FAIL = 4;

    // 申请来源  0系统内部调用
    public static final Integer SOURCE_SYSTEM = 0;
    // 申请来源  1api调用
    public static final Integer SOURCE_API = 1;

    // 商户分账设置状态   1开启；
    public static final Integer PROFIT_SHARE_MERCHANT_PS_STATUS_OPEN = 1;
    // 商户分账设置状态   0关闭；
    public static final Integer PROFIT_SHARE_MERCHANT_PS_STATUS_CLOSE = 0;

    // 商户分账设置上游状态  已删除
    public static final Integer PROFIT_SHARE_UPSTREAM_SET_DELETE = 1;
    // 商户分账设置上游状态  正常
    public static final Integer PROFIT_SHARE_UPSTREAM_SET_NORMAL = 0;

    // 商户分账类型  0商户模式
    public static final Integer PROFIT_SHARE_MERCHANT_MERCHANT = 0;
    // 商户分账类型  1分账模式
    public static final Integer PROFIT_SHARE_MERCHANT_SHORTKEY = 1;

    // 商户分账删除状态  0正常
    public static final Integer PROFIT_SHARE_MERCHANT_NORMAL = 0;
    // 商户分账删除状态  1删除
    public static final Integer PROFIT_SHARE_MERCHANT_DELETE = 1;


    // 申请状态，随行付状态和平台状态互相转换
    public static final Map<String, Integer> APPLY_STATUS = new HashMap<>();
    // 申请状态对应名称
    public static final Map<Integer, String> OPEN_STATUS_NAME = new HashMap<>();
    // 签约状态，随行付状态和平台状态相互转换
    public static final Map<String, Integer> SIGN_STATUS = new HashMap<>();
    // 签约状态对应名称
    public static final Map<Integer, String> SIGN_STATUS_NAME = new HashMap<>();


    static {
        APPLY_STATUS.put("00", 1);
        APPLY_STATUS.put("01", 2);
        APPLY_STATUS.put("02", 3);
        APPLY_STATUS.put("03", 4);
        OPEN_STATUS_NAME.put(0, "未开通");
        OPEN_STATUS_NAME.put(1, "审核中");
        OPEN_STATUS_NAME.put(2, "开通成功");
        OPEN_STATUS_NAME.put(3, "开通失败");
        OPEN_STATUS_NAME.put(4, "开通取消");
        SIGN_STATUS.put("00", 1);
        SIGN_STATUS.put("03", 1);
        SIGN_STATUS.put("04", 2);
        SIGN_STATUS.put("02", 3);
        SIGN_STATUS.put("01", 4);
        SIGN_STATUS_NAME.put(0, "未签约");
        SIGN_STATUS_NAME.put(1, "待签约");
        SIGN_STATUS_NAME.put(2, "审核中");
        SIGN_STATUS_NAME.put(3, "签约成功");
        SIGN_STATUS_NAME.put(4, "签约失败");


    }
}
