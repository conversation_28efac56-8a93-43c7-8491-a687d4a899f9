package com.cmpay.code.framework.common.constant;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2024/11/6 14:24
 * @version: 1.0
 */
public class ResultConstant {

    public static final String SUCCESS = "成功";
    public static final String FAILURE = "失败";
    public static final String EXECUTE_EXCEPTION = "执行中异常";
    public static final String SHOP_NAME_ADDRESS_PARTNER_ID_IS_NULL = "商户名称、地址、经纬度、归属市不能为空";
    public static final String SHOP_NAME_ADDRESS_EXIST = "该商户地址和名称已经存在";
    public static final String ADD_SUCCESS = "添加成功";

    public static final String ADDRESS_MUST_NUMBER = "详细地址必须包含阿拉伯数字门牌号";

    public static final String DEVICE_ID_OR_BRAND_MSG_ID_IS_NULL = "设备SN号或品牌Id为空";
    public static final String SEARCH_DEVICE_IS_NULL = "未查询到设备信息";
    public static final String DELETE_DEVICE_SUCCESS = "删除设备成功";
    public static final String SN_EXIST = "SN号已存在，请勿重复导入";
}
