package com.cmpay.code.framework.common.constant;

public class RyxConstant {
	//快银
	/*private static final String RYX_ORG_ID = "OX0000000001619";
	private static final String RYX_ACCESS_ID = "000000000000003";
	private static final String RYX_PRIVATE_KEY_PATH = "/p12/ryx/ruiyinxin/ryx_private_key.pem";
	private static final String RYX_PUBLIC_KEY_PATH = "/p12/ryx/ruiyinxin/ryxsmzf_public_key_2048.pem";
	private static final String RYX_COOPERATOR = "ROX000000001619";
	private static final String RYX_WX_CHANNEL_ID = "352308271";
	private static final String RYX_ALIPAY_CHANNEL_ID = "2088621165211580";
	private static final String RYX_SWEEP_APPID = "wxfc95b9aba32569ff";
	private static final String RYX_SWEEP_APPID_KEY = "fbea6f4a26ceb9107d1d125640adb2f2";
	private static final String RYX_SIGNIN_PRIVATE_KEY_PATH = "/p12/ryx/ruiyinxin/ryx_signin_private.keystore";
	private static final String RYX_SIGNIN_PUBLIC_KEY_PATH = "/p12/ryx/ruiyinxin/ryx_signin_public.keystore";*/
	//君之聪明
	private static final String RYX_ORG_ID = "OX0000000001927";
	private static final String RYX_ACCESS_ID = "000000000000011";
	private static final String RYX_PRIVATE_KEY_PATH = "/p12/ryx/ruiyinxin/ryx_private_key.pem";
	private static final String RYX_PUBLIC_KEY_PATH = "/p12/ryx/ruiyinxin/ryxsmzf_public_key_2048.pem";
	private static final String RYX_COOPERATOR = "ROX000000001927";
	private static final String RYX_WX_CHANNEL_ID = "384759969";
	private static final String RYX_ALIPAY_CHANNEL_ID = "2088621165211580";
	/*private static final String RYX_SWEEP_APPI = "wx4e063469740d5289";
	private static final String RYX_SWEEP_APPID_KE = "326ae0ea74990bec149e86f84cf57fb4";*/
	private static final String RYX_SIGNIN_PRIVATE_KEY_PATH = "/p12/ryx/ruiyinxin/ryx_signin_private.keystore";
	private static final String RYX_SIGNIN_PUBLIC_KEY_PATH = "/p12/ryx/jzcm/ryx_signin_public.keystore";
	
	private static final String RYX_YOUZHENG_WX_CHANNEL_ID = "351719095";
	//测试
	/*private static final String RYX_ORG_ID = "O00000000001046";
	private static final String RYX_ACCESS_ID = "00003";
	private static final String RYX_PRIVATE_KEY_PATH = "D:\\ryx\\ryxss_pkcs8_rsa_private_key_2048.pem";
	private static final String RYX_PUBLIC_KEY_PATH = "D:\\ryx\\ryxsmzf_rsa_public_key_2048.pem";
	private static final String RYX_COOPERATOR = "RO00000000001046";
	private static final String RYX_WX_CHANNEL_ID = "352308271";
	private static final String RYX_ALIPAY_CHANNEL_ID = "2088621165211580";
	private static final String RYX_SWEEP_APPID = "wxfc95b9aba32569ff";
	private static final String RYX_SWEEP_APPID_KEY = "fbea6f4a26ceb9107d1d125640adb2f2";
	private static final String RYX_SIGNIN_PRIVATE_KEY_PATH = "D:\\ryx\\privateKey.keystore";
	private static final String RYX_SIGNIN_PUBLIC_KEY_PATH = "D:\\ryx\\publicKey.keystore";*/
												//https://rjp.ruiyinxin.com||http://************:7080
	private static final String RYX_SIGNIN_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/simpleApply";
	private static final String RYX_SEARCH_ALI_LEVEL_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/queryZfbLevel";
	private static final String RYX_RISK_MANAGE_URL = "https://rjp.ruiyinxin.com/nms/ims/PosBlack/queryPosBlackList";
	private static final String RYX_RISK_MANAGE_QUERY_URL = "https://rjp.ruiyinxin.com/nms/ims/PosBlack/queryPicType";
	private static final String RYX_RISK_MANAGE_SUBMIT_URL = "https://rjp.ruiyinxin.com/nms/ims/PosBlack/submitPosBlack";
	private static final String RYX_RISK_MANAGE_SUBMIT_QUERY_URL = "https://rjp.ruiyinxin.com/nms/ims/PosBlack/queryPosBlackInfo";
	private static final String RYX_ADD_MERCHDOCS_URL = "https://rjp-doc.ruiyinxin.com:7099/merchdoc/ims/merchdoc/addMerchDocs";

	private static final String RYX_SEARCH_MERCHANT_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/getById";
	private static final String RYX_SEARCH_PIC_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/getMerchDocs";
	/*private static final String RYX_SEARCH_MERCHANT_URL = "http://119.254.80.46:7080/ims/merch/getInfoById";*/
	private static final String RYX_UPDATE_CARD_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/updateSettleCard";
	private static final String RYX_UPDATE_RATE_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/updateRate";
	private static final String RYX_MER_AMT_APPLY_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/amtApply";
	private static final String RYX_SET_SUBSIDY_FLAG_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/subsideFlag";
	private static final String RYX_CANCLE_CHANGE_MER_URL = "https://rjp.ruiyinxin.com/nms/ims/merch/cancelChange";
	
	private static final String RYX_SEARCH_PAIDOUT_REFUND_URL = "https://rjp.ruiyinxin.com/nms/ims/tranSt/queryRefund";
	private static final String RYX_SUBSIDY_IMAGES_URL = "https://rjp-doc.ruiyinxin.com:7099/merchdoc/ims/merchdoc/addMerchDocs";
	private static final String RYX_SEARCH_PAIDOUT_URL = "https://rjp.ruiyinxin.com/nms/ims/tranSt/tranStList";
	private static final String RYX_SEARCH_PAIDOUT_TODAY_URL = "https://rjp.ruiyinxin.com/nms/ims/tranSt/tranStTodayList";
	private static final String RYX_SET_APP_PATH_URL = "https://qr.ruiyinxin.com/ydzf/wechat/gateway";
	/*private static final String RYX_NOTIFY_UR = "http://pay.congmingpay.com/pay/ryxpaysuccess.do";
	private static final String RYX_SIGNIN_URL = "http://119.254.80.46:7080/nms/ims/merch/simpleApply";
	private static final String RYX_SEARCH_PAIDOUT_URL = "https://rjp.ruiyinxin.com/nms/ims/tranSt/tranStList";
	private static final String RYX_SET_APP_PATH_URL = "https://qr.ruiyinxin.com/ydzf/wechat/gateway";
	private static final String RYX_NOTIFY_URL = "http://pay.congmingpay.com/pay/ryxpaysuccess.do";*/
	private static final String SUPPLIERID = "00000013";
	private static final String OUTBRHID = "WB1101099";
	private static final String MERCHADDR_PIC = "3.2.6";
	private static final String YZ_MER_VER = "1005";
	private static final String YZ_OUTBRHID = "WB1101015";//WB1101904
	//私钥加密
	private static final String YZ_KEY = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCndUll5o5CNa4ijfawJgCyRJDaYkob+lcHnoW2iFQk0ougNwjpLT8nWaAbqQbSN5MKjz3cTbOMsZ7VCgE13x/AdIpWIgZSg4QrlKzFiOx5EdTbQNlAPOaDR94aF4u1j3TlZlOr8Sc4e7v+9TvXc1UHupykJpjpKnM1/cl5G3Ptja/fzbNNCGCQyxdngk56xsLCuYB6R3ReCpliNOWedWlqWL4K4zlILnSUFSlC2ipobYyU/b58GsdRb1ZuC90fFYoM2iyF7sfDS+PT+kw+XuBqKJCYvIogjgL0nLmLSU+IWqCEgNOgt5w9pAhJTxH2Tk2CVEsMz57bR8r5m53TcsIvAgMBAAECggEAIpBT2QOQg1v/GETDETKW3DTIzTqSX1n5kqoGoVlcPMl0ZlpVrYNYUK/wGyV1Mfikkf5k307ua3DR/tj0zQDD1f53+zVEps1cf35Nduw/GvIyRBsLfHXVgm1YC3ymqo/0qdDIHmpFFSR89i/57QtyFolPAZax4xaO9yrt5eBjujk5zX4zl/dULT5IBzxD4YVAXS1Kb7kqhPGTuD5abNzoT7c6u3Rvo3OdVh2aYkqQ0zK2gn9/ZonYJsR7iozgx9QYYU+1NmddEss/o9vyWqy8JBaCAgMnCWips9MxxTeuQlgOv+fO9fuNLT1ycgmntETQzo5QcdHuBT+GSViHNKLjgQKBgQDee6kxWl5zbso44Vvbp3euS/R3blfzARNhUa5aevIx/O6W0hDXa/eMPHxhVOlcIm2hEGiOT2IJBlcGAzDrWvy6A2ctWXjVfYL5Qr79CLML/X3y224ui2SdfLrR7aAvQDB1ZBxHlZTsYO8DSrNzFR5OLBOJpF/Hw6k1tV3D7jltwQKBgQDAr4VwvebrUvkRI5BQPAAILGaH2DiDiZbb07LpBalPOTCmdiLP7kh+NxqAz5Xxr9GYytBP6VMfEJXgXHbX3PKssi8Qi3kKgz9cP5i4QE4xGqbUT0W+8qN2E0dLUxF4W+DyB7kIAcbb+wNpzKmhTbG6RawZRlOlEd/1j1vf954L7wKBgHsRNHoUXwLDWiE0p7dNX6qOYWBfLU9CXRIMOMHcGHUGUIQlFyPSmVB9dN/yNFO+x528kdmGl9geJa0xvrGx1vKeR0iSoYCWuQhgn30gfso3IqBgOQaOHM3w3Paa12zLuQJ2qh3+5C4NQKk1fSY/nE/pmbUds82wsbz/4iorcKEBAoGAVg6OVHocJkHah3MQwyTpBr191XIWW3kZ7XgxUa17nqx2NAJTLfbIXcaSibEs0NKrk8gRHX+h5h/sMFZbpbFCmSrDpmoEV1Oi+rpUpQXjZj72/5+gENYhd1zI3LVu+ghfAPm+A/t3K0yCi8Aiiq4+kGkJoggs126STgQHDpJy/fUCgYBb7+KZGW8j1XCKUfT5j+UuXmJ6s1TTaAgvQRiWefPBL1P/p1Hc22VcVHiK0AyYmjMCsqC/JTDAZNO3Lz6My9Rsq2LKwRBG+fXcc+PCMjxLGzx9z4oSEAS0ODhM74M0AoqYM8NcWjDhO+8qJNEuDVrQWyrmZPiieV9BU4Hqn+IU8g==";
	//公钥解密
	private static final String YZ_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzfsfaGmy6HlvJpYFMW/7CAiFY2/OPiY0/321nCVXHtKuJMe/ySFdg1Nyfkg9+kTPuSWUfHu1yGt2XzcfR642Nngf3XdPOGmzraPW823mJA8i+lHxhUUcFXY1TCMgykVu/5ts5jvO03KCAX4Ccmp7+6PkxJKs495yv+vMbEGouzzRQvzpzZnJYLddexmCRjvM7Q9KN4928267MjVPPyifRTGzODg8/+Be45NtIua2Wj13JreoEQtN3EBYfeVdbFbJ517IUUVYCj6LxWU25ZASYlGjuRIQ2oKOG862T98NGIpSJdabQ/v0jPMdaximPl8MUwZBwLt8j0ViQl6NXgJOlQIDAQAB";
	private static final String YZ_PIC_URL = "https://rjp.ruiyinxin.com/ycnms/docForm";//  https://************:7097/ycnms/docForm
	private static final String YZ_MER_URL = "https://rjp.ruiyinxin.com/ycnms/ycmerch";//  https://************:7097/ycnms/ycmerch
	private static final String YZ_MER_ENCRYPT_URL = "https://rjp.ruiyinxin.com/ycnms/ycmerch/encrypt";//https://************:7097/ycnms/ycmerch/encrypt
	//private static final String YZ_REVOKE_URL = "https://rjp.ruiyinxin.com/ycnms/ycmerch";//https://************:7097/ycnms/ycmerch
	//yz私钥加密
	private static final String TRAN_RYX_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCdrJ1s99ASkRzVBS3zXHYbm5X7NU9yx6fVJl90zpc0Y05kyMyBtPLjuvbLi/m7Fs52fu99HhOhqlFfE4sCJsblEpg+FuyZuDrxopubm0UAyMqu/VKE/n3HenUf0E3KbgkT7ufbBL0iQBgs1AuAztWibThPAu1sHz0ZDYyxr3dcIQIDAQAB|MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL8rMsvIE2LuJMQ7kbfAUryblyczOpdtb8P5ESBO2cGtVirvAu0EqnLRNGFcAgzKdeERR//TZC5MKjkzVOr4sL94ug69/2qV/57ZVZ5AW/inIXPSChNjvRgDczEfftQLf/Evrrqk/BeJNWpOBzsbVYimCAjNHaWNnd8kLogTm+ldAgMBAAECgYEAmBKfN+iTM3HKIahZhNkjeBI1reNcyrUt4bGyZAcVxrJDB6hWpYG+rdOqXYC5yL8OAtqiceoRmZrRQ+uHs9vdjTIdNWIMlfv1ceKxpVMwdnq985R7PokvUo5Fo132Qn5lKWm8TLwiIqTtNSCZT9Jhz2bRr+y1Po/j8rTT5h2SbykCQQDwvlIOv6Ivs632hrk6HnPneA2QFHed5IZADGDMQV4guqmfdb1XAV/ci/1w0rwcgpUqFKFdqRx/Ix7CEiN+GUNXAkEAy0ibdtumOGqcuv8sfxO4/95Ieo2Ks7yt8PgqAFMS9YGp+UIKv0m7cMFgxovUrw0U7k8oFENOm2z/13yX4YLcawJAfWBw9dNhkPw/pjtFrfn8HNlOHvI51ZYVQLfxlY6ZiewMuMolwHErzn1tkVt+sk5jhtXVsCSJfcD5nNK7f0mC4wJAVNmiopL+J5BAiUEXdpdp4csLX7QS0AESbYF4Lph+UmFj7DmnuL5y8ZaV6WnWRZblRK9foE2T0wZFJ9HGp4dyrwJAGq9+HSLhkH/GEoE7G8p4x/ZaxA6ljXr3x4dE4USKIdaN0I3QMFlE7Ejs6Bn+GDm2sZDlO56bnm5yh/tUGMhOww==";
	private static final String SIGN_RYX_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCWe5v8Uf3popWzOoxy7bmpIgn+oz+R5afhl6xTlcM7hK/wrwlDzUp38KWtBK2XJWAOVKEduOd/nxOB311ilHVTV7vbedoOr51+HkzMriDwvHrKZmilFOVvHgcRZbyWcvXQJ9YdcsrcqrtjOtuaxsBFmfS9WXvzsl37u3ogslapowIDAQAB|MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALpm7T+f0yqpvPnbKq5fg890LbGNFCPpyqLy/QpuPo0QDxyzUYtZaXfOHgfmUG9Vu+sHST+fKkYMAlN0sCoONb6lcJhRfeV0CLO763Xlpg5NfEa9gZL5sfGJk+2oVsjM++r9iFXa/vFYq5zCl1foebN1B9hYuWfMa5VWTTx23u0RAgMBAAECgYEAgKw8agcyBelWAkRAmk5k9vgD8uSoWoIw13tVIbYoh8fmJrHCQKdgHsux/1IFhFVyezN0ZO7Ch8gScvnCAcX9Yod9lrnB8kfLsduWV2Fe9iOi1HS5AAt6REomiWRohzvlRCh8wTLtZY318t/CsnRiqHBx27DIBQ5u7MuDc0RVPIECQQDka9WY0tAQJQ01eBbqEXHbskraV/ePCLAmca5RAG9C7sRahtcZBwjy4YrjUwpHDHFAexcss4Mif4jwtzh4OrTNAkEA0OhXZAKcvJWBdBHvE+ZTXVJV7mdfi3f1Q5CYR4b+ehdKgMbcg1K3TmkNW65+TPhMvEzyOrb0hFNhfXEVG5x5VQJAQLhRD/kDXN+yq2i4TV/d+9q7nLv597wFdQ8Wzfqqi7NtTR0RixTUM0U2fgyER/c9i4CU42SFjlvGaDXxuRchLQJAfQGKyZ2GjJIp1HqmxRGd8MnEAdR5p8swSC3N8X8l54joXAajDTIB3jmedK4FhalOwZ6Jk/jGeCgsvhNcvhjzwQJBANZr28UkpKxnQEYmjiyqLcsGZgClp4OrPNYRnF45adw7NXRacxXvwkXY5DhnbauFXfhOLvhihKMjQXa6u0tX3XU=";

	//测试环境
	/*private static final String TRAN_RYX_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/KzLLyBNi7iTEO5G3wFK8m5cnMzqXbW/D+REgTtnBrVYq7wLtBKpy0TRhXAIMynXhEUf/02QuTCo5M1Tq+LC/eLoOvf9qlf+e2VWeQFv4pyFz0goTY70YA3MxH37UC3/xL666pPwXiTVqTgc7G1WIpggIzR2ljZ3fJC6IE5vpXQIDAQAB|MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJ2snWz30BKRHNUFLfNcdhublfs1T3LHp9UmX3TOlzRjTmTIzIG08uO69suL+bsWznZ+730eE6GqUV8TiwImxuUSmD4W7Jm4OvGim5ubRQDIyq79UoT+fcd6dR/QTcpuCRPu59sEvSJAGCzUC4DO1aJtOE8C7WwfPRkNjLGvd1whAgMBAAECgYB8YbYSRu9SJj07Ygmcp/zXzIWlFKnm3s9r2wiESZCIGCq4YJrLW2Yb9dpIOdS4HMakPYBJEBfmeUgVm4IWIWbWAc/otd77IN+iL/B9X0u9bgfdHZBdG58LBQhMNzstBUsXbR19+N2NkHHgu5nZ6TKepJ3rv9hMuu+m5Tcmml9tEQJBANzIUOeRcHj47sjj6XtI8V/MOBXw2JIQALuvUk9f8vm5Zw4daD8f5zKHfqb1psJfut+fFx0duOWW4IS/TUa8RqUCQQC200Qu5alKN5y3zgB48SrwICK1EIVmImhen4eOxGoDfDuByYHPAAVNRunWVn/HOQOE9braQjiZYSAGICveF4LNAkAtZDnOAkbfeD6Pqtv77tlBWtlFGYlNLmEYNUl07EMmOIoJit497C86YkUVyneIiun+w9SatdunyEdMw4/9tQPJAkAGfR0QZQpLHgor493UbQrPFvTkZD+3zkTSButyv7nbgc9C3foDdMa7ZWbrOoqycW2UjHwjPvJvTykpzn6AFWmJAkEA3MVPi/sBblp22G6o7I6/Q3Mfg+dMO6rivhp+T7l9eaPw6F4Tbj6bSBZoVpUPySGQuvf3XZEdPIWrtdBa8oe6Uw==";
	private static final String SIGN_RYX_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC6Zu0/n9Mqqbz52yquX4PPdC2xjRQj6cqi8v0Kbj6NEA8cs1GLWWl3zh4H5lBvVbvrB0k/nypGDAJTdLAqDjW+pXCYUX3ldAizu+t15aYOTXxGvYGS+bHxiZPtqFbIzPvq/YhV2v7xWKucwpdX6HmzdQfYWLlnzGuVVk08dt7tEQIDAQAB|MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJZ7m/xR/emilbM6jHLtuakiCf6jP5Hlp+GXrFOVwzuEr/CvCUPNSnfwpa0ErZclYA5UoR2453+fE4HfXWKUdVNXu9t52g6vnX4eTMyuIPC8espmaKUU5W8eBxFlvJZy9dAn1h1yytyqu2M625rGwEWZ9L1Ze/OyXfu7eiCyVqmjAgMBAAECgYEAi+JBwQ6SwrEBCYd+cYNsl71tnwgvmrbqFTnB1j0cK+KPDmQXb7CQ7eeEis4Tc7IplYiz8IYmpCrCnnJ9Q5sPZ8mIEvw3OM63MDsszTNX18zgm9s6yZVdzyAbbsAdhGLXfQfRHAfp51ab+0q6ukgTkAPxkzeg19bhtNvWqmZ69UECQQDOMW3Dt/cA/jrnGlWrMHqv+nNd/eEoxwj85VxRVkN3P3hMLYNRpYIlVmY4GP28BjbRWVZT9dMRoUkRWM9qnDuhAkEAutUrltWnsdlyKSDOSjDlgQV6bapclQPAPsNSucqhw48t+DdGSa4lQ9Bt/cn5wDgItZkh8PzaeVKdibqw9nx+wwJBAJm2D3XQNrruy0ptP2AXC98Ufdyb3rZfajvdYCziefjzv+JKK5M6JwNenWVEfCMnHsaCKvD8WRTTf30HD9kSDwECQCM2ikCjhDfBBAM8a6UQEjF/FtQUh96OFm294pmh11tEYSWnRVgdumhNoG10492Wsg2IDx2cipDTQV30EeZjEIECQERt7pPlSI7htBwbM7JG3YW7Yo1RxxlZYSm7LAeeMiUXB++Zgro/1er/G7Jd1gluzc9wBeRgpAIAVaHgmnRhuYI=";
	*/
	
	public static String getYzMerEncryptUrl() {
		return YZ_MER_ENCRYPT_URL;
	}

	public static String getRyxCancleChangeMerUrl() {
		return RYX_CANCLE_CHANGE_MER_URL;
	}

	public static String getTranRyxKey() {
		return TRAN_RYX_KEY;
	}

	public static String getSignRyxKey() {
		return SIGN_RYX_KEY;
	}

	public static String getYzPublicKey() {
		return YZ_PUBLIC_KEY;
	}

	public static String getYzKey() {
		return YZ_KEY;
	}

	public static String getYzOutbrhid() {
		return YZ_OUTBRHID;
	}

	public static String getYzMerVer() {
		return YZ_MER_VER;
	}

	public static String getYzPicUrl() {
		return YZ_PIC_URL;
	}

	public static String getYzMerUrl() {
		return YZ_MER_URL;
	}

	public static String getMerchaddrPic() {
		return MERCHADDR_PIC;
	}

	public static String getSupplierid() {
		return SUPPLIERID;
	}

	public static String getOutbrhid() {
		return OUTBRHID;
	}

	public static String getRyxUpdateCardUrl() {
		return RYX_UPDATE_CARD_URL;
	}

	public static String getRyxMerAmtApplyUrl() {
		return RYX_MER_AMT_APPLY_URL;
	}

	public static String getRyxRiskManageQueryUrl() {
		return RYX_RISK_MANAGE_QUERY_URL;
	}

	public static String getRyxAddMerchdocsUrl() {
		return RYX_ADD_MERCHDOCS_URL;
	}

	public static String getRyxRiskManageSubmitQueryUrl() {
		return RYX_RISK_MANAGE_SUBMIT_QUERY_URL;
	}

	public static String getRyxRiskManageSubmitUrl() {
		return RYX_RISK_MANAGE_SUBMIT_URL;
	}

	public static String getRyxRiskManageUrl() {
		return RYX_RISK_MANAGE_URL;
	}

	public static String getRyxUpdateRateUrl() {
		return RYX_UPDATE_RATE_URL;
	}
	public static String getRyxSearchAliLevelUrl() {
		return RYX_SEARCH_ALI_LEVEL_URL;
	}
	public static String getRyxSearchPicUrl() {
		return RYX_SEARCH_PIC_URL;
	}
	public static String getRyxSearchMerchantUrl() {
		return RYX_SEARCH_MERCHANT_URL;
	}
	public static String getRyxOrgId() {
		return RYX_ORG_ID;
	}
	public static String getRyxPrivateKeyPath() {
		return RYX_PRIVATE_KEY_PATH;
	}
	public static String getRyxPublicKeyPath() {
		return RYX_PUBLIC_KEY_PATH;
	}
	public static String getRyxCooperator() {
		return RYX_COOPERATOR;
	}
	/*public static String getRyxSweepAppi() {
		return RYX_SWEEP_APPID;
	}
	public static String getRyxSweepAppidKe() {
		return RYX_SWEEP_APPID_KEY;
	}*/
	public static String getRyxWxChannelId() {
		return RYX_WX_CHANNEL_ID;
	}
	public static String getRyxAccessId() {
		return RYX_ACCESS_ID;
	}
	public static String getRyxSigninUrl() {
		return RYX_SIGNIN_URL;
	}
	public static String getRyxSigninPrivateKeyPath() {
		return RYX_SIGNIN_PRIVATE_KEY_PATH;
	}
	public static String getRyxSigninPublicKeyPath() {
		return RYX_SIGNIN_PUBLIC_KEY_PATH;
	}
	public static String getRyxSetAppPathUrl() {
		return RYX_SET_APP_PATH_URL;
	}
	/*public static String getRyxNotifyUr() {
		return RYX_NOTIFY_URL;
	}*/
	public static String getRyxSearchPaidoutUrl() {
		return RYX_SEARCH_PAIDOUT_URL;
	}
	public static String getRyxAlipayChannelId() {
		return RYX_ALIPAY_CHANNEL_ID;
	}
	public static String getRyxSearchPaidoutTodayUrl() {
		return RYX_SEARCH_PAIDOUT_TODAY_URL;
	}
	public static String getRyxSubsidyImagesUrl() {
		return RYX_SUBSIDY_IMAGES_URL;
	}

	public static String getRyxSearchPaidoutRefundUrl() {
		return RYX_SEARCH_PAIDOUT_REFUND_URL;
	}

	public static String getRyxSetSubsidyFlagUrl() {
		return RYX_SET_SUBSIDY_FLAG_URL;
	}

	public static String getRyxYouzhengWxChannelId() {
		return RYX_YOUZHENG_WX_CHANNEL_ID;
	}
	

}
