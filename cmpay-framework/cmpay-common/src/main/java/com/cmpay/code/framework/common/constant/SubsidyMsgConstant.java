package com.cmpay.code.framework.common.constant;

/**
 * @Title: SubsidyMsgConstant
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.constant
 * @Date 2024/6/14 9:36
 * @description: 补充资料常量信息
 */
public class SubsidyMsgConstant {
    // 审核状态：0：待补充资料，1：审核中，2：审核通过，3：退回，4：资料保存不提交，5：待补充资料未读，6：上游审核中，7：防伪审核中，8：退回待补充，9：关闭
    public static final Integer AUDIT_To_BE_SUPPLEMENTED = 0;
    public static final Integer AUDIT_IN_REVIEW = 1;
    public static final Integer AUDIT_APPROVED = 2;
    public static final Integer AUDIT_REJECT = 3;
    public static final Integer AUDIT_DATA_RETENTION = 4;
    public static final Integer AUDIT_ADDITIONAL_INFO_TO_BE_ADD_UNREAD = 5;
    public static final Integer AUDIT_UPSTREAM_REVIEW_IN_PROGRESS = 6;
    public static final Integer AUDIT_ANTI_COUNTERFEITING_REVIEW_IN_PROGRESS = 7;
    public static final Integer AUDIT_RETURNED_FOR_SUPPLEMENTATION = 8;
    public static final Integer AUDIT_CLOSE = 9;
    public static final Integer COMPLETELY_CLOSE = 10;
    // 补充资料类型；E：基础资料补充；A：法人身份证补充；；F：结算人身份证补充；B：营业执照补充；D：门头、收银台、内景照补充；K：银行卡补充；M：非法人授权书补充；X：协议; Q：商户标签资料;R:经营名称及地址
    public static final String TYPE_BASIC_DATA = "E";
    public static final String TYPE_CORPORATE_ID_CARD = "A";
    public static final String TYPE_SETTLER_ID_CARD = "F";
    public static final String TYPE_BUSINESS_LICENSE = "B";
    public static final String TYPE_PLACE_OF_BUSINESS = "D";
    public static final String TYPE_BANK_CARD = "K";
    public static final String TYPE_NON_LEGAL_PERSON_AUTHORIZATION_LETTER = "M";
    public static final String TYPE_AGREEMENT = "X";
    public static final String TYPE_MERCHANT_TAGS = "Q";
    public static final String TYPE_BUSINESS_NAME_ADDRESS = "R";
    // 提交平台；0未知；1邮付小助手；2邮付管家；
    public static final Integer PLATFORM_UNKNOWN = 0;
    public static final Integer PLATFORM_MERCHANT_MINIAPP = 1;
    public static final Integer PLATFORM_MERCHANT_MANAGE = 2;

}
