package com.cmpay.code.framework.common.constant;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/12/8 10:40
 * version 1.0
 * 随行付常量
 */
public class SxfConstant {
    // 随行付响应成功状态码
    public static final String SUCCESS_CODE = "0000";
    // 随行付响应成功状态码
    public static final String SUCCESS_BIZCODE = "0000";

    // 平台响应成功状态码 成功
    public static final Integer SUCCESS = 0;
    // 平台响应成功状态码 失败
    public static final Integer FAIL = -1;

    // 分账情况说明函 随行付图片代码
    public static final Integer PROFIT_SHARE_COVER_LETTER_IMAGE_CODE = 86;
    // 分账情况说明函图片名称前缀
    public static final String PROFIT_SHARE_COVER_LETTER_IMAGE_PREFIX = "ProfitShareCoverLetterImage";
    // 分账场景 随行付图片代码
    public static final Integer PROFIT_SHARE_SCENE_IMAGE_CODE = 87;
    // 分账场景图片名称前缀
    public static final String PROFIT_SHARE_SCENE_IMAGE_PREFIX = "ProfitShareSceneImage";
    // 分账申请其他附件	 随行付图片代码
    public static final Integer PROFIT_SHARE_OTHER_IMAGE_CODE = 88;
    // 分账申请其他附件	图片名称前缀
    public static final String PROFIT_SHARE_OTHER_IMAGE_PREFIX = "ProfitShareOtherImage";

    // -----------------------------------------------------商户分账请求地址------------------------------------------------------
    // 随行付上传图片请求地址
    public static final String SXF_MERCHANT_GET_FILE_RELATIVE_URL = "https://openapi.tianquetech.com/merchant/uploadPicture";
    // 随行付商户特殊申请提交请求地址
    public static final String SXF_MERCHANT_SPECIAL_APPLICATION_COMMIT_APPLY = "https://openapi.tianquetech.com/merchant/specialApplication/commitApply";
    // 随行付申请签约请求地址
    public static final String SXF_MERCHANT_SIGN_APPLY = "https://openapi.tianquetech.com/merchant/sign/getUrl";
    // 随行付查询签约结果请求地址
    public static final String SXF_MERCHANT_QUERY_SIGN_URL = "https://openapi.tianquetech.com/merchant/sign/querySignContract";
    // 随行付设置分账发起方和接收方
    public static final String SXF_MERCHANT_QUERY_LEDGER_SETMNOARRAY = "https://openapi.tianquetech.com/query/ledger/setMnoArray";
}
