package com.cmpay.code.framework.common.constant;

public class WxServiceConstant {
	/*private static final String WX_PAY_KE = "junzhicongmingCONGMINGFU20160226";*/
	private static final String WX_PAY_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";
	private static final String WX_MICROPAY_URL = "https://api.mch.weixin.qq.com/pay/micropay";
	private static final String WX_MICROPAY_QUERY_URL = "https://api.mch.weixin.qq.com/pay/orderquery";
/*	private static final String NOTIFY_UR = "http://pay.congmingpay.com/pay/paysuccess.do";
	private static final String SCANCODE_UR = "http://pay.congmingpay.com/pay/scancode.do";*/
	private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
	private static final String TEMPLATE_SEND_URL = "https://api.weixin.qq.com/cgi-bin/message/template/send";
	private static final String WX_REFUND_URL = "https://api.mch.weixin.qq.com/secapi/pay/refund";
	private static final String WX_GZH_ORDERS_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx4e063469740d5289&redirect_uri=http://pay.congmingpay.com/activity/activity/rotate/redpacket.jsp?json={shopId:013dedea37ba38a5a2bc5bcbce5e2d2e,rotateId:merchant_redpacket_01}&response_type=code&scope=snsapi_base#wechat_redirect";
	private static final String WX_GZH_TOKEN = "junzhicongmingCONGMINGFU20160323";
	/*private static final String CMP_ACCESS_TOKEN_UR = "http://internal.congmingpay.com/internal/getaccesstoken.do";*/
	private static final String WX_MCH_MERCHANT_URL = "https://api.mch.weixin.qq.com/secapi/mch/submchmanage";
	private static final String LONG_TO_SHORT_URL = "https://api.weixin.qq.com/cgi-bin/shorturl";
	private static final String WX_CREATE_CARD_URL = "https://api.weixin.qq.com/card/create";
	private static final String WX_UPLOAD_IMG_URL = "https://api.mch.weixin.qq.com/secapi/mch/uploadmedia";
	private static final String WX_UPLOAD_IMG_NEW_URL = "https://api.mch.weixin.qq.com/v3/merchant/media/upload";
	private static final String WX_MICRO_MERCHANT_SIGN_URL = "https://api.mch.weixin.qq.com/applyment/micro/submit";
	private static final String WX_MICRO_MERCHANT_UPDATE_URL = "https://api.mch.weixin.qq.com/applyment/micro/submitupgrade";
	private static final String WX_MICRO_MERCHANT_SEARCH_URL = "https://api.mch.weixin.qq.com/applyment/micro/getstate";
	private static final String WX_MICRO_MERCHANT_UPDATE_SEARCH_URL = "https://api.mch.weixin.qq.com/applyment/micro/getupgradestate";
	private static final String WX_SEARCH_MERCHANT_CONFIG_URL = "https://api.mch.weixin.qq.com/secapi/mch/querysubdevconfig";
	private static final String WX_SUBSCRIBE_MERCHANT_CONFIG_URL = "https://api.mch.weixin.qq.com/secapi/mkt/addrecommendconf";
	private static final String WX_PATH_MERCHANT_CONFIG_URL = "https://api.mch.weixin.qq.com/secapi/mch/addsubdevconfig";
	private static final String WX_APPID_MERCHANT_CONFIG_URL = "https://api.mch.weixin.qq.com/secapi/mch/addsubdevconfig";
	private static final String SUBSCRIBE_SEND_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send";
	/*private static final String APP_ID = "wx4e063469740d5289";*/
	/*private static final String MCH_ID = "1292624101";
	private static final String SPBILL_CREATE_IP = "*************";
	private static final String APP_SECRET = "326ae0ea74990bec149e86f84cf57fb4";*/
	private static final Double CONGMINGPAY_FEILV = 0.006;
	private static final String MERCHANT_TEMPLATE_ID = "WpH5aukxC-xOOtQwKfHPz4oPZdYfa9conJiox4VHe0Q";
	private static final String Merchant_RICK_TEMPLATE_ID = "R26UwFQPT6XpeTKdQMB8EKyAx72cW4Lv7E56dLrPpDM";
	private static final String MERCHANT_PAIDOUT_FAIL_TEMPLATE_ID = "x-V3KSqQ7NHhlI5nfBnBPZ7nJUALwuDtCuWGmlJVDJ8";

	private static final String SUB_APP_ID = "wx60048116cf130c24";
	private static final String SUB_MCH_ID = "1275228101";
	private static final String SUB_WX_PAYMENT_KEY = "junzhicongmingCONGMINGFU20151021";
	private static final String WX_PAYMENT_URL = "https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers";
	private static final String WX_PAYMENT_SEARCH_URL = "https://api.mch.weixin.qq.com/mmpaymkttransfers/gettransferinfo";
	// 吃喝指南的appid
	private static final String YD_APP_ID = "wx665cb5c08d33c2b9";
	private static final String YD_APP_SECRET = "28b8a0883e45a3b2598714e4dbccc375";

	private static final String SEND_REDPACKET_URL = "https://api.mch.weixin.qq.com/mmpaymkttransfers/sendredpack";

	private static final String AUTHCODE_OPENID_URL = "https://api.mch.weixin.qq.com/tools/authcodetoopenid";
	private static final String CONSUME_CARD_CODE = "https://api.weixin.qq.com/card/code/consume";
	private static final String GET_IMG_URL = "http://file.api.weixin.qq.com/cgi-bin/media/get";
	private static final String SEND_MESSAGE_KF_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send";
	
	private static final String WX_SEARCH_MERCHANT_RISK_URL = "https://api.mch.weixin.qq.com/mchrisk/querymchrisk";
	
	private static final String WX_SEARCH_PUBLIC_KEY_URL = "https://fraud.mch.weixin.qq.com/risk/getpublickey";

	// RSA秘钥信息
	/*private static final String WX_CERT_SERIAL_NO = "52FDFDCE211C0BA67F17E39E2801134EF65B2E3B";
	private static final String MINI_PROGRAMS_APP_I = "wxdd8dd7adbce09421";*/
	private static final String MINI_PROGRAMS_PATH = "pages/index/index";
	/*public static String getMiniProgramsAppI() {
		return MINI_PROGRAMS_APP_ID;
	}*/
	public static String getMiniProgramsPath() {
		return MINI_PROGRAMS_PATH;
	}
	public static String getSubscribeSendUrl() {
		return SUBSCRIBE_SEND_URL;
	}
	private static int KEY_NUMBER = 500;
	public static String getWxUploadImgNewUrl() {
		return WX_UPLOAD_IMG_NEW_URL;
	}
/*	public static String getWX_PAY_KE() {
		return WX_PAY_KEY;
	}*/

	public static String getWX_PAY_URL() {
		return WX_PAY_URL;
	}

	public static String getWX_MICROPAY_URL() {
		return WX_MICROPAY_URL;
	}

	public static String getWX_MICROPAY_QUERY_URL() {
		return WX_MICROPAY_QUERY_URL;
	}

	/*public static String getNOTIFY_UR() {
		return NOTIFY_URL;
	}

	public static String getSCANCODE_UR() {
		return SCANCODE_URL;
	}*/

	public static String getACCESS_TOKEN_URL() {
		return ACCESS_TOKEN_URL;
	}

	public static String getTEMPLATE_SEND_URL() {
		return TEMPLATE_SEND_URL;
	}

	/*public static String getAPP_ID() {
		return APP_ID;
	}*/

/*	public static String getMCH_ID() {
		return MCH_ID;
	}

	public static String getSPBILL_CREATE_IP() {
		return SPBILL_CREATE_IP;
	}

	public static String getAPP_SECRET() {
		return APP_SECRET;
	}*/

	public static Double getCONGMINGPAY_FEILV() {
		return CONGMINGPAY_FEILV;
	}

	public static String getMERCHANT_TEMPLATE_ID() {
		return MERCHANT_TEMPLATE_ID;
	}

	public static int getKEY_NUMBER() {
		return KEY_NUMBER;
	}

	public static void setKEY_NUMBER(int kEY_NUMBER) {
		KEY_NUMBER = kEY_NUMBER;
	}

	public static String getWX_REFUND_URL() {
		return WX_REFUND_URL;
	}

	public static String getWX_GZH_ORDERS_URL() {
		return WX_GZH_ORDERS_URL;
	}

	public static String getWX_GZH_TOKEN() {
		return WX_GZH_TOKEN;
	}

	public static String getSUB_APP_ID() {
		return SUB_APP_ID;
	}

	public static String getSUB_MCH_ID() {
		return SUB_MCH_ID;
	}

	public static String getSUB_WX_PAYMENT_KEY() {
		return SUB_WX_PAYMENT_KEY;
	}

	public static String getWX_PAYMENT_URL() {
		return WX_PAYMENT_URL;
	}

	public static String getSEND_REDPACKET_URL() {
		return SEND_REDPACKET_URL;
	}

	public static String getAUTHCODE_OPENID_URL() {
		return AUTHCODE_OPENID_URL;
	}

	public static String getConsumeCardCode() {
		return CONSUME_CARD_CODE;
	}

	public static String getGetImgUrl() {
		return GET_IMG_URL;
	}

	public static String getSendMessageKfUrl() {
		return SEND_MESSAGE_KF_URL;
	}

/*	public static String getCmpAccessTokenUr() {
		return CMP_ACCESS_TOKEN_URL;
	}*/

	public static String getYdAppId() {
		return YD_APP_ID;
	}

	public static String getYdAppSecret() {
		return YD_APP_SECRET;
	}

	public static String getWxPaymentSearchUrl() {
		return WX_PAYMENT_SEARCH_URL;
	}

	public static String getWxMchMerchantUrl() {
		return WX_MCH_MERCHANT_URL;
	}

	public static String getLongToShortUrl() {
		return LONG_TO_SHORT_URL;
	}

	public static String getWxCreateCardUrl() {
		return WX_CREATE_CARD_URL;
	}

	public static String getWxUploadImgUrl() {
		return WX_UPLOAD_IMG_URL;
	}

	public static String getWxMicroMerchantSignUrl() {
		return WX_MICRO_MERCHANT_SIGN_URL;
	}

	public static String getWxMicroMerchantSearchUrl() {
		return WX_MICRO_MERCHANT_SEARCH_URL;
	}

	public static String getWxSearchMerchantConfigUrl() {
		return WX_SEARCH_MERCHANT_CONFIG_URL;
	}

	public static String getWxSubscribeMerchantConfigUrl() {
		return WX_SUBSCRIBE_MERCHANT_CONFIG_URL;
	}

	public static String getWxPathMerchantConfigUrl() {
		return WX_PATH_MERCHANT_CONFIG_URL;
	}

	public static String getWxAppidMerchantConfigUrl() {
		return WX_APPID_MERCHANT_CONFIG_URL;
	}

	public static String getWxMicroMerchantUpdateSearchUrl() {
		return WX_MICRO_MERCHANT_UPDATE_SEARCH_URL;
	}

	public static String getWxMicroMerchantUpdateUrl() {
		return WX_MICRO_MERCHANT_UPDATE_URL;
	}
	public static String getMerchantRickTemplateId() {
		return Merchant_RICK_TEMPLATE_ID;
	}
	public static String getMerchantPaidoutFailTemplateId() {
		return MERCHANT_PAIDOUT_FAIL_TEMPLATE_ID;
	}
	public static String getWxSearchMerchantRiskUrl() {
		return WX_SEARCH_MERCHANT_RISK_URL;
	}
	public static String getWxSearchPublicKeyUrl() {
		return WX_SEARCH_PUBLIC_KEY_URL;
	}
	

}
