package com.cmpay.code.framework.common.enums;


import lombok.Getter;

import static com.cmpay.code.framework.common.constant.BrandMsgConstant.TYKP_ID;


/**
 * @Title: EquipmentEnum
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.enums
 * @Date 2024/7/10 10:57
 * @description: 设备枚举
 */
@Getter
public enum EquipmentEnum {
    TYKP(TYKP_ID, "云喇叭-天谕云宽屏", "TYKPImpl");


    /**
     * 通过类型获取枚举
     */
    public static EquipmentEnum getEquipmentEnum(Long id) {
        EquipmentEnum[] equipmentEnums = EquipmentEnum.values();
        for (EquipmentEnum equipmentEnum : equipmentEnums) {
            if (equipmentEnum.getId().equals(id)) {
                return equipmentEnum;
            }
        }
        return null;
    }

    private final Long id;
    private final String description;
    private final String beanName;

    EquipmentEnum(Long id, String description, String beanName) {
        this.id = id;
        this.description = description;
        this.beanName = beanName;
    }
}
