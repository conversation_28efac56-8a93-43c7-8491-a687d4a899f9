package com.cmpay.code.framework.common.enums;


import com.cmpay.code.framework.common.util.file.FileSizeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Inherited
@Target({FIELD})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {FileSizeValidator.class})
public @interface FileSize {

    String message() default "文件过大";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
