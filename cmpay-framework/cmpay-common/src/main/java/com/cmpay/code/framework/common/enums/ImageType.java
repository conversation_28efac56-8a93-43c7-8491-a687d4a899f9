package com.cmpay.code.framework.common.enums;


import com.cmpay.code.framework.common.util.file.ImageTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Inherited
@Target({FIELD})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {ImageTypeValidator.class})
public @interface ImageType {

    String message() default "文件类型错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
