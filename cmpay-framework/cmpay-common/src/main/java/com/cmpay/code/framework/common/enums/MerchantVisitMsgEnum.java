package com.cmpay.code.framework.common.enums;

import com.cmpay.code.framework.common.constant.MerchantVisitMsgConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2024/8/5 16:37
 * @version: 1.0
 */
@Getter
public enum MerchantVisitMsgEnum {

    /**
     * 走访类型；E：基础资料补充（地址+坐标+简称）；A：法人身份证补充；；F：结算人身份证补充；B：营业执照补充；
     * D：门头、收银台、内景照补充；K：银行卡补充；
     * M：非法人授权书补充；Q：商户标签资料;R:经营名称及地址（地址+坐标+经营名称）；T:标识（正常，非正常，原因）；P：打卡（照片+备注）；
     */

    TYPE_A(MerchantVisitMsgConstant.TYPE_A, "法人身份证补充", "merchantVisitMsgA"),
    TYPE_B(MerchantVisitMsgConstant.TYPE_B, "营业执照补充", "merchantVisitMsgB"),
    TYPE_D(MerchantVisitMsgConstant.TYPE_D, "门头、收银台、内景照补充", "merchantVisitMsgD"),
    TYPE_E(MerchantVisitMsgConstant.TYPE_E, "基础资料补充", "merchantVisitMsgE"),
    TYPE_F(MerchantVisitMsgConstant.TYPE_F, "结算人身份证补充", "merchantVisitMsgF"),
    TYPE_K(MerchantVisitMsgConstant.TYPE_K, "银行卡补充", "merchantVisitMsgK"),
    TYPE_M(MerchantVisitMsgConstant.TYPE_M, "非法人授权书补充", "merchantVisitMsgM"),
    TYPE_P(MerchantVisitMsgConstant.TYPE_P, "打卡", "merchantVisitMsgP"),
    TYPE_Q(MerchantVisitMsgConstant.TYPE_Q, "商户标签资料", "merchantVisitMsgQ"),
    TYPE_R(MerchantVisitMsgConstant.TYPE_R, "经营名称及地址", "merchantVisitMsgR"),
    TYPE_T(MerchantVisitMsgConstant.TYPE_T, "标识", "merchantVisitMsgT");


    private final String type;
    private final String description;
    private final String beanName;

    MerchantVisitMsgEnum(String type, String description, String beanName) {
        this.type = type;
        this.description = description;
        this.beanName = beanName;
    }

    /**
     * 通过类型获取枚举
     */
    public static MerchantVisitMsgEnum getSubsidyEnumByType(String type) {
        MerchantVisitMsgEnum[] visitMsgEnums = MerchantVisitMsgEnum.values();
        for (MerchantVisitMsgEnum visitMsgEnum : visitMsgEnums) {
            if (visitMsgEnum.getType().equals(type)) {
                return visitMsgEnum;
            }
        }
        return null;
    }
}
