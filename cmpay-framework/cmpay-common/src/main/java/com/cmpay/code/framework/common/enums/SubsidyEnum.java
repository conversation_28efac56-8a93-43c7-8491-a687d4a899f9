package com.cmpay.code.framework.common.enums;

import com.cmpay.code.framework.common.constant.SubsidyMsgConstant;
import lombok.Getter;

/**
 * @Title: SubsidyEnum
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.enums
 * @Date 2024/6/14 15:13
 * @description: 补资料枚举类
 */
@Getter
public enum SubsidyEnum {
    TYPE_CORPORATE_ID_CARD(SubsidyMsgConstant.TYPE_CORPORATE_ID_CARD, "法人身份证补充", "ATypeSubsidy"),
    TYPE_BUSINESS_LICENSE(SubsidyMsgConstant.TYPE_BUSINESS_LICENSE, "营业执照补充", "BTypeSubsidy"),
    TYPE_PLACE_OF_BUSINESS(SubsidyMsgConstant.TYPE_PLACE_OF_BUSINESS, "门头、收银台、内景照补充", "DTypeSubsidy"),
    TYPE_BASIC_DATA(SubsidyMsgConstant.TYPE_BASIC_DATA, "基础资料补充", "ETypeSubsidy"),
    TYPE_SETTLER_ID_CARD(SubsidyMsgConstant.TYPE_SETTLER_ID_CARD, "结算人身份证补充", "FTypeSubsidy"),
    TYPE_BANK_CARD(SubsidyMsgConstant.TYPE_BANK_CARD, "银行卡补充", "KTypeSubsidy"),
    TYPE_NON_LEGAL_PERSON_AUTHORIZATION_LETTER(SubsidyMsgConstant.TYPE_NON_LEGAL_PERSON_AUTHORIZATION_LETTER, "非法人授权书补充", "MTypeSubsidy"),
    TYPE_MERCHANT_TAGS(SubsidyMsgConstant.TYPE_MERCHANT_TAGS, "商户标签资料", "QTypeSubsidy"),
    TYPE_BUSINESS_NAME_ADDRESS(SubsidyMsgConstant.TYPE_BUSINESS_NAME_ADDRESS, "经营名称及地址", "RTypeSubsidy"),
    TYPE_AGREEMENT(SubsidyMsgConstant.TYPE_AGREEMENT, "协议", "XTypeSubsidy");


    /**
     * 通过类型获取枚举
     */
    public static SubsidyEnum getSubsidyEnumByType(String type) {
        SubsidyEnum[] subsidyEnums = SubsidyEnum.values();
        for (SubsidyEnum subsidyEnum : subsidyEnums) {
            if (subsidyEnum.getType().equals(type)) {
                return subsidyEnum;
            }
        }
        return null;
    }

    private final String type;
    private final String description;
    private final String beanName;

    SubsidyEnum(String type, String description, String beanName) {
        this.type = type;
        this.description = description;
        this.beanName = beanName;
    }
}
