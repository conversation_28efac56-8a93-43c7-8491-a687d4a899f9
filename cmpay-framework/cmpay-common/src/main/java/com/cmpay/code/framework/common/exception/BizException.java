package com.cmpay.code.framework.common.exception;

import com.cmpay.code.framework.common.exception.enums.ServiceErrorCodeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Title: BizException
 * <AUTHOR>
 * @Package com.cmpay.code.framework.common.exception
 * @Date 2025/4/10 14:14
 * @description: 业务异常
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class BizException extends RuntimeException {

    /**
     * 业务错误码
     *
     * @see ServiceErrorCodeRange
     */
    private Integer code;
    /**
     * 错误提示
     */
    private String message;

    /**
     * 业务数据
     */
    private Object data;

    /**
     * 空构造方法，避免反序列化问题
     */
    public BizException() {
    }

    public BizException(ErrorCode errorCode, Object data) {
        super(errorCode.getMsg());
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
        this.data = data;
    }


}
