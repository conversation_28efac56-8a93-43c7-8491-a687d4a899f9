package com.cmpay.code.framework.common.exception;

/**
 * <AUTHOR>
 * @description: 自定义返回码
 * @date 2023-04-14 9:30:02
 * @version: 1.0
 */
public class GlobalErrorCodePc {

    ErrorCode SUCCESS = new ErrorCode(0, "成功");
    public final static ErrorCode FAIL = new ErrorCode(-1, "失败");

    // ========== 自定义错误段 ==========
    // 门店已存在
    public final static ErrorCode DEVICES_EXIST = new ErrorCode(108, "门店名称已存在");
    // 收音设备已存在
    public final static ErrorCode WSY_DEVICES_EXIST = new ErrorCode(209, "收银设备id已经绑定其他设备，请勿重复绑定");

    // 云喇叭已存在
    public final static ErrorCode AUDIO_HORN_EXIST = new ErrorCode(210, "云喇叭id已经绑定其他设备，请勿重复绑定");

    // 收音设备已存在
    public final static ErrorCode YLB_ID_INVALID = new ErrorCode(211, "云喇叭ID无效，请检查云喇叭ID是否正确或联系运营处理");

    // 云喇叭已存在
    public final static ErrorCode PRINTER_EXIST = new ErrorCode(212, "打印机id已经绑定其他商户设备，请勿重复绑定");

    // 收音设备不存在
    public final static ErrorCode YLB_ID_NOT_EXIST = new ErrorCode(290, "设备未入库,请检查云喇叭ID是否正确或联系运营处理");

    public final static ErrorCode IS_NOT_IF_USE = new ErrorCode(291, "设备未投产");

    public final static ErrorCode BRAND_IS_NOT_EXIST = new ErrorCode(292, "品牌不正确");

    public final static ErrorCode DEVICE_IS_NOT_IN_STOCK = new ErrorCode(294, "设备未入库");

    // 云喇叭sn号为空
    public final static ErrorCode YLB_SN_NOT_NULL = new ErrorCode(213, "设备SN号不能为空");
    // 商户号为空
    public final static ErrorCode SHOP_ID_NOT_NULL = new ErrorCode(214, "商户号不能为空");


    // 商户pid不存在
    public final static ErrorCode SHOP_PID_NOT_EXIST = new ErrorCode(215, "未查询到商户PID");
    // 支付宝盒绑定失败
    public final static ErrorCode ALI_YLB_FAIL = new ErrorCode(216, "绑定支付宝盒喇叭失败");
    public final static ErrorCode ALIPAY_BOX_REMOVE_BINDING_ERROR = new ErrorCode(234, "支付宝盒解绑失败，请联系运营处理");

    // isBoos不能为空
    public final static ErrorCode IS_BOOS_NOT_NULL = new ErrorCode(217, "isBoos不能为空");

    // xmid不能为空
    public final static ErrorCode XMID_NOT_NULL = new ErrorCode(218, "xmid不能为空");

    // 交易开始、截止时间不能为空
    public final static ErrorCode START_AND_END_TIME_NOT_NULL = new ErrorCode(219, "交易开始、截止时间不能为空");

    // orderId不能为空
    public final static ErrorCode ORDER_ID_NOT_NULL = new ErrorCode(218, "单号不能为空");

    // orderId不能为空
    public final static ErrorCode PASSWORD_NOT_NULL = new ErrorCode(219, "密码不能为空");

    public final static ErrorCode PASSWORD_ERROR = new ErrorCode(220, "密码错误");

    public final static ErrorCode EMPLOYEE_ID_NOT_NULL = new ErrorCode(221, "员工编号已存在");

    public final static ErrorCode EMPLOYEE_NULL = new ErrorCode(222, "员工不存在");

    public final static ErrorCode PRICE_NOT_NULL = new ErrorCode(222, "价格不能为空");

    public final static ErrorCode PROJECT_NAME_NOT_NULL = new ErrorCode(223, "送奶项目名不能为空");

    public final static ErrorCode PROJECT_NOT_NULL = new ErrorCode(224, "该送奶项目不存在");

    public final static ErrorCode SCHOOL_ID_NOT_NULL = new ErrorCode(225, "学校id不能为空");

    public final static ErrorCode SCHOOL_NULL = new ErrorCode(226, "该学校不存在");

    public final static ErrorCode TEMPLE_NULL = new ErrorCode(227, "未找到该行善项目");

    public final static ErrorCode DEVICE_NOT_NULL = new ErrorCode(228, "未选择分店");

    public final static ErrorCode SHORT_NOT_NULL = new ErrorCode(229, "未选择设备");

    public final static ErrorCode RESET_EMPLOYEE = new ErrorCode(230, "该人员未认证");

    public final static ErrorCode NO_FIND_DATA = new ErrorCode(231, "未查询到该数据");

    public final static ErrorCode ID_NOT_NULL = new ErrorCode(232, "id不能为空");
    public final static ErrorCode MEALPROGREM_ERROR = new ErrorCode(233, "套餐已存在");
    public final static ErrorCode TEMPLE_ERROR = new ErrorCode(233, "该项目已存在");

    public final static ErrorCode ISBOSS_NOT_NULL = new ErrorCode(234, "身份不能为空");
    public final static ErrorCode DEVICE_INCLUDE_SPECIAL_CHARACTER = new ErrorCode(233, "分店名称包含特殊字符");
    public final static ErrorCode MERCHANT_KEEPER_PHONE_ERROR = new ErrorCode(234, "负责人手机号不允许修改");

    public final static ErrorCode DATA_BAST = new ErrorCode(905, "获取数据失败"); // 获取数据失败

    // 参数异常
    public final static ErrorCode PARAM_ERROR = new ErrorCode(906, "参数异常");

    // 未查询到用户
    public final static ErrorCode NO_FIND_USER = new ErrorCode(907, "未查询到该用户");
    public final static ErrorCode CASHIER_NULL = new ErrorCode(908, "未查询到收银员");


    //--------------------------------修改密码------------------------------------------
    // 修改密码确认密码不正确
    public final static ErrorCode CONFIRM_NOT_SAME = new ErrorCode(1001, "两次密码不一致，请确认");

    public final static ErrorCode PASSWORD_INVALID = new ErrorCode(1004, "密码无效");

    public final static ErrorCode PASSWORD_IS_WEAK = new ErrorCode(1005, "密码强度弱，请重新设置");

    // 修改密码不能跟原密码一致
    public final static ErrorCode NEW_OLD_PASSWORD_NOT_SAME = new ErrorCode(1001, "新密码不能和原密码一致");

    // 验证码不存在
    public final static ErrorCode CODE_IS_NOT = new ErrorCode(1002, "验证码已过期，请重新发送");

    // 验证码不一致
    public final static ErrorCode CODE_NOT_SAME = new ErrorCode(1002, "验证码不正确，请重新输入");
    public final static ErrorCode OLD_CODE_NOT_SAME = new ErrorCode(1002, "原手机号验证码不正确，请重新输入！");
    public final static ErrorCode NEW_CODE_NOT_SAME = new ErrorCode(1002, "新手机号验证码不正确，请重新输入！");

    // 手机号不存在
    public final static ErrorCode PHONE_NOT_EXIST = new ErrorCode(1003, "手机号不存在");
    public final static ErrorCode PHONE_EXIST = new ErrorCode(1004, "手机号存在");
    //验证码重复发送校验
    public final static ErrorCode CODE_IS_EXIST = new ErrorCode(1005, "验证码已发送，请勿频繁点击");


    //--------------商户---------------------
    // 原密码错误
    public final static ErrorCode PWD_NOT_EXIST = new ErrorCode(1101, "原密码错误");

    // 不允许修改
    public final static ErrorCode NOT_UPD = new ErrorCode(1102, "原密码不是身份证后六位，请到小程序修改");
    //该手机号不在团队
    public final static ErrorCode PHONE_NOT_EXIST_CASHIER = new ErrorCode(1103, "该手机号没有加入团队，请勿错误操作");
    //--------------登录---------------------
    // 用户不存在
    public final static ErrorCode USER_EXIST = new ErrorCode(1201, "用户不存在");
    public final static ErrorCode USER_EXCEPTION = new ErrorCode(1204, "该用户异常，请联系管理员");

    // 不允许修改
    public final static ErrorCode USER_NOT_CERTIFIED = new ErrorCode(1202, "用户未认证，请先进行认证");

    // 门店已存在
    public final static ErrorCode MERCHANT_EXIST = new ErrorCode(1203, "您已在团队内，请勿重复添加");

    //---------------修改角色---------------------------------------------------------
    // 门店已存在
    public final static ErrorCode NOT_UPD_ROLE = new ErrorCode(1301, "不允许重置超管权限");

    //--------------------------校园微信-----------------------------------------------
    public final static ErrorCode SHOP_ID_SUB_USER_ID_NOT_NULL = new ErrorCode(1401, "shopId和subUserId不能同时为空");

    //--------------------------------文件上传------------------------------------------
    public final static ErrorCode FILE_SIZE_ERROR = new ErrorCode(1501, "文件大小不能超过2M");

    public final static ErrorCode FILE_FORMAT_ERROR = new ErrorCode(1501, "仅支持png、jpg、jpeg格式的文件");

    public final static ErrorCode FILE_ERROR = new ErrorCode(1501, "文件上传失败");
    public final static ErrorCode FILE_NOT_NULL = new ErrorCode(1501, "文件不能为空");
    public final static ErrorCode FILE_TYPE_EXCEL = new ErrorCode(1501, "仅支持xls、xlsx格式的文件");

    //----------------------------------结算卡-------------------------------------------------
    public final static ErrorCode CHANNEL_NOT_EQUAL = new ErrorCode(1601, "支付通道不一致，请到渠道后台修改");
    public final static ErrorCode BANK_NAME_NOT_NULL = new ErrorCode(1602, "总行不能为空");
    public final static ErrorCode BANK_ADDRESS_NOT_NULL = new ErrorCode(1603, "开户行不能为空");
    public final static ErrorCode CARD_NAME_NOT_NULL = new ErrorCode(1604, "户主不能为空");
    public final static ErrorCode INDETITY_NOT_NULL = new ErrorCode(1605, "结算人身份证不能为空");
    public final static ErrorCode CARD_PHONE_NOT_NULL = new ErrorCode(1606, "预留手机号不能为空");

    //--------------------------------代理商/受理商系统自定义异常------------------------------------------
    public final static ErrorCode AGENT_ID_NOT_NULL = new ErrorCode(301, "代理商id不能为空");
    public final static ErrorCode OPENID_NOT_NULL = new ErrorCode(302, "openid不能为空");
    public final static ErrorCode APPID_NOT_NULL = new ErrorCode(303, "appid不能为空");
    public final static ErrorCode APPIDSE_NOT_NULL = new ErrorCode(303, "公众号开发者秘钥不能为空");
    public final static ErrorCode GET_TOKEN_ERROR = new ErrorCode(304, "获取token失败，请重试");
    public final static ErrorCode ACCOUNT_NOT_NULL = new ErrorCode(305, "账号不能为空");
    public final static ErrorCode CHANNEL_NOT_NULL = new ErrorCode(330, "通道不能为空");
    public final static ErrorCode TIME_NOT_NULL = new ErrorCode(331, "时间不能为空");
    public final static ErrorCode STATUS_NOT_NULL = new ErrorCode(332, "状态不能为空");
    public final static ErrorCode BATCH_UPDATE_MERCHANT_STATUS_MSG_NOT_NULL = new ErrorCode(332, "批量关停、开启商户交易原因不能为空");
    public final static ErrorCode ACCOUNT_TYPE_NOT_NULL = new ErrorCode(306, "权限参数不能为空");
    public final static ErrorCode IS_OPEN_NOT_NULL = new ErrorCode(307, "是否开启自动审核参数不能为空");
    public final static ErrorCode IS_OPEN_REPEAT = new ErrorCode(308, "机构下已有用户开通自动审核功能");
    public final static ErrorCode PLATFORM_NOT_NULL = new ErrorCode(309, "platform不能为空");
    public final static ErrorCode TIME_CONVERT_ERROR = new ErrorCode(310, "时间转换失败");

    public final static ErrorCode EXPORT_EXCEL_ERROR = new ErrorCode(311, "导出excel失败");
    public final static ErrorCode EXPORT_CSV_ERROR = new ErrorCode(311, "CSV文件生成失败");
    public final static ErrorCode ACCEPT_ID_NOT_NULL = new ErrorCode(314, "受理商id不能为空");
    public final static ErrorCode ACCEPT_NO = new ErrorCode(315, "未查询到该受理商");
    public final static ErrorCode POLICY_NO = new ErrorCode(316, "未查询到该政策");
    public final static ErrorCode POLICY_EXIST = new ErrorCode(317, "该政策已经存在，请勿重复添加");
    public final static ErrorCode POLICY_PACKAGE_RATE_EXIST = new ErrorCode(318, "该费率包已经存在，请勿重复添加");
    public final static ErrorCode POLICY_PID_EXIST = new ErrorCode(319, "该渠道号已经存在，请勿重复添加");
    public final static ErrorCode MERCHANT_EXTEND_NULL = new ErrorCode(320, "此商户拓展信息为空请勿错误操作");
    public final static ErrorCode PROFIT_SHARE_OPEN = new ErrorCode(321, "商户已开通分账请勿重复操作");
    public final static ErrorCode PROFIT_SHARE_MERCHANT_REPEAT = new ErrorCode(322, "请勿重复添加分账商户");
    public final static ErrorCode PROFIT_SHARE_MERCHANT_MAX_THREE = new ErrorCode(323, "分账最多设置3个分账商户");
    public final static ErrorCode PROFIT_SHARE_RATIO_MAX_HUNDRED = new ErrorCode(324, "此商户分账比例无法超过100%");
    public static final ErrorCode PLATFORM_NOT_SUPPORT = new ErrorCode(325, "平台不支持");
    public static final ErrorCode RATE_TYPE_ERROR = new ErrorCode(326, "费率类型错误");
    public static final ErrorCode POLICY_ID_NOT_NULL = new ErrorCode(326, "政策不能为空");
    public static final ErrorCode EXPORT_AND_SEARCH_TIME_MAX_MONTH = new ErrorCode(327, "查询/导出时间不能超过31天");
    public static final ErrorCode EXPORT_AND_SEARCH_TIME_MAX_WEEK = new ErrorCode(328, "查询/导出时间不能超过7天");
    public static final ErrorCode EXPORT_AND_SEARCH_TIME_MAX_TOW_MONTH = new ErrorCode(328, "查询/导出时间不能超过60天");

    public final static ErrorCode SMALL_SHOP_NOT_PROFIT_SHARE = new ErrorCode(329, "小微商户不支持设置分账");
    public final static ErrorCode THE_SHOP_NOT_PROFIT_SHARE = new ErrorCode(330, "复核不通过的商户不支持设置分账");

    public final static ErrorCode THE_SHOP_NOT_SET_RECEIVE_PROFIT_SHARE = new ErrorCode(331, "复核不通过的商户不支持设置分账");
    public final static ErrorCode THE_SHOP_NOT_RECEIVE_PROFIT_SHARE = new ErrorCode(332, "复核不通过的商户不支持接收分账");
    public final static ErrorCode NOT_FOUND_PROFIT_SHARE_MERCHANT = new ErrorCode(321, "未查询到分账商户");


    public final static ErrorCode CARD_NAME_NOT_SAME = new ErrorCode(333, "分账发起方与分账接收方不能为相同法人或相同结算人");
    public final static ErrorCode UPDATE_RATE_ERROR = new ErrorCode(334, "修改费率失败");
    public final static ErrorCode UPDATE_RATE_ERROR_NOT_AUDIT = new ErrorCode(334, "商户原通道有未审核的费率审批");

    //----------------------------------用户管理-------------------------------------------------
    public final static ErrorCode ADMIN_NO_UPDATE = new ErrorCode(621, "不能修改或改为内部人员，请联系管理员");
    public final static ErrorCode ORANGE_NOT_NULL = new ErrorCode(622, "您所在机构没有设置超管，请联系管理员");
    public final static ErrorCode DEFAULT_ADMIN_NO_UPDATE = new ErrorCode(623, "您所在机构没有设置超管，请联系管理员");
    public final static ErrorCode USER_ID_NOT_NULL = new ErrorCode(624, "用户id不能为空");
    public final static ErrorCode USER_ROLE_ERROR = new ErrorCode(625, "您的角色有误，请联系管理员");
    public final static ErrorCode USERNAME_ISEXIST = new ErrorCode(626, "手机号已经存在，请重新修改");

    public final static ErrorCode ORG_IS_EXIST = new ErrorCode(627, "该机构已存在，请勿重复添加");
    public final static ErrorCode APP_DROP_IS_SUCCESS = new ErrorCode(628, "APP强制下线失败");

    //----------------------------------应用管理-------------------------------------------------
    public final static ErrorCode BASIC_VALUE_NOT_NULL = new ErrorCode(801, "权限标识不能为空");
    public final static ErrorCode BASIC_NAME_NOT_NULL = new ErrorCode(802, "权限名称不能为空");
    public final static ErrorCode REMARK_NOT_NULL = new ErrorCode(803, "权限内容说明不能为空");
    public final static ErrorCode BASIC_EXIST = new ErrorCode(806, "当前权限已存在");
    public final static ErrorCode APPLICATION_EXIST = new ErrorCode(807, "当前应用已存在");
    public final static ErrorCode APPLICATION_NAME_NOT_NULL = new ErrorCode(808, "应用名称不能为空");
    public final static ErrorCode SHOP_BIND_INFOS_REPEAT = new ErrorCode(809, "商户已绑定该应用，请勿重复绑定");
    public final static ErrorCode SHOP_BIND_INFOS_EXIST = new ErrorCode(810, "商户未绑定该应用，请先绑定应用");
    public final static ErrorCode SIGN_ERROR = new ErrorCode(811, "sign异常，请联系技术人员！");

    //----------------------------------内部/省/市系统自定义异常-------------------------------------------------
    public final static ErrorCode PARTNER_ID_NOT_NULL = new ErrorCode(701, "市id不能为空");
    public final static ErrorCode PARTNER_NOT_EXIST = new ErrorCode(701, "市公司不存在");
    public final static ErrorCode BRANCH_ID_NOT_NULL = new ErrorCode(702, "省id不能为空");
    public final static ErrorCode ORDER_NON_PAYMENT = new ErrorCode(703, "订单未支付成功，请确认订单状态");
    public final static ErrorCode ORDER_NOT_INTERFACE_REQUEST = new ErrorCode(704, "订单非接口请求，无法回调");
    public final static ErrorCode LOCAL_ADDRESS_NO_CALLBACK = new ErrorCode(705, "本地地址无法回调");
    public final static ErrorCode PARTNER_PAY_CHANNEL_EXIST = new ErrorCode(706, "此大渠道的支付渠道已存在");
    public final static ErrorCode RISK_MERCHANT_EXIST = new ErrorCode(707, "投诉订单不存在");
    public final static ErrorCode YWY_OPENID_NOT_NULL = new ErrorCode(708, "业务员id不能为空");
    public final static ErrorCode PAIDOUT_FILE_EXIST = new ErrorCode(709, "结算信息不存在");
    public final static ErrorCode NOTIFY_ORDER_FAIL = new ErrorCode(710, "回调订单失败");
    public final static ErrorCode ADVERTISEMENT_AREA_EXIST = new ErrorCode(711, "该广告地区已存在");
    public final static ErrorCode QUASH_REAL_NAME_AUTHENTICATION_ERROR = new ErrorCode(740, "撤销实名认证失败");
    public final static ErrorCode COPY_MERCHANT_ERROR = new ErrorCode(741, "复制商户失败");
    public final static ErrorCode PROFIT_SHARING_ACTIVITIES_ERROR = new ErrorCode(742, "分润活动失败");
    public final static ErrorCode SEARCH_ACCOUNT_RISK_ERROR = new ErrorCode(743, "查询风险等级失败");
    public final static ErrorCode BATCH_HULU_MERCHANT_IMPORT_ERROR = new ErrorCode(744, "批量开通提现失败");
    public final static ErrorCode BATCH_DELETE_MERCHANT_IMPORT_ERROR = new ErrorCode(745, "批量注销商户失败");
    public final static ErrorCode READ_FILE_ERROR = new ErrorCode(746, "读取文件失败，请按照格式重新上传");
    public final static ErrorCode CHANNEL_NOT_EXIST = new ErrorCode(747, "当前通道不存在，请选择正确通道");
    public final static ErrorCode NOTIFICATION_NULL = new ErrorCode(748, "未查询到商户通知信息");
    public final static ErrorCode UPLOAD_SUBSIDY_INFO = new ErrorCode(749, "上传文件失败");
    public final static ErrorCode DEMAND_RELEASE_NOT_EXISTS = new ErrorCode(750, "需求不存在");
    public final static ErrorCode DEMAND_RELEASE_NOT_PERMISSION = new ErrorCode(751, "需求只允许本机构修改或删除");
    public final static ErrorCode DEMAND_BIND_SERVICE_EXISTS = new ErrorCode(752, "取消合作商后才能回到发布中");
    public final static ErrorCode DEMAND_RATE_ERROR = new ErrorCode(753, "费率区间最小值不能大于费率区间最大值");
    public final static ErrorCode DEMAND_RELEASE_COOPERATIVE_NOT_NULL = new ErrorCode(754, "请选择合作商");
    public final static ErrorCode POLICY_REVIEW_ERROR = new ErrorCode(755, "政策审核失败");
    public final static ErrorCode MERCHANT_VISIT_MSG_ID_NOT_NULL = new ErrorCode(756, "任务id为空不允许修改");
    public final static ErrorCode MERCHANT_VISIT_MSG_NOT_EXISTS = new ErrorCode(756, "未查询到商户信息");
    public final static ErrorCode UPDATE_MERCHANT_SENCE_ERROR = new ErrorCode(757, "修改商户支付场景失败");

    //-----------------------------------市级分公司-----------------------------------------------------------
    public final static ErrorCode PARTNER_IS_EXIST = new ErrorCode(712, "市级分公司已存在，请重新添加");
    public final static ErrorCode PARTNER_CREATE_ERROR = new ErrorCode(712, "市级分公司创建失败");
    public final static ErrorCode PARTNER_UPDATE_ERROR = new ErrorCode(712, "市级分公司修改失败");
    public final static ErrorCode PARTNER_USER_IS_EXIST = new ErrorCode(713, "该市级用户已存在");
    public final static ErrorCode RISK_MANAGE_EXIST = new ErrorCode(714, "风控订单不存在");
    public final static ErrorCode PHONE_IS_ERROR = new ErrorCode(715, "手机号格式不正确");
    public final static ErrorCode COMPLAINT_CONTEXT_ERROR = new ErrorCode(721, "回复文本长度超出限制200");
    public final static ErrorCode MERCHANT_CHANNEL_NOT_EXIST = new ErrorCode(722, "未找到关联的上游关联商户信息");
    public final static ErrorCode ORDER_NOT_TRANSACTION_ID_EXIST = new ErrorCode(723, "未找到上游订单号");
    public final static ErrorCode UNKNOWN_ORIGIN_EXIST = new ErrorCode(724, "未知的投诉来源");
    public final static ErrorCode MCC_CODE_NOT_NULL = new ErrorCode(725, "mccCode不能为空");
    public final static ErrorCode YZ_ORG_EXIST = new ErrorCode(726, "机构号错误");
    public final static ErrorCode EXPIRE_TIME_NOT_NULL = new ErrorCode(727, "过期时间不能为空");
    public final static ErrorCode ORDER_SEARCH_PARAM_ERROR = new ErrorCode(728, "请输入商户号或上游流水号或订单号或微信支付宝三方号");

    public final static ErrorCode SHOP_ID_IS_NOT_EXIXT_LIST = new ErrorCode(730, "该商户号不存在");
    public final static ErrorCode SUB_AGENT_NO_MERCHANT = new ErrorCode(731, "该大商户下没有商户");

    public final static ErrorCode SUB_AGENT_PHONE_IS_EXIXT = new ErrorCode(732, "手机号存在");
    //--------------------------------费率、费率包相关----------------------------------------
    public static final ErrorCode CHANGE_RATE_FAIL = new ErrorCode(1301, "修改费率失败");

    //---------------------------------商户相关----------------------------------------------
    public static final ErrorCode MERCHANT_NOT_EXIST = new ErrorCode(1201, "商户不存在");
    public static final ErrorCode MERCHANT_IMAGES_NOT_EXIST = new ErrorCode(1202, "商户图片不存在");
    public static final ErrorCode MERCHANT_IMAGES_INCOMPLETE = new ErrorCode(1202, "商户图片不完整");

    public static final ErrorCode IMAGES_NOT_EXIST = new ErrorCode(1202, "图片不存在");

    public static final ErrorCode MERCHANT_IMAGES_TOO_BIG = new ErrorCode(1202, "图片最大不能超过5M");
    public static final ErrorCode MERCHANT_IMAGES_TYPE_FIAL = new ErrorCode(1203, "仅支持PNG、JPG、JPEG类型图片");
    public static final ErrorCode MERCHANT_IMAGES_FILE_TYPE_FIAL = new ErrorCode(1204, "商户图片文件类型错误");
    public static final ErrorCode MERCHANT_IMAGES_IS_NULL = new ErrorCode(1202, "图片不能为空");
    public static final ErrorCode MERCHANT_SXF_CHANNEL_FIAL = new ErrorCode(1205, "商户随行付通道异常");
    public static final ErrorCode MERCHANT_NOT_RECEIVE = new ErrorCode(1206, "商户不是领取状态，不允许撤销操作");
    public static final ErrorCode MERCHANT_NOT_RECEIVE_YWY = new ErrorCode(1207, "改商户不是您领取，不允许撤销操作");
    public static final ErrorCode MERCHANT_NOT_RECEIVE_TARGET = new ErrorCode(1207, "目标商户，不允许撤销操作");
    public static final ErrorCode MERCHANT_GT_POS = new ErrorCode(1209, "商户已开通国通pos,不允许切换商户");
    public static final ErrorCode MERCHANT_SXF_POS = new ErrorCode(1210, "商户已开通随行付pos,不允许切换商户");
    public static final ErrorCode MERCHANT_ZJ = new ErrorCode(1211, "商户为转介商户,不允许切换商户");

    //---------------------------------------------分账相关-----------------------------------------------------------
    public static final ErrorCode MERCHANT_PROFIT_SHARE_EXIST_UNTREATED = new ErrorCode(2000, "该商户已申请开通分账");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_FIAL_TO_QUERY_UP = new ErrorCode(2000, "获取上游响应信息错误");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_INITIATE_RECEIVE_REPEAT = new ErrorCode(2000, "分账发起方和接收方不能相同");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_EXCESSIVE_PROPORTION = new ErrorCode(2000, "分账比例超出设置比例");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_NOT_FOUND = new ErrorCode(2000, "未查询到信息");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_INITIATOR_RECEIVER_NOT_SAME = new ErrorCode(2000, "分账发起方与分账接收方不能相同");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_INITIATOR_NOT_OPEN = new ErrorCode(2000, "分账发起方未开通分账");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_RECEIVER_NOT_OPEN = new ErrorCode(2000, "分账接收方未开通分账");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_INITIATOR_RECEIVER_ALREADY_CONFIG = new ErrorCode(2000, "分账发起方和分账接收方已配置");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_REPEAT_RECEIVER = new ErrorCode(2000, "存在重复分账接收方");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_DATA_NOT_EXIST = new ErrorCode(2000, "数据不存在");
    public static final ErrorCode MERCHANT_PROFIT_SHARE_ALREADY_SET = new ErrorCode(2000, "分账接收方已经配置其他分账模式");

    public static final ErrorCode AGENT_SUB_ID_IS_NOT_NULL = new ErrorCode(2001, "大商户id不能为空");

    //---------------------------------管控相关----------------------------------------------
    public static final ErrorCode CONTROL_DATA_NULL = new ErrorCode(1101, "商户管控数据不存在");
    public static final ErrorCode CONTROL_DATA_RETAIN_STATUS_FAIL = new ErrorCode(1102, "商户管控保留状态异常");
    public static final ErrorCode CONTROL_DATA_RETAIN_INCOMPLETE_FAIL = new ErrorCode(1103, "商户管控申请保留审核中");
    public static final ErrorCode CONTROL_BATCH_STATUS_NOT_REVIEW = new ErrorCode(1104, "批次状态非审核中");
    public static final ErrorCode CONTROL_BATCH_STATUS_NOT_TAKE_EFFECT = new ErrorCode(1105, "批次状态非生效中");
    public static final ErrorCode CONTROL_DATA_AUDIT_STATUS_NOT_DISPOSE = new ErrorCode(1106, "管控商户为非处理状态");
    public static final ErrorCode CONTROL_DATA_AUDIT_ORIGINALLY_PROCESSED = new ErrorCode(1107, "管控商户为原始状态，不可操作重置");
    public static final ErrorCode CONTROL_DATA_REVOKED_STATUS = new ErrorCode(1108, "管控商户已撤销，请勿重复撤销");
    public static final ErrorCode CONTROL_DATA_ID_NULL = new ErrorCode(1109, "商户管控ID不能为空");
    public static final ErrorCode MERCHANT_WHITE_LIST_EXIST = new ErrorCode(1110, "商户管控白名单已存在");
    public static final ErrorCode MERCHANT_WHITE_LIST_NOT_EXIST = new ErrorCode(1111, "商户管控白名单不存在");

    //---------------------------------------瑞银信通道相关----------------------------------------------------
    public static final ErrorCode CHANGE_RYX_DEBIT_CARD_FAIL = new ErrorCode(2001, "瑞银信结算卡修改失败，请联系运营处理");
    public static final ErrorCode CHANGE_RYX_CONFIRM_OPENED_FAIL = new ErrorCode(2001, "瑞银信确认开通失败，请联系运营处理");


    //---------------------------------------瑞银信(H)通道相关----------------------------------------------------
    public static final ErrorCode CHANGE_HULU_LAT_LNG_EXCEPTION = new ErrorCode(2002, "经纬度填写错误");
    public static final ErrorCode CHANGE_HULU_BASE_CHANGE_FAIL = new ErrorCode(2002, "瑞银信(H)基础修改失败");
    public static final ErrorCode CHANGE_HULU_SAVE_TERMINAL_FAIL = new ErrorCode(2002, "瑞银信(H)开通终端失败");
    public static final ErrorCode CHANGE_HULU_CHANGE_SETTLEMENT_CARD_FAIL = new ErrorCode(2002, "瑞银信(H)修改结算卡失败");
    public static final ErrorCode CHANGE_HULU_GET_ELECTRON_SIGN_URL_FAIL = new ErrorCode(2002, "瑞银信(H)获取电子签约链接失败");
    public static final ErrorCode CHANGE_HULU_GET_MERCHANT_STATUS_FAIL = new ErrorCode(2002, "瑞银信(H)获取商户状态失败");
    public static final ErrorCode CHANGE_HULU_CHANGE_PAYMENT_FAIL = new ErrorCode(2002, "瑞银信(H)支付修改失败");
    public static final ErrorCode CHANGE_HULU_QUERY_CHANGE_STATUS_FAIL = new ErrorCode(2002, "瑞银信(H)查询修改状态失败");
    public static final ErrorCode CHANGE_HULU_QUERY_TERMINAL_STATUS_FAIL = new ErrorCode(2002, "瑞银信(H)查询终端状态失败");
    public static final ErrorCode CHANGE_HULU_GET_SIGN_STATUS_FAIL = new ErrorCode(2002, "瑞银信(H)查询电子签约状态失败");
    public static final ErrorCode CHANGE_HULU_APPLY_OPENED_FAIL = new ErrorCode(2002, "瑞银信(H)申请开通失败");
    public static final ErrorCode CHANGE_HULU_CONFIRM_OPENED_FAIL = new ErrorCode(2002, "瑞银信(H)确认开通失败");

    public static final ErrorCode CHANGE_HULU_GET_MERCHANT_STATUS_AUDIT_FAIL = new ErrorCode(2005, "商户状态审核中");
    public static final ErrorCode CHANGE_HULU_GET_SIGN_STATUS_SIGNING_FAIL = new ErrorCode(2006, "商户电子签约审核中");


    //---------------------------------------银联商务通道相关----------------------------------------------------
    public static final ErrorCode YLSW_APPLY_OPENED_FAIL = new ErrorCode(2010, "银联商务申请开通失败");
    public static final ErrorCode YLSW_GET_STATUS = new ErrorCode(2010, "银联商务查询状态失败");
    public static final ErrorCode YLSW_GET_SIGN_URL = new ErrorCode(2010, "银联商务获取签约地址失败");
    public static final ErrorCode YLSW_CORPORATE_ACCOUNT_VERIFICATION_FAIL = new ErrorCode(2010, "银联商务对公账户验证失败");
    public static final ErrorCode YLSW_CORPORATE_ACCOUNT_AUTHENTICATION_FAIL = new ErrorCode(2010, "银联商务对公账户认证失败");
    public static final ErrorCode YLSW_CONFIRM_OPENED_FAIL = new ErrorCode(2010, "银联商务确认开通失败");

    //---------------------------------------国通通道相关------------------------------------------------------
    public static final ErrorCode GT_SIGN_CODE_FAIL = new ErrorCode(2020, "国通三方通道发送签约验证码失败");
    public static final ErrorCode GT_MODIFICATION_SUBMISSION_FAIL = new ErrorCode(2020, "国通三方通道修改提交失败");
    public static final ErrorCode GT_MODIFY_SETTLEMENT_CARD_FAIL = new ErrorCode(2020, "国通三方通道修改结算卡失败");
    public static final ErrorCode GT_OPEN_AXQ_FAIL = new ErrorCode(2020, "国通三方通道安心签开户失败");
    public static final ErrorCode GT_ELECTRONIC_CONTRACT_SIGNING_FAIL = new ErrorCode(2020, "国通三方通道电子合同签约失败");
    public static final ErrorCode GT_QUERY_MERCHANT_STATUS_FAIL = new ErrorCode(2020, "国通三方通道查询商户状态失败");
    public static final ErrorCode CHANGE_GT_CONFIRM_OPENED_FAIL = new ErrorCode(2020, "国通确认开通失败，请联系运营处理");
    public static final ErrorCode CHANGE_GT_SIGNING_FAIL = new ErrorCode(2020, "国通通失败，请联系运营处理");

    //---------------------------------------通道相关----------------------------------------------------
    public static final ErrorCode CHANNEL_UPDATE_MERCHANT_CARD_FAIL = new ErrorCode(2030, "修改结算卡失败，请核对信息后再一次提交");
    public static final ErrorCode CHANNEL_UPDATE_MERCHANT_CARD_REDIS_KEY_EXIST = new ErrorCode(2031, "更新上游结算卡工单已存在");
    public static final ErrorCode CHANNEL_UPDATE_MERCHANT_CARD_REDIS_KEY_NONE = new ErrorCode(2031, "更新上游结算卡工单不存在，请确认提交后在查询");
    public static final ErrorCode CHANNEL_NOT_UPDATE_RATE = new ErrorCode(2032, "该通道不支持修改费率");
    public static final ErrorCode CHANGE_SXF_TQ_CONFIRM_OPENED_FAIL = new ErrorCode(2033, "随行付确认开通失败，请联系运营处理");
    public static final ErrorCode CHANGE_LAKLA_V3_CONFIRM_OPENED_FAIL = new ErrorCode(2034, "拉卡拉确认开通失败，请联系运营处理");
    public static final ErrorCode LAKAL_V3_SEARCH = new ErrorCode(2035, "查询拉卡拉电子合约失败，请联系运营处理");
    public static final ErrorCode LAKAL_V3_STATUS_SEARCH = new ErrorCode(2036, "查询拉卡拉商户状态失败，请联系运营处理");
    public static final ErrorCode LAKAL_V3_REPORT_SEARCH = new ErrorCode(2037, "查询拉卡拉报备结果失败，请联系运营处理");
    public static final ErrorCode LAKAL_V3_OPEND_SEARCH = new ErrorCode(2038, "申请开通拉卡拉失败，请联系运营处理");
    public static final ErrorCode CHANGE_YZ_PAY_CONFIRM_OPENED_FAIL = new ErrorCode(2040, "数币直连确认开通失败，请联系运营处理");
    public static final ErrorCode CHANGE_WQF_CONFIRM_OPENED_FAIL = new ErrorCode(2040, "数币直连确认开通失败，请联系运营处理");

    //---------------------------------------应用平台相关------------------------------------------------------
    public static final ErrorCode AFFILIATION_ACCEPT_ID_ERROR = new ErrorCode(2101, "所选网点与领取网点不一致，请核对后领取");
    public static final ErrorCode RATE_SUBSIDY_NOT_NULL = new ErrorCode(2102, "请选择是否分润");
    public static final ErrorCode RATE_NOT_NULL = new ErrorCode(2103, "费率不能为空");
    public static final ErrorCode STAND_RATE_NOT_NULL = new ErrorCode(2103, "标准费率不能为空");
    public static final ErrorCode STANDARD_RATE_NOT_INTERVAL = new ErrorCode(2103, "费率应添加2.3到6.0区间，请重新填写");
    public static final ErrorCode MERCHANT_PUBLIC_RECEIVE = new ErrorCode(2104, "只允许修改未领取商户");
    public static final ErrorCode PARTNER_APPLICATION_RATE_EXIST = new ErrorCode(2105, "市级应用合作费率已存在");
    public static final ErrorCode PARTNER_APPLICATION_RATE_NOT_EXIST = new ErrorCode(2105, "市级应用合作费率不存在");
    public static final ErrorCode PARTNER_APPLICATION_RATE_CREATE_ERROR = new ErrorCode(2106, "市级应用合作费率创建失败");
    public static final ErrorCode PARTNER_APPLICATION_RATE_UPDATE_ERROR = new ErrorCode(2107, "市级应用合作费率修改失败");
    public static final ErrorCode BRANCH_ACCESS_APPLICATION_NOT_EXISTS = new ErrorCode(2108, "省级应用合作不存在或禁用");
    public static final ErrorCode BRANCH_ACCESS_APPLICATION_EXISTS = new ErrorCode(2108, "省级应用合作已存在");
    public static final ErrorCode PARTNER_ACCESS_APPLICATION_NOT_EXISTS = new ErrorCode(2109, "市级应用合作不存在或禁用");
    public static final ErrorCode PARTNER_ACCESS_APPLICATION_EXISTS = new ErrorCode(2109, "市级应用合作已存在");
    public static final ErrorCode APPLICATION_NOT_EXISTS = new ErrorCode(2110, "应用不存在");
    public static final ErrorCode APPLICATION_NOT_SUBSIDY = new ErrorCode(2111, "当前应用不分润，请选择分润应用");
    public static final ErrorCode SUBSIDY_RATE_NOT_INTERVAL = new ErrorCode(2112, "补贴费区间为0到2.0，请重新填写");
    public static final ErrorCode SIMPLE_CODE_ID_NOT_NULL = new ErrorCode(2113, "请输入门店id/设备id");
    public static final ErrorCode SIMPLE_CODE_TYPE_NOT_NULL = new ErrorCode(2113, "请输入设备类型");
    public static final ErrorCode SUBSCRIBE_MESSAGE_NOT_EXIST = new ErrorCode(2114, "该商户没有订阅消息");

    // -------------------------------------------------商户补充资料错误----------------------------------------------
    public final static ErrorCode SHOP_INFOR_NOT_NULL = new ErrorCode(2115, "商户补资料通知已存在");
    public final static ErrorCode SHOP_NOT_NEED_INFOR = new ErrorCode(2116, "商户无需补充资料");
    public final static ErrorCode MERCHANT_SUBSIDY_MSG_NOT_EXISTS = new ErrorCode(2116, "为查询到商户补充资料信息");
    public final static ErrorCode MERCHANT_CHANNEL_SHOP_ID_HULU = new ErrorCode(2117, "商户号错误或该商户号不为葫芦通道");
    public final static ErrorCode CHANGE_RATES_FAIL = new ErrorCode(2118, "修改失败");

    // -------------------------------------------------设备相关----------------------------------------------
    public final static ErrorCode SN_IS_EXIST = new ErrorCode(2119, "SN号已存在，请勿重复导入");
    public final static ErrorCode SHORTURL_IS_NULL = new ErrorCode(2120, "未查询到终端");
    public final static ErrorCode ADVERTISEMENT_IS_NULL = new ErrorCode(2121, "未查询到广告");
    public final static ErrorCode CONFIGURE_DEVICE_ADVERT_FAIL = new ErrorCode(2121, "配置设备广告失败");
    public final static ErrorCode RESET_DEVICE_ADVERT_FAIL = new ErrorCode(2121, "配置设备广告失败");
    public final static ErrorCode THE_DEVICE_IS_CURRENTLY_NOT_SUPPORTED = new ErrorCode(2122, "暂不支持该设备");
    public final static ErrorCode NO_ADVERTISING_CONFIGURATION_INFORMATION_FOUND = new ErrorCode(2123, "未查询到广告配置信息");
    public final static ErrorCode SAME_ORG_ALLOW_ENABLE_ONE_ADVERT = new ErrorCode(2124, "同一机构只允许启用一条设备广告");
    public final static ErrorCode ADVERT_HAS_BEEN_CONFIG_ORG = new ErrorCode(2125, "该广告已被配置到机构，请先删除！");

    public final static ErrorCode SHOPID_AND_PARAM_JSON = new ErrorCode(2126, "表中商户号或者通知内容不能为空");

    public final static ErrorCode TIME_ERRO = new ErrorCode(2127, "定时时间不能小于当前时间");

    public final static ErrorCode RATE_EROR = new ErrorCode(2128, "商户重置重开失败");

    public final static ErrorCode XINXIANG_EROR = new ErrorCode(2129, "信翔由系统自动分配");
    public final static ErrorCode PROPORTION_EROR = new ErrorCode(2130, "修改不可超过100%");
    public final static ErrorCode MINITYPE_EROR = new ErrorCode(2131, "无法根际广告id找到广告位置");
    public final static ErrorCode ADID_EROR = new ErrorCode(2132, "广告id不能为空");
    public final static ErrorCode BPAAID_EROR = new ErrorCode(2134, "机构号不能为空");
    public final static ErrorCode PLATFORM_EROR = new ErrorCode(2135, "投放平台不能为空");
    public final static ErrorCode BPAATYPE_EROR = new ErrorCode(2136, "归属级别不能为空");

    public final static ErrorCode SIGN_EROR = new ErrorCode(2137, "签名不能为空");

    public final static ErrorCode SIGN_IS_EROR = new ErrorCode(2138, "签名校验失败");
    public final static ErrorCode BATCH_DELETE_ERROR = new ErrorCode(2139, "批量删除设备失败");

    // --------------------------------------------商户走访任务----------------------------------------------
    public final static ErrorCode BATCH_CREATE_VISIT_MSG_ERROR = new ErrorCode(2201, "批量导入商户走访信息异常");

    //  --------------------------------------------数据报表----------------------------------------------
    public final static ErrorCode BRANCH_NOT_NULL = new ErrorCode(2301, "省级不能为空");

    public final static ErrorCode IMAGE_EROR = new ErrorCode(2302, "广告弹窗图片不能为空");

    public final static ErrorCode NOTICEID_EROR = new ErrorCode(2303, "当前批次号错误");

    public final static ErrorCode NOTICEIDTYPE_EROR = new ErrorCode(2303, "当前批次号下无此弹窗");

    public final static ErrorCode INS_SIZE_EROR = new ErrorCode(2304, "机构弹窗一次只允许发一条通知");

    public final static ErrorCode PARTNER_IS_NULL = new ErrorCode(2305, "该省份下无市");
    public final static ErrorCode AGENT_IS_NULL = new ErrorCode(2305, "该市下无区县");

    public final static ErrorCode OPENID_IS_NULL = new ErrorCode(2306, "该区县下无业务员");

    public final static ErrorCode PHONE_IS_NULL = new ErrorCode(2307, "手机号为空");

    public final static ErrorCode AGENT_PHONE_IS_NULL = new ErrorCode(2306, "业务员信息为空");

    public final static ErrorCode NOTICE_TITLE_IS_NULL = new ErrorCode(2307, "通知标题不能为空");

    public final static ErrorCode NOTICE_IS_PERM_CLOSE = new ErrorCode(2308, "当前批次不允许永久关闭");
    //------------------------------------账户补贴-----------------------------------------------------------------------
    public final static ErrorCode SHOP_IS_BIND = new ErrorCode(2401, "该商户已绑定");

    //------------------------------------切换通道-----------------------------------------------------------------------
    public final static ErrorCode CHANNEL_IS_NULL = new ErrorCode(2402, "支付通道不能为空");


    public final static ErrorCode CHANNEL_IS_ERROR = new ErrorCode(2403, "切换通道必须保持一致");

    public final static ErrorCode CHANNEL_NULL = new ErrorCode(2404, "当前商户所切换通道为空");

    public final static ErrorCode ERROR_ORDER_RESULT = new ErrorCode(2405, "申请原因不能为空");
    public final static ErrorCode ERROR_REQIE_TIME = new ErrorCode(2406, "日切时间不能为空");
    public final static ErrorCode PARTNER_RIGHT_ERROR = new ErrorCode(2407, "机构权限异常");
    public final static ErrorCode SEND_CODE_FAIL = new ErrorCode(2408, "验证码发送失败");
    public final static ErrorCode LOGIN_CODE_LOSE_EFFICACY = new ErrorCode(2409, "验证码失效");
    public final static ErrorCode PHONE_EXCEPTION = new ErrorCode(2410, "手机号异常");

    public static final ErrorCode PHONE_NOT_SAME = new ErrorCode(2411, "手机号一致");
    public static final ErrorCode GET_USER_INFO_FAIL = new ErrorCode(2412, "获取用户信息失败");
    public static final ErrorCode QR_CODE_HAS_EXPORED = new ErrorCode(2413, "二维码已失效");
    public static final ErrorCode UNBOUND_WECHAT = new ErrorCode(2414, "该账号未绑定微信");
    public static final ErrorCode WECHAT_AUTH_FAIL = new ErrorCode(2415, "微信授权失败");
    public static final ErrorCode ACCOUNT_HAS_BEEN_LINKED_TO_WECHAT = new ErrorCode(2416, "该账号已绑定微信");
    public static final ErrorCode ACCOUNT_IS_NOT_BOUND_TO_WECHAT = new ErrorCode(2417, "该账号未绑定微信");
    public static final ErrorCode ILLEGAL_UNBINDING = new ErrorCode(2418, "请使用正确的微信进行操作");
    public static final ErrorCode INCORRECT_WECHAT = new ErrorCode(2418, "请使用正确的微信进行操作");

    public static final ErrorCode EXPORT_ADD_SHORTURL_ERROR = new ErrorCode(2419, "生成空码牌失败");


    public static final ErrorCode IS_BOSS_ERROR = new ErrorCode(2420, "当前用户无身份");

    public static final ErrorCode H5_SIGN_IS_NULL = new ErrorCode(2430, "调用失败");

    public static final ErrorCode WX_SET_URL = new ErrorCode(2431, "设置url不能为空");

    public static final ErrorCode STOP_RATE_PACKAGE_ERROR = new ErrorCode(2432, "停止补贴失败，请联系运营处理");
    public static final ErrorCode APPROVAL_TASK_IS_NULL = new ErrorCode(2433, "当前无审批任务");
    public static final ErrorCode APPROVAL_TASK_ERROR = new ErrorCode(2434, "当前审批任务不存在或未开放审批权限");
    public static final ErrorCode APPROVAL_ERROR = new ErrorCode(2435, "流程执行失败");

    public static final ErrorCode CREATAE_APPROVAL_RULE_ERROR = new ErrorCode(2436, "创建审批规则不能全部为空");
}


