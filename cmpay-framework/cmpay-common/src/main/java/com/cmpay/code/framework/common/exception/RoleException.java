package com.cmpay.code.framework.common.exception;

import com.cmpay.code.framework.common.exception.enums.ServiceErrorCodeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2025/5/29 16:26
 * @version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class RoleException extends RuntimeException {
    /**
     * 业务错误码
     *
     * @see ServiceErrorCodeRange
     */
    private Integer code;
    /**
     * 错误提示
     */
    private String message;

    /**
     * 空构造方法，避免反序列化问题
     */
    public RoleException() {
    }

    public RoleException(ErrorCode errorCode) {
        super(errorCode.getMsg());
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
    }
}
