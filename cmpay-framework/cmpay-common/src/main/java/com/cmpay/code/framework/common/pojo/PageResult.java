package com.cmpay.code.framework.common.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "分页结果")
@Data
public final class PageResult<T> implements Serializable {

    @Schema(description = "数据", required = true)
    private List<T> list;

    @Schema(description = "总量", required = true)
    private Long total;

    @Schema(description = "总页数", required = true)
    private Long numberSize;

    @Schema(description = "数据", required = true)
    private List<T> lists;

    public PageResult() {
    }

    public PageResult(List<T> list, Long total, List<T> lists) {
        this.lists = lists;
        this.total = total;
    }

    public PageResult(List<T> list, Long total) {
        this.list = list;
        this.total = total;
    }

    public PageResult(Long total) {
        this.list = new ArrayList<>();
        this.total = total;
    }

    public PageResult(List<T> list, Long total,Long numberSize) {
        this.list = list;
        this.total = total;
        this.numberSize = numberSize;
    }

    public static <T> PageResult<T> empty() {
        return new PageResult<>(0L);
    }

    public static <T> PageResult<T> empty(Long total) {
        return new PageResult<>(total);
    }

}
