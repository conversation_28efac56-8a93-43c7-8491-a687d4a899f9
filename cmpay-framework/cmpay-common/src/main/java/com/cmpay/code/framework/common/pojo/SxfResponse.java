package com.cmpay.code.framework.common.pojo;

import com.cmpay.code.framework.common.constant.SxfConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * author suohong<PERSON>
 * date 2023/12/11 16:23
 * version 1.0
 * 随行付统一返回类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SxfResponse<T> {
    private Integer code;
    private String message;
    private T data;


    public static <T> SxfResponse<T> success(String message, T data) {
        SxfResponse<T> sxfResponse = new SxfResponse<>();
        sxfResponse.setCode(SxfConstant.SUCCESS);
        sxfResponse.setMessage(message);
        sxfResponse.setData(data);
        return sxfResponse;
    }

    public static <T> SxfResponse<T> fail(String message, T data) {
        SxfResponse<T> sxfResponse = new SxfResponse<>();
        sxfResponse.setCode(SxfConstant.FAIL);
        sxfResponse.setMessage(message);
        sxfResponse.setData(data);
        return sxfResponse;
    }
}
