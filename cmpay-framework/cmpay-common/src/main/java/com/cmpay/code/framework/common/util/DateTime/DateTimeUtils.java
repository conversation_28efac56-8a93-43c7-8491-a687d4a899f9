package com.cmpay.code.framework.common.util.DateTime;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.cmpay.code.framework.common.exception.GlobalErrorCodePc.START_AND_END_TIME_NOT_NULL;
import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-06 10:16:22
 * @version: 1.0
 */
@Slf4j
public class DateTimeUtils {

    /**
     * 显示年月日时分秒，例如 2015-08-11 09:51:53.
     */
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 仅显示年月日，例如 2015-08-11.
     */
    public static final String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 仅显示时分秒，例如 09:51:53.
     */
    public static final String TIME_PATTERN = "HH:mm:ss";

    /**
     * 显示年月日时分秒(无符号)，例如 20150811095153.
     */
    public static final String UNSIGNED_DATETIME_PATTERN = "yyyyMMddHHmmss";

    /**
     * 仅显示年月日(无符号)，例如 20150811.
     */
    public static final String UNSIGNED_DATE_PATTERN = "yyyyMMdd";

    /**
     * 获取几天之后的时间
     */
    public static String getNumberDayDateTime(int numDays) {
        // 获取当前时间
        Date now = new Date();
        // 获取Calendar实例
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        // 在当前时间上加15天，表示半个月
        calendar.add(Calendar.MONTH, numDays);
        // 获取半个月后的时间
        Date halfMonthLater = calendar.getTime();
        DateFormat dtf2 = new SimpleDateFormat(DATETIME_PATTERN);
        return dtf2.format(halfMonthLater);
    }

    /**
     * 格式化string类型 yyyy-MM-dd
     *
     * @param dateTime 时间
     * @return 字符串
     */
    public static Date getStringDisplayDate(String dateTime) {
        DateFormat dtf2 = new SimpleDateFormat(DATE_PATTERN);
        Date parse = new Date();
        try {
            parse = dtf2.parse(dateTime);
        } catch (ParseException e) {
            log.error("时间转换失败");
        }
        return parse;
    }

    /**
     * 格式化string类型 yyyy-MM-dd HH:mm:ss
     *
     * @param dateTime 时间
     * @return 字符串
     */
    public static Date getStringDisplayDateTime(String dateTime) {
        DateFormat dtf2 = new SimpleDateFormat(DATETIME_PATTERN);
        Date parse = new Date();
        try {
            parse = dtf2.parse(dateTime);
        } catch (ParseException e) {
            log.error("时间转换失败");
        }
        return parse;
    }

    /**
     * 格式化Date类型 yyyy-MM-dd
     *
     * @param date 时间
     * @return 字符串
     */
    public static String getDateDisplayString(Date date) {
        DateFormat dtf2 = new SimpleDateFormat(DATE_PATTERN);
        return dtf2.format(date);
    }

    /**
     * 格式化string类型 yyyy-MM-dd HH:mm:ss
     *
     * @param date 时间
     * @return 字符串
     */
    public static String getDateTimeDisplayString(Date date) {
        DateFormat dtf2 = new SimpleDateFormat(DATETIME_PATTERN);
        return dtf2.format(date);
    }

    /**
     *
     * @param time
     * @return
     */
    public static String stringToStringDate(String time) {
        String dateTime ;
        Date stringDisplayDate = getStringDisplayDate(time);
        dateTime = getDateDisplayString(stringDisplayDate);
        return dateTime;
    }

    /**
     *
     * @param time
     * @return
     */
    public static String stringToStringDateTime(String time) {
        String dateTime ;
        Date stringDisplayDate = getStringDisplayDateTime(time);
        dateTime = getDateTimeDisplayString(stringDisplayDate);
        return dateTime;
    }

    public static Date dateToDateTime(Date date) {
        Date dateTime;
        String dateTimeDisplayString = getDateTimeDisplayString(date);
        dateTime = getStringDisplayDateTime(dateTimeDisplayString);
        return dateTime;
    }

    /**
     * 获取当前日期和时间字符串.
     *
     * @return String 日期时间字符串，例如 2015-08-11 09:51:53
     */
    public static String getLocalDateTimeStr() {
        return format(LocalDateTime.now(), DATETIME_PATTERN);
    }

    /**
     * 获取当前日期字符串.
     *
     * @return String 日期字符串，例如2015-08-11
     */
    public static String getLocalDateStr() {
        return format(LocalDate.now(), DATE_PATTERN);
    }

    /**
     * 获取当前时间字符串.
     *
     * @return String 时间字符串，例如 09:51:53
     */
    public static String getLocalTimeStr() {
        return format(LocalTime.now(), TIME_PATTERN);
    }


    /**
     * 获取日期时间字符串
     *
     * @param temporal 需要转化的日期时间
     * @param pattern  时间格式
     * @return String 日期时间字符串，例如 2015-08-11 09:51:53
     */
    public static String format(TemporalAccessor temporal, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return dateTimeFormatter.format(temporal);
    }

    /**
     * 日期时间字符串转换为日期时间(java.time.LocalDateTime)
     *
     * @param localDateTimeStr 日期时间字符串
     * @param pattern          日期时间格式 例如DATETIME_PATTERN
     * @return LocalDateTime 日期时间
     */
    public static LocalDateTime parseLocalDateTime(String localDateTimeStr, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(localDateTimeStr, dateTimeFormatter);
    }

    /**
     * 日期字符串转换为日期(java.time.LocalDate)
     *
     * @param localDateStr 日期字符串
     * @param pattern      日期格式 例如DATE_PATTERN
     * @return LocalDate 日期
     */
    public static LocalDate parseLocalDate(String localDateStr, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(localDateStr, dateTimeFormatter);
    }

    /**
     * 获取指定日期时间加上指定数量日期时间单位之后的日期时间.
     *
     * @param localDateTime 日期时间
     * @param num           数量
     * @param chronoUnit    日期时间单位
     * @return LocalDateTime 新的日期时间
     */
    public static LocalDateTime plus(LocalDateTime localDateTime, int num, ChronoUnit chronoUnit) {
        return localDateTime.plus(num, chronoUnit);
    }


    /**
     * 根据ChronoUnit计算两个日期之间相隔年数或月数或天数
     *
     * @param start      开始日期
     * @param end        结束日期
     * @param chronoUnit 日期时间单位,(ChronoUnit.YEARS,ChronoUnit.MONTHS,ChronoUnit.WEEKS,ChronoUnit.DAYS)
     * @return long 相隔年数或月数或天数
     */
    public static long getChronoUnitBetween(LocalDate start, LocalDate end, ChronoUnit chronoUnit) {
        return Math.abs(start.until(end, chronoUnit));
    }

    /**
     * 获取本年第一天的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfYearStr() {
        return getFirstDayOfYearStr(LocalDateTime.now());
    }

    /**
     * 获取本年最后一天的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfYearStr() {
        return getLastDayOfYearStr(LocalDateTime.now());
    }

    /**
     * 获取指定日期当年第一天的日期字符串
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfYearStr(LocalDateTime localDateTime) {
        return getFirstDayOfYearStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期当年最后一天的日期字符串
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfYearStr(LocalDateTime localDateTime) {
        return getLastDayOfYearStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期当年第一天的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfYearStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0), pattern);
    }

    /**
     * 获取指定日期当年最后一天的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfYearStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.with(TemporalAdjusters.lastDayOfYear()).withHour(23).withMinute(59).withSecond(59), pattern);
    }

    /**
     * 获取本月第一天的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfMonthStr() {
        return getFirstDayOfMonthStr(LocalDateTime.now());
    }

    /**
     * 获取本月最后一天的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfMonthStr() {
        return getLastDayOfMonthStr(LocalDateTime.now());
    }

    /**
     * 获取指定日期当月第一天的日期字符串
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getFirstDayOfMonthStr(LocalDateTime localDateTime) {
        return getFirstDayOfMonthStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期当月最后一天的日期字符串
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfMonthStr(LocalDateTime localDateTime) {
        return getLastDayOfMonthStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期当月第一天的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfMonthStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0), pattern);
    }

    /**
     * 获取指定日期当月最后一天的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfMonthStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59), pattern);
    }

    /**
     * 获取本周第一天的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfWeekStr() {
        return getFirstDayOfWeekStr(LocalDateTime.now());
    }

    /**
     * 获取本周最后一天的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfWeekStr() {
        return getLastDayOfWeekStr(LocalDateTime.now());
    }

    /**
     * 获取指定日期当周第一天的日期字符串,这里第一天为周一
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfWeekStr(LocalDateTime localDateTime) {
        return getFirstDayOfWeekStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期当周最后一天的日期字符串,这里最后一天为周日
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfWeekStr(LocalDateTime localDateTime) {
        return getLastDayOfWeekStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期当周第一天的日期字符串,这里第一天为周一,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfWeekStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0), pattern);
    }

    /**
     * 获取指定日期当周最后一天的日期字符串,这里最后一天为周日,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfWeekStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.with(DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59), pattern);
    }


    /**
     * 获取今天开始时间的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getStartTimeOfDayStr() {
        return getStartTimeOfDayStr(LocalDateTime.now());
    }

    /**
     * 获取今天结束时间的日期字符串
     *
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getEndTimeOfDayStr() {
        return getEndTimeOfDayStr(LocalDateTime.now());
    }

    /**
     * 获取指定日期开始时间的日期字符串
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getStartTimeOfDayStr(LocalDateTime localDateTime) {
        return getStartTimeOfDayStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期结束时间的日期字符串
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getEndTimeOfDayStr(LocalDateTime localDateTime) {
        return getEndTimeOfDayStr(localDateTime, DATETIME_PATTERN);
    }

    /**
     * 获取指定日期开始时间的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd HH:mm:ss
     */
    public static String getStartTimeOfDayStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.withHour(0).withMinute(0).withSecond(0), pattern);
    }

    /**
     * 获取指定日期结束时间的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getEndTimeOfDayStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.withHour(23).withMinute(59).withSecond(59), pattern);
    }

    public static void timeIsNull(String startTime, String endTime) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw exception(START_AND_END_TIME_NOT_NULL);
        }
    }

    /**
     * 比较两个字符串时间，判断第一个时间是否大于第二个时间。
     *
     * @param timeString1 第一个时间字符串，格式为 "yyyy-MM-dd"
     * @param timeString2 第二个时间字符串，格式为 "yyyy-MM-dd"
     * @return 如果第一个时间大于第二个时间，则返回true；否则返回false。
     */
    public static boolean isTime1AfterTime2(String timeString1, String timeString2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1;
        Date date2;

        try {
            date1 = sdf.parse(timeString1);
            date2 = sdf.parse(timeString2);

            return date1.after(date2);
        } catch (ParseException e) {
            e.printStackTrace();
            return false; // 如果解析失败，则认为第一个时间不大于第二个时间
        }
    }

    public static String convertDateToFormattedString(String inputDate) {

        SimpleDateFormat inputFormat = new SimpleDateFormat(UNSIGNED_DATE_PATTERN);
        SimpleDateFormat outputFormat = new SimpleDateFormat(DATE_PATTERN);
        Date date = null;

        try {
            // Parse the input date string into a Date object
            date = inputFormat.parse(inputDate);
        } catch (ParseException e) {
            System.out.println("Parsing error: " + e.getMessage());
            return inputDate;
        }
        return outputFormat.format(date);
    }

    /**
     * 计算两个日期字符串之间的天数差。
     *
     * @param dateString1 第一个日期字符串
     * @param dateString2 第二个日期字符串
     * @param dateFormat   日期字符串的格式，如 "yyyy-MM-dd"
     * @return 两个日期之间的天数差
     * @throws DateTimeParseException 如果日期字符串格式不正确或无法解析
     */
    public static long daysBetween(String dateString1, String dateString2, String dateFormat) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        LocalDate date1 = LocalDate.parse(dateString1, formatter);
        LocalDate date2 = LocalDate.parse(dateString2, formatter);
        return ChronoUnit.DAYS.between(date1, date2);
    }

    /**
     * 生成最近numDays天的日期列表。
     * @param dateStr 参考时间
     * @param numDays 天数
     * @return 近多少天的日期列表
     */
    public static List<String> generateRecentDates(String dateStr, int numDays) {
        List<String> dates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN);
        // 解析输入的日期字符串
        LocalDate date = LocalDate.parse(dateStr, formatter);
        // 生成过去numDays天的日期，包括今天
        for (int i = 0; i < numDays; i++) {
            LocalDate currentDate = date.minusDays(i);
            dates.add(currentDate.format(formatter));
        }
        return dates;
    }

    /**
     * 获取当前时间加上指定分钟数后的时间，并格式化为 yyyyMMddHHmmss 字符串
     *
     * @param minutes 要增加的分钟数
     * @return 增加指定分钟数后的时间，格式为 yyyyMMddHHmmss
     */
    public static String getTimeAfterMinutesFormatted(int minutes) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 增加指定分钟数
        LocalDateTime timeAfterMinutes = now.plusMinutes(minutes);
        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化时间并返回
        return timeAfterMinutes.format(formatter);
    }

    /**
     * 获取当前时间的第二天凌晨日期，格式为 yyyyMMddHHmmss
     *
     * @return 第二天凌晨的时间，格式为 yyyyMMddHHmmss
     */
    public static String getNextDayMidnightFormatted() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取第二天的日期
        LocalDate nextDay = today.plusDays(1);
        // 获取凌晨的时间（00:00:00）
        LocalTime midnight = LocalTime.MIDNIGHT;
        // 组合日期和时间
        LocalDateTime nextDayMidnight = LocalDateTime.of(nextDay, midnight);
        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化时间并返回
        return nextDayMidnight.format(formatter);
    }

    public static String getNextOneMin(){
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 获取一分钟之后的时间
        LocalDateTime oneMinuteLater = now.plusMinutes(1);
        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 格式化时间并返回
        return oneMinuteLater.format(formatter);
    }
}