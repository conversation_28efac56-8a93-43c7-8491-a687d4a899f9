package com.cmpay.code.framework.common.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.core.codec.EncodingException;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.*;
import java.net.*;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Iterator;
import java.util.Map;
import java.util.TimerTask;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpUrlConnectionToInterface {
    private static final int CONNECT_TIMEOUT = 20000;// 设置连接建立的超时时间为10s
    private static final int SOCKET_TIMEOUT = 20000;
    private static final int MAX_CONN = 3000;    // 最大连接数
    private static final int Max_PRE_ROUTE = 3000;
    private static final int MAX_ROUTE = 3000;
    private static CloseableHttpClient httpClient;                // 发送请求的客户端单例
    private static PoolingHttpClientConnectionManager manager;                // 连接池管理类
    private static ScheduledExecutorService monitorExecutor;
    // 发送请求的客户端单例
    private final static Object syncLock = new Object(); // 相当于线程锁,用于线程安全

    /**
     * 调用第三方接口：post请求
     *
     * @param url        第三方地址
     * @param postString 拼接参数
     * @return
     */
    public static String AccessUrl(String url, String postString) {
        String result = null;
        HttpURLConnection conn = null;
        try {
            URL postUrl = new URL(url);
            conn = (HttpURLConnection) postUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            conn.setConnectTimeout(300000);
            conn.setReadTimeout(300000);
            OutputStream os = conn.getOutputStream();
            os.write(postString.getBytes("utf-8"));
            os.flush();
            os.close();

            int code = conn.getResponseCode();
            conn.getHeaderFields();
            conn.getContentLength();
            if (code == 200) {//返回成功
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                result = buffer.toString();
            } else {
                log.info("返回失败=" + code);
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (EncodingException e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return result;
    }

    /**
     * 调用第三方接口：post请求
     *
     * @param url        第三方地址
     * @param postString 拼接参数
     * @return
     */
    public static String AccessUrlFormDate(String url, String postString) {
        String result = null;
        HttpURLConnection con = null;
        try {
            String encode = URLEncoder.encode(postString, "UTF-8");
            URL urlObj = new URL(url + "?" + encode);
            con = (HttpURLConnection) urlObj.openConnection();
            con.setRequestMethod("GET"); // 改为GET请求，假设你只需要获取URL的信息
            con.setDoInput(true);
            con.setUseCaches(true); // GET请求可以使用缓存

            // 可以设置其他请求头信息，如果需要的话
            // con.setRequestProperty("Header-Name", "Header-Value");

            StringBuffer buffer = new StringBuffer();
            BufferedReader reader = null;

            reader = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }

            result = buffer.toString();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }

        return result;
    }

    /**
     * 调用上传接口
     *
     * @param url  上传地址
     * @param file 文件
     * @return
     */
    public static String AccessUrlFile(String url, MultipartFile file) {
        String result = null;
        HttpURLConnection con = null;
        try {
            /**
             * 第一部分
             */
            URL urlObj = new URL(url);
            // 连接
            con = (HttpURLConnection) urlObj.openConnection();
            /**
             * 设置关键�?
             */
            con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
            con.setDoInput(true);
            con.setDoOutput(true);
            con.setUseCaches(false); // post方式不能使用缓存
            // 设置请求头信�?
            con.setRequestProperty("Connection", "Keep-Alive");
            con.setRequestProperty("Charset", "UTF-8");
            // 设置边界
            String BOUNDARY = "----------" + System.currentTimeMillis();
            con.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            // 请求正文信息
            // 第一部分�?
            StringBuilder sb = new StringBuilder();
            sb.append("--"); // 必须多两道线
            sb.append(BOUNDARY);
            sb.append("\r\n");
            System.out.println("=========" + file.getName());
            System.out.println("=====11=1111111===" + file.getOriginalFilename());
            sb.append("Content-Disposition: form-data;name=\"file\";filename=\"" + file.getOriginalFilename() + "\"\r\n");
            sb.append("Content-Type:application/octet-stream\r\n\r\n");
            byte[] head = sb.toString().getBytes("utf-8");
            // 获得输出�?
            OutputStream out = new DataOutputStream(con.getOutputStream());
            // 输出表头
            out.write(head);
            // 文件正文部分
            // 把文件已流文件的方式 推入到url�?
            DataInputStream in = new DataInputStream(file.getInputStream());
            int bytes = 0;
            byte[] bufferOut = new byte[1024];
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            in.close();
            // 结尾部分
            byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义�?后数据分隔线
            out.write(foot);
            out.flush();
            out.close();
            StringBuffer buffer = new StringBuffer();
            BufferedReader reader = null;
            // 定义BufferedReader输入流来读取URL的响�?
            reader = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }
            if (result == null) {
                result = buffer.toString();
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }

        return result;
    }

    public static String AccessUrlGet(String url){
        String result = null;
        HttpURLConnection conn = null;
        try {
            URL postUrl = new URL(url);
            conn = (HttpURLConnection)postUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");// 连接超时30秒
            conn.connect();
            int code = conn.getResponseCode();

            if(code==200){//返回成功
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
                String line;
                StringBuffer buffer = new StringBuffer();
                while((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                result = buffer.toString();
            }else{
                log.info("返回失败："+code);
            }
        } catch (MalformedURLException e) {
            conn.disconnect();
            e.printStackTrace();
        } catch (IOException e) {
            conn.disconnect();
            e.printStackTrace();
        } finally{
            if(conn != null){
                conn.disconnect();
            }
        }
        return result;

    }

    /**
     * 调用上传接口并返回响应的Excel文件的字节数组
     *
     * @param url  上传地址
     * @param file 要上传的Excel文件
     * @return 响应的Excel文件的字节数组，如果失败则返回null
     */
    public static byte[] uploadAndGetExcelAsByteArray(String url, MultipartFile file) {
        HttpURLConnection con = null;
        try {
            /**
             * 第一部分
             */
            URL urlObj = new URL(url);
            // 连接
            con = (HttpURLConnection) urlObj.openConnection();
            /**
             * 设置关键�?
             */
            con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
            con.setDoInput(true);
            con.setDoOutput(true);
            con.setUseCaches(false); // post方式不能使用缓存
            // 设置请求头信�?
            con.setRequestProperty("Connection", "Keep-Alive");
            con.setRequestProperty("Charset", "UTF-8");
            // 设置边界
            String BOUNDARY = "----------" + System.currentTimeMillis();
            con.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            // 请求正文信息
            // 第一部分�?
            StringBuilder sb = new StringBuilder();
            sb.append("--"); // 必须多两道线
            sb.append(BOUNDARY);
            sb.append("\r\n");
            System.out.println("=========" + file.getName());
            System.out.println("=====11=1111111===" + file.getOriginalFilename());
            sb.append("Content-Disposition: form-data;name=\"file\";filename=\"" + file.getOriginalFilename() + "\"\r\n");
            sb.append("Content-Type:application/octet-stream\r\n\r\n");
            byte[] head = sb.toString().getBytes("utf-8");
            // 获得输出�?
            OutputStream out = new DataOutputStream(con.getOutputStream());
            // 输出表头
            out.write(head);
            // 文件正文部分
            // 把文件已流文件的方式 推入到url�?
            DataInputStream in = new DataInputStream(file.getInputStream());
            int bytes = 0;
            byte[] bufferOut = new byte[1024];
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            in.close();
            // 结尾部分
            byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义�?后数据分隔线
            out.write(foot);
            out.flush();
            out.close();

            if (con.getResponseCode() == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = con.getInputStream();
                BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                bufferedInputStream.close();
                inputStream.close();
                con.disconnect();
                return outputStream.toByteArray();
            } else {
                log.error("日志含义接收流数据失败:{}", con.getResponseCode());
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 调用三方接口的excel
     *
     * @param url        地址
     * @param postString 拼接参数
     * @return 字节流
     */
    public static byte[] downloadStreamFromUrl(String url, String postString) {
        HttpURLConnection conn = null;
        try {
            URL postUrl = new URL(url);
            conn = (HttpURLConnection) postUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            conn.setConnectTimeout(30000);
            conn.setReadTimeout(30000);
            OutputStream os = conn.getOutputStream();
            os.write(postString.getBytes("utf-8"));
            os.flush();
            os.close();
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = conn.getInputStream();
                BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                bufferedInputStream.close();
                inputStream.close();
                conn.disconnect();
                return outputStream.toByteArray();
            } else {
                log.error("日志含义接收流数据失败:{}", conn.getResponseCode());
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 对http请求进行基本设置
     *
     * @param httpRequestBase http请求
     */
    private static void setRequestConfig(HttpRequestBase httpRequestBase) {
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(CONNECT_TIMEOUT)
                .setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();

        httpRequestBase.setConfig(requestConfig);
    }

    public static CloseableHttpClient getHttpClient(String url) {
        String hostName = url.split("/")[2];
        int port = 80;
        if (hostName.contains(":")) {
            String[] args = hostName.split(":");
            hostName = args[0];
            port = Integer.parseInt(args[1]);
        }

        if (httpClient == null) {
            // 多线程下多个线程同时调用getHttpClient容易导致重复创建httpClient对象的问题,所以加上了同步锁
            synchronized (syncLock) {
                if (httpClient == null) {
                    httpClient = createHttpClient(hostName, port);
                    // 开启监控线程,对异常和空闲线程进行关闭
                    monitorExecutor = Executors.newScheduledThreadPool(1);
                    monitorExecutor.scheduleAtFixedRate(new TimerTask() {
                        @Override
                        public void run() {
                            // 关闭异常连接
                            manager.closeExpiredConnections();
                            // 关闭空闲的连接
                            manager.closeIdleConnections(CONNECT_TIMEOUT, TimeUnit.MILLISECONDS);
                            // logger.logger("close expired and idle for over CONNECT_TIMEOUT connection");
                        }
                    }, CONNECT_TIMEOUT, CONNECT_TIMEOUT, TimeUnit.MILLISECONDS);
                }
            }
        }
        return httpClient;
    }

    /**
     * 根据host和port构建httpclient实例
     *
     * @param host 要访问的域名
     * @param port 要访问的端口
     * @return
     */
    public static CloseableHttpClient createHttpClient(String host, int port) {
        ConnectionSocketFactory plainSocketFactory = PlainConnectionSocketFactory.getSocketFactory();
        LayeredConnectionSocketFactory sslSocketFactory = SSLConnectionSocketFactory.getSocketFactory();
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", plainSocketFactory).register("https", sslSocketFactory).build();

        manager = new PoolingHttpClientConnectionManager(registry);
        // 设置连接参数
        manager.setMaxTotal(MAX_CONN); // 最大连接数
        manager.setDefaultMaxPerRoute(Max_PRE_ROUTE); // 路由最大连接数

        HttpHost httpHost = new HttpHost(host, port);
        manager.setMaxPerRoute(new HttpRoute(httpHost), MAX_ROUTE);

        // 请求失败时,进行请求重试
        HttpRequestRetryHandler handler = new HttpRequestRetryHandler() {
            @Override
            public boolean retryRequest(IOException e, int i, HttpContext httpContext) {
                if (i > 3) {
                    // 重试超过3次,放弃请求
                    log.info("retry has more than 3 time, give up request");
                    return false;
                }
                if (e instanceof NoHttpResponseException) {
                    // 服务器没有响应,可能是服务器断开了连接,应该重试
                    log.info("receive no response from server, retry");
                    return true;
                }
                if (e instanceof SSLHandshakeException) {
                    // SSL握手异常
                    log.info("SSL hand shake exception");
                    return false;
                }
                if (e instanceof InterruptedIOException) {
                    // 超时
                    log.info("InterruptedIOException");
                    return false;
                }
                if (e instanceof UnknownHostException) {
                    // 服务器不可达
                    log.info("server host unknown");
                    return false;
                }
                if (e instanceof ConnectTimeoutException) {
                    // 连接超时
                    log.info("Connection Time out");
                    return false;
                }
                if (e instanceof SSLException) {
                    log.info("SSLException");
                    return false;
                }

                HttpClientContext context = HttpClientContext.adapt(httpContext);
                HttpRequest request = context.getRequest();
                if (!(request instanceof HttpEntityEnclosingRequest)) {
                    // 如果请求不是关闭连接的请求
                    return true;
                }
                return false;
            }
        };

        CloseableHttpClient client = HttpClients.custom().setConnectionManager(manager).setRetryHandler(handler)
                .build();
        return client;
    }

    public static String postString(String url, String params, String content_type, String authorization,
                                    String Wechatpay_Serial) {
        String result = null;
        HttpPost httpPost = new HttpPost(url);
        setRequestConfig(httpPost);
        if (content_type == null || "".equals(content_type)) {
            content_type = "application/x-www-form-urlencoded; charset=UTF-8";
            httpPost.setHeader("Accept", "*/*");
        } else if ("xml".equals(content_type)) {
            content_type = "application/xml; charset=UTF-8";
            httpPost.setHeader("Accept", "application/xml");
        } else if ("json".equals(content_type)) {
            content_type = "application/json; charset=utf-8";
            httpPost.setHeader("Accept", "application/json");
        }

        httpPost.setHeader("Content-Type", content_type);

        if (Wechatpay_Serial != null && !"".equals(Wechatpay_Serial)) {
            httpPost.setHeader("Wechatpay-Serial", Wechatpay_Serial);
        }
        if (authorization != null && !"".equals(authorization)) {
            httpPost.setHeader("Authorization", authorization);
        }

        /*
         * Header[] header1 = httpPost.getAllHeaders(); for(int
         * i=0;i<header1.length;i++){ Header ha = header1[i];
         * logger.logger(ha.getName()+":"+ha.getValue()); }
         */
        String charSet = "UTF-8";

        CloseableHttpResponse response = null;
        try {
            StringEntity entity = new StringEntity(params, charSet);
            // 必须设置，部分服务端接收时，需要特别再次设置UTF-8
            entity.setContentEncoding(charSet);
            httpPost.setEntity(entity);

            CloseableHttpClient client = getHttpClient(url);

            response = client.execute(httpPost, HttpClientContext.create());
            /*
             * Header[] header = response.getAllHeaders(); for(int i=0;i<header.length;i++){
             * Header ha = header[i]; logger.logger(ha.getName()+","+ha.getValue()); }
             */
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                result = EntityUtils.toString(responseEntity, charSet);
            }
        } catch (IOException e) {
            log.info("读取超时异常：" + url + "," + params);
            e.printStackTrace();
        } finally {
            try {
                if (response != null)
                    response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }


    /**
     * string请求
     *
     * @param url
     * @param params
     * @param content_type
     * @return
     */
    public static String postString(String url, String params, String content_type) {
        String result = null;
        HttpPost httpPost = new HttpPost(url);
        setRequestConfig(httpPost);
        if (content_type == null || "".equals(content_type)) {
            content_type = "application/x-www-form-urlencoded; charset=UTF-8";
        } else if ("xml".equals(content_type)) {
            content_type = "application/xml; charset=UTF-8";
        } else if ("json".equals(content_type)) {
            content_type = "application/json; charset=UTF-8";
        }

        httpPost.setHeader("Content-Type", content_type);
        // httpPost.setHeader("Content-Length", params.length()+"");
        /*
         * Header[] header1 = httpPost.getAllHeaders(); for(int
         * i=0;i<header1.length;i++){ Header ha = header1[i];
         * logger.logger(ha.getName()+":"+ha.getValue()); }
         */
        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;
        try {
            response = getHttpClient(url).execute(httpPost, HttpClientContext.create());
            /*
             * Header[] header = response.getAllHeaders(); for(int i=0;i<header.length;i++){
             * Header ha = header[i]; logger.logger(ha.getName()+","+ha.getValue()); }
             */
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                result = EntityUtils.toString(responseEntity, charSet);
            }
        } catch (IOException e) {
            log.error("读取超时异常:" + url + "," + params);
            e.printStackTrace();
        } finally {
            try {
                if (response != null)
                    response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static String AccessUrlJson(String url, String postString) {
        String result = null;
        HttpURLConnection conn = null;
        try {
            URL postUrl = new URL(url);
            conn = (HttpURLConnection) postUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            conn.setConnectTimeout(30000);
            conn.setReadTimeout(30000);
            OutputStream os = conn.getOutputStream();
            os.write(postString.getBytes("utf-8"));
            os.flush();
            os.close();

            int code = conn.getResponseCode();
            conn.getHeaderFields();
            conn.getContentLength();
            if (code == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                result = buffer.toString();
            } else {
                log.info("返回失败:" + code);
            }
        } catch (MalformedURLException e) {
            conn.disconnect();
            e.printStackTrace();
        } catch (IOException e) {
            conn.disconnect();
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        conn.disconnect();
        return result;
    }


    /**
     * string请求
     *
     * @param url
     * @param content_type
     * @param authorization
     * @param Wechatpay_Serial
     * @return
     */
    public static String getString(String url, String content_type, String authorization, String Wechatpay_Serial) {
        String result = null;
        HttpGet httpGet = new HttpGet(url);
        setRequestConfig(httpGet);
        if (content_type == null || "".equals(content_type)) {
            content_type = "application/x-www-form-urlencoded; charset=UTF-8";
            httpGet.setHeader("Accept", "*/*");
        } else if ("xml".equals(content_type)) {
            content_type = "application/xml; charset=UTF-8";
            httpGet.setHeader("Accept", "application/xml");
        } else if ("json".equals(content_type)) {
            content_type = "application/json; charset=utf-8";
            httpGet.setHeader("Accept", "application/json");
        }

        httpGet.setHeader("Content-Type", content_type);

        if (Wechatpay_Serial != null && !"".equals(Wechatpay_Serial)) {
            httpGet.setHeader("Wechatpay-Serial", Wechatpay_Serial);
        }
        if (authorization != null && !"".equals(authorization)) {
            httpGet.setHeader("Authorization", authorization);
        }

        CloseableHttpResponse response = null;
        try {
            CloseableHttpClient client = getHttpClient(url);

            response = client.execute(httpGet, HttpClientContext.create());

            HttpEntity responseEntity = response.getEntity();

            if (responseEntity != null) {
                result = EntityUtils.toString(responseEntity, "UTF-8");
            }
        } catch (IOException e) {
            log.info("读取超时异常：" + url);
            e.printStackTrace();
        } finally {
            try {
                if (response != null)
                    response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 上传图片瑞银信
     *
     * @param urlStr      上传地址
     * @param textMap     文本集
     * @param fileMap     图片集
     * @param contentType 没有传入文件类型默认采用application/octet-stream
     *                    contentType非空采用filename匹配默认的图片类型
     * @return 返回response数据
     */
    public static JSONObject formUploadForRyx(String urlStr, Map<String, Object> textMap, Map<String, Object> fileMap,
                                              String contentType) {
        JSONObject res = new JSONObject();
        HttpURLConnection conn = null;
        // boundary就是request头和上传文件内容的分隔符
        String BOUNDARY = "---------------------------";
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(10000);
            conn.setReadTimeout(30000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            conn.setRequestProperty("Accept", "*/*");

            OutputStream out = new DataOutputStream(conn.getOutputStream());
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                log.info(strBuf.toString());
                out.write(strBuf.toString().getBytes("UTF-8"));
            }
            // file
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    InputStream inputValue = (InputStream) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    String filename = inputName + ".png";

                    // 没有传入文件类型，同时根据文件获取不到类型，默认采用application/octet-stream
                    // contentType非空采用filename匹配默认的图片类型
                    if (!"".equals(contentType)) {
                        if (filename.endsWith(".png")) {
                            contentType = "image/png";
                        } else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg") || filename.endsWith(".jpe")) {
                            contentType = "image/jpeg";
                        } else if (filename.endsWith(".gif")) {
                            contentType = "image/gif";
                        } else if (filename.endsWith(".ico")) {
                            contentType = "image/image/x-icon";
                        }
                    }
                    if (contentType == null || "".equals(contentType)) {
                        contentType = "application/octet-stream";
                    }
                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
                    String name = "files";
                    if ("cardPic".equals(inputName)) {
                        name = "cardPic";
                    } else if ("autPic".equals(inputName)) {
                        name = "autPic";
                    }
                    strBuf.append(
                            "Content-Disposition: form-data; name=\"" + name + "\"; filename=\"" + filename + "\"\r\n");
                    strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
                    log.info(strBuf.toString());
                    out.write(strBuf.toString().getBytes("UTF-8"));
                    DataInputStream in = new DataInputStream(inputValue);
                    int bytes = 0;
                    byte[] bufferOut = new byte[1024];
                    while ((bytes = in.read(bufferOut)) != -1) {
                        out.write(bufferOut, 0, bytes);
                    }
                    in.close();
                }
            }
            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();

            int code = conn.getResponseCode();
            log.info("ruiyinxin result code:" + code);
            String result = null;
            if (code == 200) {
                // 读取返回数据
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                result = buffer.toString();
                reader.close();
                reader = null;
            }
            res.put("result", result);
            res.put("code", code);

        } catch (Exception e) {
            log.info("发送POST请求出错。" + urlStr);
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return res;
    }

    public static String doPost(String url, String objectJson) throws IOException {
        StringBuilder urlPath = new StringBuilder(url);
        //请求对应接口
        BufferedReader reader = null;
        BufferedWriter writer = null;
        try {
            //创建连接
            System.out.println("请求路径为：" + urlPath);
            URL urlCon = new URL(urlPath.toString());
            HttpURLConnection httpCon = (HttpURLConnection) urlCon.openConnection();
            //设置连接的基本信息
            //请求方式为Post
            httpCon.setRequestMethod("POST");
            //设置通用的请求属性
            httpCon.setRequestProperty("accept", "*/*");
            httpCon.setRequestProperty("connection", "Keep-Alive");
            httpCon.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1)");
            httpCon.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            //是否可读写
            httpCon.setDoOutput(true);
            httpCon.setDoInput(true);
            //是否使用缓存
            httpCon.setUseCaches(false);
            //设置连接超时60s
            httpCon.setConnectTimeout(60000);
            //设置读取响应超时60s
            httpCon.setReadTimeout(60000);
            //正式连接
            httpCon.connect();

            //在这里传递对象的json
            writer = new BufferedWriter(new OutputStreamWriter(httpCon.getOutputStream()));
            //发送请求参数即数据
            writer.write(objectJson);
            //flush输出流的缓冲
            writer.flush();

            StringBuilder result = new StringBuilder();
            reader = new BufferedReader(new InputStreamReader(httpCon.getInputStream()));
            String temp = "";
            while ((temp = reader.readLine()) != null) {
                result.append(temp);
            }
            httpCon.disconnect();
            System.out.println("返回数据为：" + result);
            return result.toString();
        } catch (IOException exception) {
            exception.printStackTrace();
        } finally {
            if (reader != null) reader.close();
            if (writer != null) writer.close();
        }
        return "遇到未知错误";
    }


    /**
     * 随行付上传图片
     * httpclient上传文件
     *
     * @return
     */
    public static String formSxfTqUploadHttpClient(String uploadUrl, String orgIdz, String pictureTypez,
                                                   InputStream fis, String filename) {
        String PhotoUrl = "";
        String result = "";
        // String uploadUrl = "http://172.16.138.162:5041/merchant/uploadPicture";
        CloseableHttpClient httpclient = null;
        try {
            SSLConnectionSocketFactory sslsf = getSSLConnectionSocketFactory();
            httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
            HttpPost httppost = new HttpPost(uploadUrl);

            /* FileBody bin = new FileBody(new File(image)); */
            StringBody orgId = new StringBody(orgIdz, ContentType.TEXT_PLAIN);
            StringBody pictureType = new StringBody(pictureTypez, ContentType.TEXT_PLAIN);
            StringBody reqId = new StringBody(UUID.randomUUID().toString().replace("-", ""),
                    ContentType.TEXT_PLAIN);
            HttpEntity reqEntity = MultipartEntityBuilder.create()
                    .addPart("file", new InputStreamBody(fis, filename)).addPart("orgId", orgId)
                    .addPart("pictureType", pictureType).addPart("reqId", reqId).build();

            httppost.setEntity(reqEntity);
            log.info("executing request " + httppost.getRequestLine());
            CloseableHttpResponse response = httpclient.execute(httppost);
            try {
                log.info("----------------------------------------");
                log.info("{}", response.getStatusLine());
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, "UTF-8");
                    /*
                     * JSONObject resultJson = new JSONObject(result); if
                     * ("0000".equals(resultJson.getString("code"))) { JSONObject respJson =
                     * resultJson.getJSONObject("respData"); String bizCode =
                     * respJson.getString("bizCode"); if("0000".equals(bizCode)){ PhotoUrl =
                     * respJson.getString("PhotoUrl"); } }
                     */
                    log.info("返回结果：" + result);
                } else {
                    log.error("上传图片异常");
                }
                EntityUtils.consume(resEntity);
            } finally {
                response.close();
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        } finally {
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }


    /**
     * 创建没有证书的SSL链接工厂类
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyStoreException
     * @throws KeyManagementException
     */
    public static SSLConnectionSocketFactory getSSLConnectionSocketFactory()
            throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        SSLContextBuilder context = new SSLContextBuilder().useProtocol("TLSv1.2");
        context.loadTrustMaterial(null, new TrustStrategy() {
            @Override
            public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                return true;
            }
        });
        return new SSLConnectionSocketFactory(context.build());
    }


    /**
     * 发送post请求
     * @param json
     * @param URL
     * @return
     */
    public static String sendPost(String URL, String json) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(URL);
        post.setHeader("Content-Type", "application/json");
        String result;
        try {
            StringEntity s = new StringEntity(json.toString(), "utf-8");
            s.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
            post.setEntity(s);
            // 发送请求
            HttpResponse httpResponse = client.execute(post);
            // 获取响应输入流
            InputStream inStream = httpResponse.getEntity().getContent();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inStream, "utf-8"));
            StringBuilder strber = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null)
                strber.append(line + "\n");
            inStream.close();
            result = strber.toString();
            if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                System.out.println("请求服务器成功，做相应处理");
            } else {
                System.out.println("请求服务端失败");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

}