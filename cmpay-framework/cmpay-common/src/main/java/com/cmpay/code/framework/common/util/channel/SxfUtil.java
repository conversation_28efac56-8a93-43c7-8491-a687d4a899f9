package com.cmpay.code.framework.common.util.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.framework.common.constant.SxfConstant;
import com.cmpay.code.framework.common.pojo.SxfResponse;
import com.cmpay.code.framework.common.util.HttpUrlConnectionToInterface;
import com.cmpay.code.framework.common.util.id.IdUtil;
import com.cmpay.code.framework.common.util.string.StrUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * author suohongbo
 * date 2023/12/8 10:32
 * version 1.0
 * 随行付工具类
 */
@Slf4j
public class SxfUtil {

    /**
     * 批量上传随行付图片
     *
     * @param orgNo           随行付机构号
     * @param imageCode       随行付图片代码
     * @param imageNamePrefix 上传图片名称
     * @param files           文件流集合
     * @return
     */
    public static List<String> uploadSxfImage(String orgNo, Integer imageCode, String imageNamePrefix, List<InputStream> files) {
        List<String> imageUrls = new ArrayList<>();
        for (int i = 0; i < files.size(); i++) {
            String imageName = imageNamePrefix + (i + 1) + ".png";
            try {
                InputStream inputStream = files.get(i);
                String responseStr = HttpUrlConnectionToInterface.formSxfTqUploadHttpClient(SxfConstant.SXF_MERCHANT_GET_FILE_RELATIVE_URL, orgNo, String.valueOf(imageCode), inputStream, imageName);
                JSONObject responseJson = JSON.parseObject(responseStr);
                if (responseJson.containsKey("code") && StrUtils.isSame(responseJson.getString("code"), "0000")) {
                    JSONObject respData = responseJson.getJSONObject("respData");
                    if (respData.containsKey("bizCode") && StrUtils.isSame(respData.getString("bizCode"), "0000")) {
                        imageUrls.add(respData.getString("PhotoUrl"));
                    }
                }

            } catch (Exception e) {
                log.error("upload sxf image error:{},{},{}", orgNo, imageCode, imageNamePrefix);
            }

        }

        return imageUrls;
    }


    /**
     * 随行付公用参数及加密
     *
     * @param reqData
     * @param sxfOrgKey
     * @param sxfOrgId
     * @return
     */
    /*
     * private static String getSxfPostStr(JSONObject reqData, String sxfOrgId,
     * String sxfOrgKey) { String reqId = getRandom(30); SimpleDateFormat sdf = new
     * SimpleDateFormat("yyyyMMddHHmmss"); String time = sdf.format(new Date());
     * Map<String,String> map = new HashMap<String,String>(); map.put("orgId",
     * sxfOrgId); map.put("reqId", reqId); map.put("version", "1.0");
     * map.put("timestamp", time); map.put("reqData", reqData.toString());
     * map.put("signType", "RSA");
     *
     * String sign = parseSxfRSASign(map,sxfOrgKey); map.clear();
     *
     * JSONObject json = new JSONObject(); try { json.put("orgId", sxfOrgId);
     * json.put("reqId", reqId); json.put("version", "1.0"); json.put("timestamp",
     * time); json.put("reqData", reqData.toString()); json.put("sign", sign);
     * json.put("signType", "RSA"); } catch (JSONException e) { e.printStackTrace();
     * } return json.toString(); }
     */
    public static String getSxfPostStr(JSONObject reqData, String sxfOrgId, String sxfOrgKey) {
        String reqId = IdUtil.getRandom(30);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String time = sdf.format(new Date());
        Map<String, String> map = new HashMap<String, String>();
        map.put("orgId", sxfOrgId);
        map.put("reqId", reqId);
        map.put("version", "1.0");
        map.put("timestamp", time);
        map.put("reqData", reqData.toString());
        map.put("signType", "RSA");

        String sign = parseSxfRSASign(map, sxfOrgKey);
        map.clear();

        JSONObject json = new JSONObject();
        try {
            json.put("orgId", sxfOrgId);
            json.put("reqId", reqId);
            json.put("version", "1.0");
            json.put("timestamp", time);
            json.put("reqData", reqData.toString());
            json.put("sign", sign);
            json.put("signType", "RSA");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return json.toString();
    }


    /**
     * 随行付rsa加密
     *
     * @param map
     * @param sxfOrgKey
     * @return
     */
    private static String parseSxfRSASign(Map<String, String> map, String sxfOrgKey) {
        // 获取sign
        Collection<String> keyset = map.keySet();
        List<String> list = new ArrayList<String>(keyset);
        Collections.sort(list);
        String signStr = "";
        for (int i = 0; i < list.size(); i++) {
            signStr += list.get(i) + "=" + map.get(list.get(i));
            if (i < list.size() - 1) {
                signStr += "&";
            }
        }

        String sign = null;
        try {
            PrivateKey privateKey = stringToPrivateKey(sxfOrgKey);

            Signature signature = Signature.getInstance("SHA1WithRSA");
            signature.initSign(privateKey);
            signature.update(signStr.getBytes("UTF-8"));

            byte[] signed = signature.sign();
            sign = Base64.getEncoder().encodeToString(signed);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (SignatureException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return sign;
    }

    /**
     * rsa私钥字符串转PrivateKey
     *
     * @param key
     * @return
     */
    private static PrivateKey stringToPrivateKey(String key) {
        PrivateKey privateKey = null;
        try {
            byte[] keyBytes = Base64.getDecoder().decode(key);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            privateKey = keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        }
        return privateKey;
    }


    /**
     * 解析随行付响应参数
     *
     * @param responseData
     * @return
     */
    public static SxfResponse<JSONObject> parsingParameters(String responseData) {
        try {
            String message = null;
            JSONObject responseJson = JSON.parseObject(responseData);
            // 如果不包含code，或不等于0000，或者不包含respData
            if (!responseJson.containsKey("code") || StrUtils.isNotSame(responseJson.getString("code"), SxfConstant.SUCCESS_CODE) || !responseJson.containsKey("respData")) {
                if (responseJson.containsKey("msg")) {
                    message = responseJson.getString("msg");
                }
                return SxfResponse.fail(message, null);
            }
            JSONObject respData = responseJson.getJSONObject("respData");
            // 如果不包含bizCode，或者不等于0000
            if (!respData.containsKey("bizCode") || StrUtils.isNotSame(respData.getString("bizCode"), SxfConstant.SUCCESS_BIZCODE)) {
                if (respData.containsKey("bizMsg")) {
                    message = respData.getString("bizMsg");
                }
                return SxfResponse.fail(message, null);
            }
            return SxfResponse.success(null, respData);
        } catch (Exception e) {
            return SxfResponse.fail("上游响应异常", null);
        }

    }

}
