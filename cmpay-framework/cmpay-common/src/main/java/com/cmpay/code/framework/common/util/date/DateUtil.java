package com.cmpay.code.framework.common.util.date;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import static com.cmpay.code.framework.common.util.DateTime.DateTimeUtils.DATETIME_PATTERN;
import static com.cmpay.code.framework.common.util.DateTime.DateTimeUtils.UNSIGNED_DATETIME_PATTERN;

/**
 * author suohong<PERSON>
 * date 2023/11/17 16:05
 * version 1.0
 * 日期工具类
 */
public class DateUtil {
    /**
     * 格式：2015-08-11 09:51:53
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 格式：2015-08-11
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 格式：20150811095153
     */
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";


    /**
     * 格式：20231117161920365
     */
    public static final String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";

    /**
     * 格式：231117161920365
     */
    public static final String YYMMDDHHMMSSSSS = "yyMMddHHmmssSSS";

    /**
     * 格式 20231111
     */
    public static final String YYYYMMDD = "yyyyMMdd";


    /**
     * 格式：2015-08-11 09:51:53.
     */
    public static final SimpleDateFormat YYYY_MM_DD_HH_MM_SS_FORMAT = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);

    /**
     * 格式：2015-08-11.
     */
    public static final SimpleDateFormat YYYY_MM_DD_FORMAT = new SimpleDateFormat(YYYY_MM_DD);

    /**
     * 格式：20150811095153
     */
    public static final SimpleDateFormat YYYYMMDDHHMMSS_FORMAT = new SimpleDateFormat(YYYYMMDDHHMMSS);


    /**
     * 格式：20231117161920365
     */
    public static final SimpleDateFormat YYYYMMDDHHMMSSSSS_FORMAT = new SimpleDateFormat(YYYYMMDDHHMMSSSSS);


    /**
     * 格式：231117161920365
     */
    public static final SimpleDateFormat YYMMDDHHMMSSSSS_FORMAT = new SimpleDateFormat(YYMMDDHHMMSSSSS);

    /**
     * 格式：20231111
     */
    public static final SimpleDateFormat YYYYMMDD_FORMAT = new SimpleDateFormat(YYYYMMDD);

    /**
     * 显示年月日时分秒，例如 2015-08-11 09:51:53.
     */
    public static final SimpleDateFormat DATETIME_PATTERN_FORMAT = new SimpleDateFormat(DATETIME_PATTERN);

    /**
     * 显示年月日时分秒(无符号)，例如 20150811095153.
     */
    public static final SimpleDateFormat UNSIGNED_DATETIME_PATTERN_FORMAT = new SimpleDateFormat(UNSIGNED_DATETIME_PATTERN);


    /**
     * 获取某一天的结束时间
     */
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取当天开始时间
     */
    public static Date getDayBegin() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }


    // 获取指定日期的开始时间
    public static Date getDesignationBeginDay(int i) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayBegin());
        cal.add(Calendar.DAY_OF_MONTH, i);
        return cal.getTime();
    }


    /**
     * 获取从月初到当前日期的前一天（不包含当前日期）的每一天的开始时间（午夜0点）的字符串表示
     */
    public static List<String> getDaysFromBeginningOfMonthToYesterdayAsString() {
        List<String> dateStrings = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取当前月的第一天
        LocalDate firstDayOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 遍历从月初到前一天的每一天（不包括今天）
        for (LocalDate date = firstDayOfMonth; !date.isEqual(today); date = date.plusDays(1)) {
            // 获取每一天的开始时间（午夜0点）
            LocalDateTime startTime = date.atStartOfDay();
            // 将LocalDateTime转换为字符串
            String dateTimeString = startTime.format(formatter);
            // 将字符串添加到列表中
            dateStrings.add(dateTimeString);
        }
        return dateStrings;
    }

    /**
     * 获取本周截止到今天（不包含今天）每一天的开始时间的字符串表示，并返回List集合
     */
    public static List<String>  getBeginningOfWeekDaysToStringExcludingToday() {
        List<String> dateStrings = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取本周的第一天（周一）
        LocalDate firstDayOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 遍历从本周第一天到今天前一天的每一天
        for (LocalDate date = firstDayOfWeek; !date.isEqual(today); date = date.plusDays(1)) {
            // 获取每一天的开始时间（午夜0点）
            LocalDateTime startTime = date.atStartOfDay();
            // 将LocalDateTime转换为字符串
            String dateTimeString = startTime.format(formatter);
            // 将字符串添加到列表中
            dateStrings.add(dateTimeString);
        }
        return dateStrings;
    }

    /**
     * 获取昨天的开始时间（即昨天的00:00:00时刻）。
     */
    public static String getYesterdayStartTime() {
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 设置时间为当天的开始时间（00:00:00）
        LocalDateTime startTime = yesterday.atStartOfDay();
        // 格式化日期时间为字符串
        return startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static boolean isCurrentTimeAfterDays(int days, LocalDateTime dateTime) {
        LocalDateTime futureDateTime = dateTime.plusDays(days);
        return LocalDateTime.now().isAfter(futureDateTime);
    }

    public static boolean isFutureTime(String timeStr) {
        try {
            // 定义时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 将字符串解析为 LocalDateTime
            LocalDateTime inputTime = LocalDateTime.parse(timeStr, formatter);

            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 比较时间，返回 true 如果传入时间晚于当前时间
            return inputTime.isAfter(currentTime);
        } catch (DateTimeParseException e) {
            // 如果时间字符串格式不正确，记录错误并返回 false
            return false;
        }
    }


}
