package com.cmpay.code.framework.common.util.device;

import com.opencsv.CSVWriter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

import static com.cmpay.code.framework.common.exception.GlobalErrorCodePc.EXPORT_CSV_ERROR;
import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2024/4/22 15:44
 * @version: 1.0
 */
@Slf4j
public class DeviceUtil {

    /**
     * 导出数据到CSV文件。
     *
     * @param header   CSV文件的标题行
     * @param datas    包含数据的对象列表
     * @return         生成的CSV文件对象
     */
    public static File exportCsv(String[] header, List<Object> datas) {
        File csvFile = null;
        CSVWriter writer = null;
        try {
            // 创建临时CSV文件
            csvFile = File.createTempFile("temp", ".csv");
            csvFile.deleteOnExit();
            // 使用OutputStreamWriter和FileOutputStream指定UTF-8编码
            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), "UTF-8"));
            writer = new CSVWriter(bw);
            bw.write("\ufeff");
            writer.writeNext(header); // 写入标题行
            // 写入数据行
            for (Object dataObj : datas) {
                String[] row = ((ToCsv) dataObj).toCsvRow();
                writer.writeNext(row);
            }
        } catch (IOException e) {
            // 记录错误日志
            System.err.println("create tempFile csv error: " + e.getMessage());
            // 删除临时文件
            if (csvFile != null && csvFile.exists()) {
                csvFile.delete();
            }
            // 抛出自定义异常
            throw exception(EXPORT_CSV_ERROR);
        } finally {
            // 关闭CSVWriter，确保资源被回收
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    System.err.println("Error closing CSV writer: " + e.getMessage());
                }
            }
        }
        // 返回创建的CSV文件
        return csvFile;
    }

    public static void outCsvStream(HttpServletResponse response, File tempFile, String filename) {
        try (OutputStream out = response.getOutputStream();
             FileInputStream in = new FileInputStream(tempFile)) {

            // 重置响应状态
            response.reset();

            // 设置响应的内容类型
            response.setContentType("application/csv");

            // 使用URL编码对文件名进行编码，以确保在所有浏览器中都正确显示
            String encodedFilename = URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            // 设置响应的Content-Disposition头，使用RFC 5987编码格式
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);

            // 写入UTF-8 BOM以避免Excel打开时的中文乱码问题
            out.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            byte[] buffer = new byte[10240];
            int bytesRead;
            // 读取文件内容并写入响应输出流
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            // 记录异常信息，或者进行其他异常处理
            System.err.println("export csv error: " + e.getMessage());
            throw exception(EXPORT_CSV_ERROR);
        }
    }

    public static boolean deleteFile( File file) {
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
}
