package com.cmpay.code.framework.common.util.file;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2023/12/6 15:18
 * version 1.0
 * 文件工具类
 */
public class FileUtil {


    /**
     * 网络图片获取文件输入流
     *
     * @param link 网络链接
     * @return inputstream   文件输入流
     */
    public static InputStream getLinkInputStream(String link) {
        InputStream inputStream = null;
        HttpURLConnection httpURLConnection = null;
        try {
            URL url = new URL(link);
            httpURLConnection = (HttpURLConnection) url.openConnection();
            // 设置网络连接超时时间
            httpURLConnection.setConnectTimeout(3000);
            // 设置应用程序要从网络连接读取数据
            httpURLConnection.setDoInput(true);
            httpURLConnection.setRequestMethod("GET");
            int responseCode = httpURLConnection.getResponseCode();
            System.out.println("responseCode is:" + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 从服务器返回一个输入流
                inputStream = httpURLConnection.getInputStream();
            } else {
                return null;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return inputStream;
    }


    /**
     * link地址批量转换为MultipartFile
     *
     * @param links 网络图片地址集合
     * @return 输入流集合
     */
    public static List<InputStream> linkConvertMultipartFileList(List<String> links) {
        List<InputStream> inputStreams = new ArrayList<>();
        for (String s : links) {
            InputStream inputStream = getLinkInputStream(s);
            if (!Objects.isNull(inputStream)) {
                inputStreams.add(inputStream);
            }
        }
        return inputStreams;
    }

    /**
     * 判断一个文件类型是否为空
     *
     * @param file
     * @return
     */
    public static boolean isNullFile(MultipartFile file) {
        if (ObjectUtils.isEmpty(file) || file.getSize() <= 0) {
            return true;
        }
        return false;
    }



}
