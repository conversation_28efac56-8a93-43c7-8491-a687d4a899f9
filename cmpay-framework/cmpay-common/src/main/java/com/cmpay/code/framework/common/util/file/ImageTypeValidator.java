package com.cmpay.code.framework.common.util.file;

import com.cmpay.code.framework.common.enums.ImageType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 必须是图片校验类
 */
public class ImageTypeValidator implements ConstraintValidator<ImageType, MultipartFile> {


    @Override
    public void initialize(ImageType constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(MultipartFile multipartFile, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(multipartFile) || StringUtils.isEmpty(multipartFile.getOriginalFilename())) {
            return false;
        } else {
            return MultipartFileUtil.verifyType(multipartFile);
        }
    }
}
