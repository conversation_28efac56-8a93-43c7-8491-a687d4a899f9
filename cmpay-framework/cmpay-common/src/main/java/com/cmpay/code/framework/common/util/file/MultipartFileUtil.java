package com.cmpay.code.framework.common.util.file;

import com.cmpay.code.framework.common.constant.FileConstant;
import com.cmpay.code.framework.common.util.string.StrUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2023/12/6 15:52
 * version 1.0
 */

public class MultipartFileUtil {
    private final static Integer FILE_SIZE = 5;//文件上传限制大小
    private final static String FILE_UNIT = "M";//文件上传限制单位（B,K,M,G）
    private static final String FILE_PNG = ".png";
    private static final String FILE_PNG_MAX = ".png";
    private static final String FILE_JPG = ".jpg";
    private static final String FILE_JPG_MAX = ".JPG";
    private static final String FILE_JPEG = ".jpeg";
    private static final String FILE_XLS = ".xls";
    private static final String FILE_XLSX = ".xlsx";

    /**
     * @param len  文件长度
     * @param size 限制大小
     * @param unit 限制单位（B,K,M,G）
     * @描述 判断文件大小
     */
    public static boolean checkFileSize(Long len, int size, String unit) {
        double fileSize = 0;
        if ("B".equalsIgnoreCase(unit)) {
            fileSize = (double) len;
        } else if ("K".equalsIgnoreCase(unit)) {
            fileSize = (double) len / 1024;
        } else if ("M".equalsIgnoreCase(unit)) {
            fileSize = (double) len / 1048576;
        } else if ("G".equalsIgnoreCase(unit)) {
            fileSize = (double) len / 1073741824;
        }
        return !(fileSize > size);
    }

    // 校验大小
    public static boolean verifySize(MultipartFile file) {
        if (Objects.isNull(file)) {
            return false;
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return false;
        }
        return checkFileSize(file.getSize(), FILE_SIZE, FILE_UNIT);
    }

    // 校验类型
    public static boolean verifyType(MultipartFile file) {
        if (Objects.isNull(file)) {
            return false;
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return false;
        }
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
        return !StrUtils.isNotSame(FILE_PNG, fileType)
                || !StrUtils.isNotSame(FILE_JPG, fileType)
                || !StrUtils.isNotSame(FILE_JPEG, fileType)
                || !StrUtils.isNotSame(FILE_JPG_MAX, fileType)
                || !StrUtils.isNotSame(FILE_PNG_MAX, fileType);
    }

    /**
     * 校验指定类型
     *
     * @param file 文件
     * @param type 文件类型     excel  word  jpg
     * @return 是否符合要求
     */
    public static boolean checkFileType(MultipartFile file, String type) {
        if (Objects.isNull(file)) {
            return false;
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return false;
        }
        String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
        // 如果是excel类型
        if (StrUtils.isSame(type, FileConstant.EXCEL_TYPE)) {
            return StrUtils.isSame(FILE_XLS, fileType) || StrUtils.isSame(FILE_XLSX, fileType);
        }
        return false;
    }
}
