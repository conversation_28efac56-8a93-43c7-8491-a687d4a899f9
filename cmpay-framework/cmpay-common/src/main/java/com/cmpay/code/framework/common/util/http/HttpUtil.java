package com.cmpay.code.framework.common.util.http;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.framework.common.util.HttpUrlConnectionToInterface;
import com.cmpay.code.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * author suoh<PERSON><PERSON>
 * date 2023/12/18 17:41
 * version 1.0
 */
@Slf4j
public class HttpUtil {

    /**
     * 回调专用方法
     *
     * @param url    请求地址
     * @param o      请求参数
     * @param remark 备注
     * @return string 返回结果
     */
    public static String postJsonCallback(String url, Object o, String remark) {
        JSONObject callbackRequestJson = JsonUtils.parseObjectToClass(o, JSONObject.class);
        log.info("request com cmpay code framework common util http httputil callback:{},{},{}", remark, url, callbackRequestJson);
        String responseCallbackStr = HttpUrlConnectionToInterface.postString(url, callbackRequestJson.toString(), "json", null, null);
        log.info("response com cmpay code framework common util http httputil callback:{},{},{},{}", remark, url, callbackRequestJson, responseCallbackStr);
        return responseCallbackStr;
    }
}
