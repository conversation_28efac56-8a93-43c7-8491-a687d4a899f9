package com.cmpay.code.framework.common.util.id;

import com.cmpay.code.framework.common.util.date.DateUtil;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Date;
import java.util.Random;


/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2023/11/17 16:15
 * version 1.0
 * 生成各种id的工具类
 */
public class IdUtil {


    private final static char[] digits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e',
            'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U',
            'V', 'W', 'X', 'Y', 'Z'};

    /**
     * 生成定时任务唯一执行单号
     *
     * @return orderId 定时单号
     */
    public static String getScheduledId() {
        return "TASK_" + DateUtil.YYYYMMDDHHMMSSSSS_FORMAT.format(new Date()) + randomNum(3);
    }

    /**
     * 生成定时任务唯一执行单号
     *
     * @return orderId 定时单号
     */
    public static Long getBizId() {
        return Long.parseLong(DateUtil.YYMMDDHHMMSSSSS_FORMAT.format(new Date()) + randomNum(4));
    }


    /**
     * 生成20位id   日期格式
     *
     * @return id
     */
    public static String get20BitId() {
        return DateUtil.YYYYMMDDHHMMSSSSS_FORMAT.format(new Date()) + randomNum(3);
    }


    /**
     * 获取随机商户号
     *
     * @return trueid 短商户号
     */
    public static String getRandomTrueid() {
        return "60" + randomNum(8);
    }


    /**
     * 生成随机位数数字字符串
     */
    public static String randomNum(Integer length) {
        StringBuilder stringBuilder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            int digit = random.nextInt(10);
            stringBuilder.append(digit);
        }
        return stringBuilder.toString();
    }

    /**
     * 获取number位随机字母和数字组成的随机数
     *
     * @return
     */
    public static String getRandom(int number) {
        Random random = new Random();
        char[] cs = new char[number];
        for (int i = 0; i < cs.length; i++) {
            cs[i] = digits[random.nextInt(digits.length)];
        }
        return new String(cs);
    }

    /**
     * 将商户号转换为长商户号
     *
     * @param shopId 商户号
     * @return string  长商户号
     */
    public static String convertShopId(String shopId) {
        if (shopId.length() < 32) {
            shopId = DigestUtils.md5Hex(shopId);
        }
        return shopId;
    }
}
