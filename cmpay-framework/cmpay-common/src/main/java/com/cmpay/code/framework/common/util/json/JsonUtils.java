package com.cmpay.code.framework.common.util.json;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.framework.common.util.md5.MD5Util2;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Year;
import java.util.*;

/**
 * JSON 工具类
 *
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class JsonUtils {

    private static ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.registerModules(new JavaTimeModule()); // 解决 LocalDateTime 的序列化
    }

    /**
     * 初始化 objectMapper 属性
     * <p>
     * 通过这样的方式，使用 Spring 创建的 ObjectMapper Bean
     *
     * @param objectMapper ObjectMapper 对象
     */
    public static void init(ObjectMapper objectMapper) {
        JsonUtils.objectMapper = objectMapper;
    }

    @SneakyThrows
    public static String toJsonString(Object object) {
        return objectMapper.writeValueAsString(object);
    }

    @SneakyThrows
    public static byte[] toJsonByte(Object object) {
        return objectMapper.writeValueAsBytes(object);
    }

    @SneakyThrows
    public static String toJsonPrettyString(Object object) {
        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
    }

    public static <T> T parseObject(String text, Class<T> clazz) {
        if (StrUtil.isEmpty(text)) {
            return null;
        }
        try {
            return objectMapper.readValue(text, clazz);
        } catch (IOException e) {
            log.error("json parse err,json:{}", text, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 将字符串解析成指定类型的对象
     * 使用 {@link #parseObject(String, Class)} 时，在@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS) 的场景下，
     * 如果 text 没有 class 属性，则会报错。此时，使用这个方法，可以解决。
     *
     * @param text  字符串
     * @param clazz 类型
     * @return 对象
     */
    public static <T> T parseObject2(String text, Class<T> clazz) {
        if (StrUtil.isEmpty(text)) {
            return null;
        }
        return JSONUtil.toBean(text, clazz);
    }

    public static <T> T parseObject(byte[] bytes, Class<T> clazz) {
        if (ArrayUtil.isEmpty(bytes)) {
            return null;
        }
        try {
            return objectMapper.readValue(bytes, clazz);
        } catch (IOException e) {
            log.error("json parse err,json:{}", bytes, e);
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String text, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(text, typeReference);
        } catch (IOException e) {
            log.error("json parse err,json:{}", text, e);
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseArray(String text, Class<T> clazz) {
        if (StrUtil.isEmpty(text)) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(text, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("json parse err,json:{}", text, e);
            throw new RuntimeException(e);
        }
    }

    public static JsonNode parseTree(String text) {
        try {
            return objectMapper.readTree(text);
        } catch (IOException e) {
            log.error("json parse err,json:{}", text, e);
            throw new RuntimeException(e);
        }
    }

    public static JsonNode parseTree(byte[] text) {
        try {
            return objectMapper.readTree(text);
        } catch (IOException e) {
            log.error("json parse err,json:{}", text, e);
            throw new RuntimeException(e);
        }
    }

    public static boolean isJson(String text) {
        return JSONUtil.isTypeJSON(text);
    }

    /**
     * 按字母顺序排序map里面的数据，并返回json
     *
     * @param map
     * @param key
     * @return 返回postXml
     */
    public static JSONObject parseSignForJson(Map<String, String> map, String key) {
        // 获取sign
        Collection<String> keyset = map.keySet();
        List<String> list = new ArrayList<String>(keyset);
        Collections.sort(list);
        String signStr = "";
        for (int i = 0; i < list.size(); i++) {
            signStr += list.get(i) + "=" + map.get(list.get(i));
            if (i < list.size() - 1) {
                signStr += "&";
            }
        }
        signStr += "&key=" + key;
        log.info("signStr == >>" + signStr);
        String sign = MD5Util2.encode(signStr).toUpperCase();
        JSONObject rj = new JSONObject();
        try {
            for (int i = 0; i < list.size(); i++) {
                signStr += list.get(i) + "=" + map.get(list.get(i));
                rj.put(list.get(i), map.get(list.get(i)));
            }
            rj.put("sign", sign);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return rj;
    }


    /*
     * @Description:生成应用id和秘钥，id18位，秘钥32位
     *@param
     *@return
     */
    public static synchronized JSONObject generateAppInfo() {
        JSONObject json = new JSONObject();
        int year = Year.now().getValue();//获取当前年份
        String pre = String.valueOf(year);//固定前四位
        int hashCodeV = UUID.randomUUID().toString().hashCode();
        if (hashCodeV < 0) {//有可能是负数
            hashCodeV = -hashCodeV;
        }
        int len = (pre + hashCodeV).length();
        int need_len = 18 - len;
        String random = "" + Math.random();
        String substring = random.substring(random.length() - need_len);
        String appid = pre + hashCodeV + substring;//应用id
        String app_key = MD5Util2.encode(appid);
        app_key = app_key.toUpperCase();
        json.put("appid", appid);
        json.put("app_key", app_key);
        return json;
    }


    /**
     * 将object类型转换为指定类型
     *
     * @param o      object对象
     * @param oClass 转换成的类型
     * @param <T>    转换成的类型
     * @return 抓换成的类型对象
     */
    public static <T> T parseObjectToClass(Object o, Class<T> oClass) {
        String oStr = JSON.toJSONString(o);
        return JSON.parseObject(oStr, oClass);
    }
    /**
     * 按照JSONArray中的对象的某个字段进行正序排序(采用fastJson)
     *
     * @param jsonArrStr json数组字符串
     */
    public static JSONArray jsonArraySortByTimePositiveSequence(JSONArray jsonArr, String key) {
        JSONArray sortedJsonArray = new JSONArray();
        try {
            List<JSONObject> jsonValues = new ArrayList<JSONObject>();
            for (int i = 0; i < jsonArr.size(); i++) {
                jsonValues.add(jsonArr.getJSONObject(i));
            }
            Collections.sort(jsonValues, new Comparator<JSONObject>() {
                String string1;
                String string2;

                @Override
                public int compare(JSONObject a, JSONObject b) {
                    try {
                        string1 = a.getString(key).replaceAll("-", "");
                        string2 = b.getString(key).replaceAll("-", "");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    return -string2.compareTo(string1);
                }
            });
            for (int i = 0; i < jsonValues.size(); i++) {
                sortedJsonArray.add(jsonValues.get(i));
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return sortedJsonArray;

    }
}
