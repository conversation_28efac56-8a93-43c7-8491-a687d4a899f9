package com.cmpay.code.framework.common.util.notice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.framework.common.util.HttpUrlConnectionToInterface;
import com.cmpay.code.framework.common.util.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description: 通知工具类
 * @Date 2024/12/9 11:00
 * @version: 1.0
 */
@Slf4j
public class NoticeUtils {

    // public static String INFORM_URL = "https://test.gdwxyf.com/merchantminiapp/api/notification/save";
    public static String INFORM_URL = "https://manage.gdwxyf.com/merchantminiapp/api/notification/save";

    // 发送通知
    public static void sendNotice(String shop_id, String scene, String validityTime, String notice_type, String display_pos, String display_per, String type_business, JSONObject paramJson) {
        // 获取当前时间
        Date noticeTime = new Date();
        // 发送参数
        JSONObject data = new JSONObject();
        data.put("notice_type", notice_type);// 通知类型
        data.put("display_pos", display_pos);// 通知显示位置
        data.put("display_per", display_per);// 通知显示权限
        data.put("type_business", type_business);// 通知类型
        data.put("notice_time", new SimpleDateFormat(DateUtil.YYYY_MM_DD_HH_MM_SS).format(noticeTime));// 通知时间
        data.put("validity_time", validityTime);// 过期时间
        data.put("scene", scene);// 场景值
        data.put("shop_id", shop_id);// 商户号
        data.put("param_json", paramJson);// 通知内容
        try {
            log.info("send notice req:url{},param:{}", INFORM_URL, data.toJSONString());
            String result = HttpUrlConnectionToInterface.postString(INFORM_URL, data.toJSONString(), "json", null, null);
            log.info("send notice resp:url{},param:{},result:{}", INFORM_URL, data.toJSONString(), result);
        } catch (Exception e) {
            log.error("send notice error:url{},param:{}", INFORM_URL, data.toJSONString(), e);
        }
    }

    // 测试发送通知
    public static void main(String[] args) {
        String url = "https://test.gdwxyf.com/merchantminiapp/api/notification/save";
        // 获取当前时间
        Date noticeTime = new Date();
        String notice_content_ass = "尊敬的商户，您好！感谢您的支持！诚邀您参与问卷调研，帮助我们更好地服务于您。请点击查看详情填写问卷";
        // 自定义通知类型
        JSONObject paramJson = new JSONObject();
        paramJson.put("notice_title", "【邮付小助手优化问卷调查】");
        paramJson.put("notice_content_ass", notice_content_ass);
        paramJson.put("shop_id", "63bb84e78e1c9bd211c614b1e51fd6da");// 商户号
        paramJson.put("appId", "wxebadf544ddae62cb");// 通道
        paramJson.put("path", "pages/survey/index?sid=15111679&hash=202f&navigateBackMiniProgram=true");// 通道
        // 发送参数
        JSONObject data = new JSONObject();
        data.put("notice_type","business");//通知类型
        data.put("display_pos","2");//通知显示位置
        data.put("display_per","1,4");//通知显示权限
        data.put("type_business","10004");//通知类型
        data.put("notice_time", new SimpleDateFormat(DateUtil.YYYY_MM_DD_HH_MM_SS).format(noticeTime));// 通知时间
        data.put("validity_time", "2024-08-31 23:59:59");// 过期时间
        data.put("scene", "7");// 场景值
        data.put("shop_id", "dd65be2353903152376501884a7dc1a4");// 商户号
        data.put("param_json", paramJson);// 通知内容
        log.info("小工具——发送补充瑞银信资料发送通知接口：Url:{}，参数:{}", url, data.toJSONString());
        String result = HttpUrlConnectionToInterface.postString(url, data.toJSONString(), "json", null, null);
        result = result != null ? result : "";
        JSONObject jsonObject = JSON.parseObject(result);
        log.info("=================");
        log.info(jsonObject.toString());
        log.info("=================");
        if(jsonObject==null){
            throw exception(-1, "未获取到通知结果");
        }
    }
}
