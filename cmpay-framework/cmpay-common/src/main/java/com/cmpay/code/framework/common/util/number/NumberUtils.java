package com.cmpay.code.framework.common.util.number;

import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数字的工具类，补全 {@link cn.hutool.core.util.NumberUtil} 的功能
 *
 * <AUTHOR>
 */
public class NumberUtils {

    private final static char[] digits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e',
            'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U',
            'V', 'W', 'X', 'Y', 'Z' };

    public static Long parseLong(String str) {
        return StrUtil.isNotEmpty(str) ? Long.valueOf(str) : null;
    }

    /**
     * 获取支付订单号
     *
     * @return
     */
    public static String getOrderIdNumber() {
        int hashCodeV = UUID.randomUUID().toString().hashCode();
        if (hashCodeV < 0) {
            hashCodeV = -hashCodeV;
        }

        int len = (hashCodeV + "").length();
        int dlen = 20 - len;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        Date date = new Date();
        String str1 = sdf.format(date).substring(0, dlen);
        return str1 + hashCodeV;
    }

    /**
     * 获取number位随机字母和数字组成的随机数
     *
     * @return
     */
    public static String getRandom(int number)
    {
        Random random	= new Random();
        char[]	cs		= new char[number];
        for (int i = 0; i < cs.length; i++)
        {
            cs[i] = digits[random.nextInt(digits.length)];
        }
        return new String(cs);
    }

    /**
     * 获取纯数字随机数
     *
     * @param n
     * @return
     */
    public static String getIntRandom(int n)
    {
        String random = (long) ((Math.random() * 9 * Math.pow(10, n - 1)) + (int) Math.pow(10, n - 1)) + "";
        return random;
    }

    /**
     * 校验是否为手机号
     * @param phone 手机号
     * @return 是否
     */
    public static boolean validatePhoneNumber(String phone) {
        // 定义手机号的正则表达式模式
        String regexPattern = "^1[3456789]\\d{9}$";

        // 创建Pattern对象
        Pattern pattern = Pattern.compile(regexPattern);

        // 创建Matcher对象
        Matcher matcher = pattern.matcher(phone);

        // 判断手机号是否匹配模式
        return matcher.matches();
    }

    /**
     * 获取给定double值的相反数。
     *
     * @param number 给定的double值
     * @return 给定值的相反数
     */
    public static double getOppositeNumber(double number) {
        return -number;
    }

    /**
     * 获取给定double值加上 -
     *
     * @param number 给定的double值
     * @return 给定值加上 -
     */
    public static String getOppositeString(double number) {
        return "-" + number;
    }

    public static String getOppositeString(BigDecimal number) {
        return "-" + number;
    }

    public static String getMerchantPublicOrderId() {
        // 定义时间格式，例如："yyyyMMddHHmmss"
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        // 获取当前时间的时间戳字符串
        String timestamp = dateFormat.format(Calendar.getInstance().getTime());
        return "app_" + timestamp;
    }

    /**
     * 生成批次号：时间+6位随机数
     * @return
     */
    public static String getBatchId(Integer number) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        return sdf.format(date) + NumberUtils.getIntRandom(number);
    }

    /**
     * 将费率转换为百分比并格式化
     * @param rate 费率
     * @return  百分比
     */
    public static String formatPercentage(String rate) {
        double value = Double.parseDouble(rate);
        double result = value / 10;
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        return decimalFormat.format(result);
    }
    public static Double formatDouble(String str) {
        Double number = 0.0;
        try {
            number = Double.valueOf(str.trim()); // 返回Double对象
            System.out.println(number); // 输出：-67.89
        } catch (NumberFormatException e) {
            System.out.println("转换失败！");
        }
        return number;
    }
    /**
     * 字符串运算
     *
     * @param str1 string
     * @param str2 string
     * @param type string add sub mul div
     * @param num  int   保留小数位
     * @return string
     */
    public static Double decimalOperationCustomize(String str1, String str2, String type,int num) {
        str1 = str1.trim();
        str2 = str2.trim();
        double val = 0;
        Pattern pattern = Pattern.compile("^[0.0-9.0]+$");
        if (!pattern.matcher(str1).matches() || !pattern.matcher(str2).matches()) {
            return val;
        }
        Double param1 = new Double(str1);
        Double param2 = new Double(str2);

        if ((param1 == 0 || param2 == 0)&&!"add".equals(type)&&!"sub".equals(type)) {
            return val;
        }
        BigDecimal b1 = new BigDecimal(str1);
        BigDecimal b2 = new BigDecimal(str2);

        if ("add".equals(type)) {
//            加法
            val = b1.add(b2).doubleValue();
        } else if ("sub".equals(type)) {
//            减法
            val = b1.subtract(b2).doubleValue();
        } else if ("mul".equals(type)) {
//            乘法
            val = b1.multiply(b2).setScale(num,BigDecimal.ROUND_HALF_UP).doubleValue();
        } else if ("div".equals(type)) {
//            除法
            val = b1.divide(b2, num, BigDecimal.ROUND_HALF_UP).doubleValue();
        } else {
            return val;
        }
        return val;
    }

}
