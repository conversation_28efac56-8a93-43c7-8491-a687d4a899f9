package com.cmpay.code.framework.common.util.password;

//
public class PasswordUtils {

    public static boolean isValidPassword(String username, String password) {
        // 检查密码是否包含用户名
        if (password.toLowerCase().contains(username.toLowerCase())) {
            return false;
        }

        // 检查密码是否使用连续3个相同字符
        for (int i = 0; i < password.length() - 2; i++) {
            if (password.charAt(i) == password.charAt(i + 1) && password.charAt(i + 1) == password.charAt(i + 2)) {
                return false;
            }
        }

        // 检查密码是否使用标准键盘上连续三个相邻按键
        String keyboardRows = "1234567890qwertyuiopasdfghjklzxcvbnm";
        for (int i = 0; i < password.length() - 2; i++) {
            int firstIndex = keyboardRows.indexOf(password.charAt(i));
            int secondIndex = keyboardRows.indexOf(password.charAt(i + 1));
            int thirdIndex = keyboardRows.indexOf(password.charAt(i + 2));
            if (firstIndex != -1 && secondIndex != -1 && thirdIndex != -1) {
                if (Math.abs(firstIndex - secondIndex) == 1 && Math.abs(secondIndex - thirdIndex) == 1) {
                    return false;
                }
            }
        }

        return true;
    }

}
