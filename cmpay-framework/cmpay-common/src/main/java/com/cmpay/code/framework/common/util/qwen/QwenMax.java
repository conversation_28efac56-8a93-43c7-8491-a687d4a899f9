package com.cmpay.code.framework.common.util.qwen;

import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.generation.models.QwenParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.MessageManager;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.utils.Constants;
import com.cmpay.code.framework.common.constant.QwenConstant;

/**
 * author suohong<PERSON>
 * date 2024/2/28 16:51
 * version 1.0
 */
public class QwenMax {
    public static String callWithMessage(String describe)
            throws NoApiKeyException, ApiException, InputRequiredException {
        Constants.apiKey = QwenConstant.API_KEY;
        Generation gen = new Generation();
        MessageManager msgManager = new MessageManager(10);
        Message systemMsg =
                Message.builder().role(Role.SYSTEM.getValue()).content("你是智能助手机器人").build();
        Message userMsg = Message.builder()
                .role(Role.USER.getValue())
                .content(describe).build();
        msgManager.add(systemMsg);
        msgManager.add(userMsg);
        QwenParam param =
                QwenParam.builder().model(Generation.Models.QWEN_PLUS).messages(msgManager.get())
                        .resultFormat(QwenParam.ResultFormat.MESSAGE)
                        .topP(0.8)
                        .enableSearch(true)
                        .build();
        GenerationResult result = gen.call(param);
        msgManager.add(result);
        String content = result.getOutput().getChoices().get(0).getMessage().getContent().replace("\n", "").replace(" ", "").replace("```", "#");
        int prefixIndex = content.indexOf("{");
        int suffixIndex = content.lastIndexOf("#");
        if(prefixIndex < 0 || suffixIndex < 0){
            return content;
        }
        return content.substring(prefixIndex, suffixIndex);
    }


}
