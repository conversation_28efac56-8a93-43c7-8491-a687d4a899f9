package com.cmpay.code.framework.common.util.qwen;

import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import com.alibaba.dashscope.utils.Constants;
import com.cmpay.code.framework.common.constant.QwenConstant;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author suohongbo
 * date 2024/2/28 16:22
 * version 1.0
 * 通义千问VL
 * 图片 -> 文字描述
 */
public class QwenVL {

//    private static final String modelName = "qwen-vl-plus";
//    private static final String modelName = "qwen-vl-max";

    public static String getImageDescription(List<String> images,String askImage)
            throws ApiException, NoApiKeyException, UploadFileException {
        List<Map<String, Object>> contents = new ArrayList<>();
        for (String image : images) {
            if (StringUtils.isEmpty(image)) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("image", image);
            contents.add(content);
        }
        Map<String, Object> content = new HashMap<>();
        content.put("text", askImage);
        contents.add(content);
        Constants.apiKey = QwenConstant.API_KEY;
        MultiModalConversation conv = new MultiModalConversation();
        MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue())
                .content(contents).build();
        MultiModalConversationParam param = MultiModalConversationParam.builder()
                .model(MultiModalConversation.Models.QWEN_VL_PLUS)
                .message(userMessage)
                .build();
        MultiModalConversationResult result = conv.call(param);
        return String.valueOf(result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text")).replace("\n", "");
    }



}
