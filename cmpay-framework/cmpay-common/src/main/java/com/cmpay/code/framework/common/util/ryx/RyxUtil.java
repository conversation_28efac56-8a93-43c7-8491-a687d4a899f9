package com.cmpay.code.framework.common.util.ryx;


import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.framework.common.util.HttpUrlConnectionToInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;


@Slf4j
public class RyxUtil {

	public static JSONObject ryxRiskManageList(String url, String order_id, String ryx_access_id, String public_key_file_path, String
			private_key_file_path, Map<String,Object> textMap, Map<String,Object> fileMap, String orgId){
		JSONObject rj = new JSONObject();
		String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyzdjDn8NvkO1lrJx5a6F3Q6MfHQN6aEIvKsF2Quf3j4/dHoQZj4anQ5wznGdK3kklAT/CBitfYXk1Sz0ztHCu4q5tVe8tGm6vtOiTpJ/hmOwVpkhKiwszOACyLDiPWJLSfT/Lzm4aj6f+2yzS4yT5NRoB4MYBNRnh8gHGH6oal5MFhjjsxTJYC/pOgpp3+I2wlqO96J//Y8W13wB6dW2YqO7TX2zccca16pd1WcFrI9nNAA8bOKveZfZcMtvumpp5gWISBrzOn+O8/LngzR25on29qnHTFCxM5ecM6ZKZwrAzGL8Xd3XYHdBQYh0LTiWZrKhdI95g9DUb3iVoDO6qwIDAQAB";
		String privateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDYu/qxq81HyRLByUwuobu6urhdHRgFscaCzW6S/qDiIs3BrpKy1Vxf9g/XUTrnVuNnZJNNOM54l0C4qfAHjOEKdWQ2NG43SEqdeaBtu2BbTQ4akghUAeq1h/R9eKCNN4rCzx8+2UvKhmw39yBAiddbujHAaAET5Eod+A/YCu5oiM+msywLIod241LcNBB8/ym3jtRdSjBtUq5wnKghZDuy5jOtyvXG3YLhyRSvH0jQWk8ukX+rwa5btC/NLwOWW52qWY7U3TOtgr5yszxFQ2/X5ngWoHJwjG6PPa15wsA/jaYwSh1FJPk6YgVlTFHFEKoaLXNlWDFh1Vpa/ACR0z19AgMBAAECggEAQTfny1kgb66RB2FUnF+sxxby7YoYtXX2f1vkHzWiIgpJEjQ/DrNnlbTMtYVLDVw3F+DK9pzWfLE/F5KDmTIKcgQ7QJhsZhRJ57M21G5jI3Y/fT1eZjEheLjNmIgZAAWrqs164IWw6eOjYv/pY6JW/NI9sGFbmanDp9sCoYdAkJG0dTOpDvIMWrvQrNe2R9zi5VVIlyvT2FLaNuNX4e992nwyTTLw58iHOLi0uuvjhYuTa2Ty9zHstckqMLn8GXonLNSSsC/fnWDO2NKq6Q+sqdvVG8BhhwJeRew89D5G+FfMKoySz7IvUTiOoJsxujUGMDysTJEbgZ8HBmSFK+s1oQKBgQD/sOa8rx7JkVy+KmKeXr9c45JVfeTW9swL81gVNp7xg8sXU8/IR8JX2DWV78AG2Ou6ValjAgxK2y7VUTI9zY4w7IML7vV2pXNhjWhRyPw/yflhlV/rdyc4VBAdKmYXc0SrUzfzkr8JUsXupJAwdDr9ee9d+VfJNGVutJNKTpexVQKBgQDY/wbOvJEJea04YKfGXvUwHhfPDQgvGo8QwQjtxKCbo//7qPR3E66YIJEMfixNGUJoQfU0iIRwMDVj6PYaQmFIqtY+tidL30FFsLZqcYxQySir6NxZPJze/pavpzBgy/1Rzm6Bcg0OMoMg0vZv1UiufejKfe+pD3ue/cPBBKv7iQKBgQDsEgw20Qh/2fHisYdz6M6NmJ8Wx7Fst1gVAIumqlN9cM0lsH/6ziYkrL5svHpy1/xsgYJuowluY4eN8/WLEiniCbvEXM/Mx10MCF70g7hBvNy+uAdjgxN28kYeZ3Ly273e4GqcPDSdCwssFkv6MJX2SYozHW9rmTC9UYNb68wqMQKBgQDExbZBoIMRCZKHjjZ1z0Qdxxwl+JZUSIw1UCatneFaYcROTC6rEGEBuH5vyraXj3FFo5IcejNGnKC9odIRi+I2CBzRr1JBHglNjepM6fyogVFtWnL+RNWn7Uttq3lWPYTQOxbAMVEfb7Sy/jrUvJ71BF4lyT+bmdurGEj77iWaqQKBgQDxH3k4VqzKcvq9zG4SESIha2Sa1rG7puy58Z3JkPFVmGyUrpR+2+HOcJ3Hkb+XPYZi03AcWxJRE1jyMCeKZp+rMzmZ5mIn2uJDHDjA4ZIJ5tsN60m8nhGW4rKbdGSB+9dp0ixdUY8dQA/a5/rd2bq8+62+X/FpACaakm3bt4ul+w==";
		try {
			log.info("private_key:"+orgId+","+private_key_file_path);
			log.info("public_key:"+orgId+","+public_key_file_path);
//			PrivateKey hzfPriKey = loadPrivateKeyByStr(loadPrivateKeyByFile(private_key_file_path));
//			PublicKey yhPubKey = loadPublicKeyByStr(loadPublicKeyByFile(public_key_file_path));
			PrivateKey hzfPriKey = loadPrivateKeyByStr(privateKey);
			PublicKey yhPubKey = loadPublicKeyByStr(publicKey);
			SimpleDateFormat sdf  = new SimpleDateFormat("yyyyMMddhhmmss");
			String reqTime = sdf.format(new Date());
			Map<String,Object> map = new HashMap<>();
			map.put("accessId", ryx_access_id); // 接入方Id
			if(order_id != null && !"".equals(order_id)){
				map.put("accessMerchId", order_id); // 接入方订单号
			}
			map.put("reqTime", reqTime);  // pattern : yyyyMMddHHmmss

			//组织签名数据
			String ivParam = getIVParam(16);
			log.info("private_key1:"+orgId+","+ivParam);
			Object[] sortedKeys = textMap.keySet().toArray();
			Arrays.sort(sortedKeys);
			StringBuilder buffer = new StringBuilder();
			buffer.append("accessId=").append(ryx_access_id)
					.append("&reqTime=").append(map.get("reqTime"))
					.append("&ivParam=").append(ivParam);
			for (Object key : sortedKeys) {
				buffer.append("&").append(key).append("=").append(textMap.get(key));
			}
			String sign_str = buffer.toString();
			log.info("ruiyinxin RISK org str :"+orgId+","+sign_str);
			String sign_ecp = signSha256withRSA(sign_str, hzfPriKey);
			log.info("sign:"+orgId+","+sign_ecp);
			map.put("sign", sign_ecp);

			//获取AES密钥
			byte[] aesKey = initKey();
			JSONObject keyMap = new JSONObject();
			keyMap.put("aesKey", Base64.encodeBase64String(aesKey));
			keyMap.put("ivParam", ivParam);
			//用RSA公钥加密方式对AES密钥进行加密,对方公钥加密
			String encryptKey = encrypt(yhPubKey,keyMap.toString().getBytes("utf-8"));
			map.put("encryptKey", encryptKey);
			/*fileMap
			if(files!=null&&files.length>0) map.put("files",files);*/
			//数据加密
			map.put("info", encryptToString(mapToJson(textMap).toString().getBytes("utf-8"), aesKey, ivParam));
			log.info("ryx RISK post json req:"+orgId+","+mapToJson(map));

			JSONObject result = HttpUrlConnectionToInterface.formUploadForRyx(url, map, fileMap, "image/png");
			log.info("ryx RISK post json:"+orgId+","+result.toString());
			if(result != null && result.containsKey("result")){
				String res = result.getString("result");
				JSONObject rsj = new JSONObject(res.isEmpty());

				if(rsj.containsKey("info")){
					String info = rsj.getString("info");
					String encryptKey2 = rsj.getString("encryptKey");
					String sign = rsj.getString("sign");
					String rspTime = rsj.getString("reqTime");
					//使用RSA私钥解密AES密钥,我方私钥解密
					String aesKeyInfo = new String(decrypt(hzfPriKey, decodeBase64(encryptKey2)), "utf-8");
					JSONObject akinfo = new JSONObject(aesKeyInfo.isEmpty());

					String data = new String(decryptAes(decodeBase64(info), decodeBase64(akinfo.get("aesKey").toString()), akinfo.get("ivParam").toString()), "utf-8");
					log.info("ryx RISK post result:"+orgId+","+data);
					rj = new JSONObject(data.isEmpty());
					String code = "";
					if(rj.containsKey("code")){
						code = rj.getString("code");
					}
					String msg = "查询风控信息失败";
					if(rj.containsKey("msg")){
						msg = rj.getString("msg");
					}
					if("0000".equals(code)){
						rj.put("result_code", "success");
						rj.put("order_id", order_id);
					}else{
						rj.put("result_code", "fail");
						rj.put("order_id", order_id);
						rj.put("error_msg", msg);
					}
				}else{
					rj.put("result_code", "fail");
					rj.put("order_id", order_id);
					rj.put("error_msg", "查询风控信息失败，请联系技术人员");
				}
			}else{
				rj.put("result_code", "fail");
				rj.put("order_id", order_id);
				rj.put("error_msg", "查询风控信息失败，状态码"+result.getString("code")+"请联系技术人员");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rj;
	}
	private static String getIVParam(int length) {
		SecureRandom random = new SecureRandom();
		StringBuilder ret = new StringBuilder();
		for (int i = 0; i < length; i++) {
			boolean isChar = (random.nextInt(2) % 2 == 0);// 输出字母还是数字
			if (isChar) { // 字符串
				int choice = random.nextInt(2) % 2 == 0 ? 65 : 97; // 取得大写字母还是小写字母
				ret.append((char) (choice + random.nextInt(26)));
			} else { // 数字
				ret.append(Integer.toString(random.nextInt(10)));
			}
		}
		return ret.toString();
	}

	private static String signSha256withRSA(String data, PrivateKey hzfPriKey){
		try {
            Signature Sign = Signature.getInstance("SHA256withRSA");
            Sign.initSign(hzfPriKey);
            Sign.update(data.getBytes("utf-8"));
            byte[] signed = Sign.sign();
            return Base64.encodeBase64String(signed) ;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
	}

	/**
	 * 生成二进制密钥
	 * @return
	 * @throws Exception
	 */
	private static byte[] initKey() throws Exception {
		KeyGenerator kg = KeyGenerator.getInstance("AES");
		kg.init(256);
		return kg.generateKey().getEncoded();
	}

	/**
     * 公钥加密过程
     *
     * @param publicKey
     *            公钥
     * @param plainTextData
     *            明文数据
     * @return
     * @throws Exception
     *             加密过程中的异常信息
     */
	private static String encrypt(PublicKey publicKey, byte[] plainTextData) throws Exception {
        if (publicKey == null) {
            throw new Exception("加密公钥为空, 请设置");
        }
        Cipher cipher = null;
        try {
            // 使用默认RSA
            cipher = Cipher.getInstance("RSA");
            // cipher= Cipher.getInstance(KEY_ALGORITHM, new BouncyCastleProvider());
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] output = cipher.doFinal(plainTextData);

            return Base64.encodeBase64String(output);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此加密算法");
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
            return null;
        } catch (InvalidKeyException e) {
            throw new Exception("加密公钥非法,请检查");
        } catch (IllegalBlockSizeException e) {
            throw new Exception("明文长度非法");
        } catch (BadPaddingException e) {
            throw new Exception("明文数据已损坏");
        }
    }

	/**
	 * 数据加密
	 * @param data 待加密数据
	 * @param key 密钥
	 * @param ivParam 向量
	 * @return 加密数据
	 * @throws Exception
	 */
	private static String encryptToString(byte[] data, byte[] key, String ivParam) throws Exception {
		return Base64.encodeBase64String(encrypt(data, key, ivParam));
	}

	/**
	 * 数据加密
	 * @param data 待加密数据
	 * @param key 密钥
	 * @param ivParam 向量
	 * @return 加密数据
	 * @throws Exception
	 */
	private static byte[] encrypt(byte[] data, byte[] key, String ivParam) throws Exception {
		SecretKey secretKey = new SecretKeySpec(key, "AES");
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");// 创建密码器
		IvParameterSpec iv = new IvParameterSpec(ivParam.getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
		cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);// 初始化
		byte[] result = cipher.doFinal(data);
		return result; // 加密
	}

	/**
	 * map to json
	 * @param map
	 * @return
	 */
	private static JSONObject mapToJson(Map<String, Object> map) {
		JSONObject json = new JSONObject();
		try {
			Collection<String> keyset = map.keySet();
			List<String> list = new ArrayList<>(keyset);
			Collections.sort(list);
			for (String element : list) {
				json.put(element, map.get(element));
			}
		} catch (JSONException e) {
			e.printStackTrace();
		}
		return json;
	}

	/**
     *
     * @Title: decodeBase64
     * @Description: Base64解密
     * @param data
     * @return String
     * <AUTHOR>
     */
	private static byte[] decodeBase64(String data){
    	return Base64.decodeBase64(data);
    }

	/**
     * 私钥解密过程
     *
     * @param privateKey
     *            私钥路径
     * @param cipherData
     *            密文数据
     * @return 明文
     * @throws Exception
     *             解密过程中的异常信息
     */
	private static byte[] decrypt(PrivateKey privateKey, byte[] cipherData) throws Exception {
        Cipher cipher = null;
        try {
            // 使用默认RSA
            cipher = Cipher.getInstance("RSA");
            // cipher= Cipher.getInstance(KEY_ALGORITHM, new BouncyCastleProvider());
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] output = cipher.doFinal(cipherData);
            return output;
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此解密算法");
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
            return null;
        } catch (InvalidKeyException e) {
            throw new Exception("解密私钥非法,请检查");
        } catch (IllegalBlockSizeException e) {
            throw new Exception("密文长度非法");
        } catch (BadPaddingException e) {
            throw new Exception("密文数据已损坏");
        }catch(Exception e){
        	throw new Exception ("解密失败");
        }
    }
    /**
	 * 数据解密
	 * @param data 待解密数据
	 * @param key 密钥
	 * @param ivParam 向量
	 * @return 解密数据
	 * @throws Exception
	 */
	private static byte[] decryptAes(byte[] data, byte[] key, String ivParam) throws Exception {
		SecretKey secretKey = new SecretKeySpec(key, "AES");
		 Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");// 创建密码器
         IvParameterSpec iv = new IvParameterSpec(ivParam.getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
         cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);// 初始化
         byte[] result = cipher.doFinal(data);
         return result; // 加密
	}



	/**
     * 从文件中加载私钥
     *
     * @param path
     *            私钥文件名
     * @return 是否成功
     * @throws Exception
     */
    private static String loadPrivateKeyByFile(String path) throws Exception {
        try {
            BufferedReader br = new BufferedReader(new FileReader(path));
            String readLine = null;
            StringBuilder sb = new StringBuilder();
            while ((readLine = br.readLine()) != null) {
                sb.append(readLine);
            }
            br.close();
            return sb.toString();
        } catch (IOException e) {
            throw new Exception("私钥数据读取错误");
        } catch (NullPointerException e) {
            throw new Exception("私钥输入流为空");
        }
    }

    /**
     * 从字符串中加载私钥
     *
     * @param privateKeyStr
     *            私钥数据字符串
     * @throws Exception
     *             加载私钥时产生的异常
     */
    private static PrivateKey loadPrivateKeyByStr(String privateKeyStr)
            throws Exception {
        try {
            Base64 base64 = new Base64();
            byte[] buffer = base64.decode(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("私钥非法");
        } catch (NullPointerException e) {
            throw new Exception("私钥数据为空");
        }
    }
    /**
     * 从文件中输入流中加载公钥
     *
     * @param path
     *            公钥输入流
     * @throws Exception
     *             加载公钥时产生的异常
     */
    public static String loadPublicKeyByFile(String path) throws Exception {
        try {
            BufferedReader br = new BufferedReader(new FileReader(path));
            String readLine = null;
            StringBuilder sb = new StringBuilder();
            while ((readLine = br.readLine()) != null) {
                sb.append(readLine);
            }
            br.close();
            return sb.toString();
        } catch (IOException e) {
            throw new Exception("公钥数据流读取错误");
        } catch (NullPointerException e) {
            throw new Exception("公钥输入流为空");
        }
    }
    /**
     * 从字符串中加载公钥
     *
     * @param publicKeyStr
     *            公钥数据字符串
     * @throws Exception
     *             加载公钥时产生的异常
     */
    private static PublicKey loadPublicKeyByStr(String publicKeyStr)
            throws Exception {
        try {
            Base64 base64 = new Base64();
            byte[] buffer = base64.decode(publicKeyStr);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
            return keyFactory.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new Exception("公钥非法");
        } catch (NullPointerException e) {
            throw new Exception("公钥数据为空");
        }
    }

	/**
	 * 私钥解密(用于数据解密)
	 *
	 * @param data          解密前的字符串
	 * @param privateKeyStr 私钥
	 * @return 解密后的字符串
	 * @throws Exception
	 */
	public static String decryptByPrivateKey(String data, String privateKeyStr) throws Exception {
		// 去除私钥字符串中的非Base64部分
		// 去除私钥字符串中的非Base64部分
		privateKeyStr = privateKeyStr.replace("-----BEGIN PRIVATE KEY-----", "")
				.replace("-----END PRIVATE KEY-----", "")
				.replaceAll("\\s", ""); // 去除所有空白字符（包括换行符）

		// 使用 Apache Commons Codec 的 Base64 解码
		byte[] priKey = Base64.decodeBase64(privateKeyStr);

		// 创建PKCS8编码密钥规范
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(priKey);

		// 返回转换指定算法的KeyFactory对象
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");

		// 根据PKCS8编码密钥规范产生私钥对象
		PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

		// 根据转换的名称获取密码对象Cipher（转换的名称：算法/工作模式/填充模式）
		Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding"); // 指定具体的加密模式和填充方式

		// 用私钥初始化此Cipher对象（解密模式）
		cipher.init(Cipher.DECRYPT_MODE, privateKey);

		// 对数据解密
		byte[] decrypt = cipher.doFinal(Base64.decodeBase64(data));

		// 返回字符串
		return new String(decrypt, "UTF-8"); // 指定字符集
	}


}
