package com.cmpay.code.framework.common.util.sign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.*;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2023/12/20 15:18
 * version 1.0
 */
@Slf4j
public class SignUtil {
    /*
     * @Description:根据json字符串和key生成sign
     *@param
     *@return
     */
    public static String parseSign(JSONObject json, String key) {
        json.remove("sign");
        Map<String, Object> map = JSON.parseObject(json.toString(), Map.class);
        //获取sign
        Collection<String> keyset = map.keySet();
        List<String> list = new ArrayList<String>(keyset);
        Collections.sort(list);
        String signStr = "";
        for (int i = 0; i < list.size(); i++) {
            signStr += list.get(i) + "=" + map.get(list.get(i));
            if (i < list.size() - 1) {
                signStr += "&";
            }
        }
        signStr += "&key=" + key;
        log.info("signStr:" + signStr);
        String sign = DigestUtils.md5Hex(signStr).toUpperCase();
        list.clear();
        list = null;
        map.clear();
        map = null;
        return sign;
    }



}
