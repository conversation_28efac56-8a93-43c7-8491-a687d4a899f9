package com.cmpay.code.framework.common.util.sign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.framework.common.util.md5.MD5Util2;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.*;

/**
 * 加签验证
 */
public class SignUtils {
    public static String parseSign(JSONObject json, String key){
        json.remove("sign");
        Map<String, Object> map = JSON.parseObject(json.toString(), Map.class);
        //获取sign
        Collection<String> keyset= map.keySet();
        List<String> list = new ArrayList<String>(keyset);
        Collections.sort(list);
        String signStr = "";
        for(int i=0;i<list.size();i++){
            signStr += list.get(i)+"="+map.get(list.get(i));
            if(i<list.size()-1){
                signStr += "&";
            }
        }
        signStr += "&key="+key;
        String sign = MD5Util2.encode(signStr).toUpperCase();
        list.clear();
        list = null;
        map.clear();
        map = null;
        return sign;
    }

    /*
     * @Description:进行md5加密
     *@param
     *@return
     */
    public static String md5encode(String str) {
        String md5Str = DigestUtils.md5Hex(str).toUpperCase();
        return md5Str;
    }
}
