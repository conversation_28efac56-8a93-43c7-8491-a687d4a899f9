package com.cmpay.code.framework.common.util.string;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StrUtils {

    public static String maxLength(CharSequence str, int maxLength) {
        return StrUtil.maxLength(str, maxLength - 3); // -3 的原因，是该方法会补充 ... 恰好
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects) {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组
     *                * @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects) {
        return isNull(objects) || (objects.length == 0);
    }

    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object) {
        return object == null;
    }


    /**
     * 给定字符串是否以任何一个字符串开始
     * 给定字符串和数组为空都返回 false
     *
     * @param str      给定字符串
     * @param prefixes 需要检测的开始字符串
     * @since 3.0.6
     */
    public static boolean startWithAny(String str, Collection<String> prefixes) {
        if (StrUtil.isEmpty(str) || ArrayUtil.isEmpty(prefixes)) {
            return false;
        }

        for (CharSequence suffix : prefixes) {
            if (StrUtil.startWith(str, suffix, false)) {
                return true;
            }
        }
        return false;
    }

    public static List<Long> splitToLong(String value, CharSequence separator) {
        long[] longs = StrUtil.splitToLong(value, separator);
        return Arrays.stream(longs).boxed().collect(Collectors.toList());
    }

    /**
     * 判断String字符串是否为整数或小数
     *
     * @param str
     * @return
     */
    public static Boolean isNumberic(String str) {
        if (str == null) {
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]*\\.?[0-9]+");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 是否包含除下划线外的特殊字符
     *
     * @param str 字符串
     * @return 是否
     */
    public static Boolean isHasSpecialCharacters(String str) {
        boolean result;
        String regEx = "[`~!@#$%^&*+=|{}':;',\\[\\].<>/?~！@#￥%……&*——+|{}【】‘；：”“’。，、？]|\n|\r|\t|http";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        result = m.find();
        return result;
    }

    /**
     * 判断两个字符串是否相同
     */
    public static boolean isSame(String str1, String str2) {
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return false;
        }
        return str1.equals(str2);
    }

    /**
     * 判断两个字符串是否不相同
     */
    public static boolean isNotSame(String str1, String str2) {
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return true;
        }
        return !str1.equals(str2);
    }


    /**
     * 集合拼接成字符串，按照英文逗号分隔
     *
     * @return
     */
    public static String listJoinStr(List<String> list) {
        return CollectionUtils.isEmpty(list) ? "" : list.stream().map(Objects::toString).collect(Collectors.joining(","));
    }


    /**
     * 判断两个包装类型是否相等
     */
    public static boolean isSamePack(Object o1, Object o2) {
        boolean flag = false;
        if (Objects.isNull(o1) || Objects.isNull(o2)) {
            return flag;
        }
        return o1.equals(o2);
    }

    /**
     * 判断两个包装类型是否不相等
     */
    public static boolean isNotSamePack(Object o1, Object o2) {
        boolean flag = true;
        if (Objects.isNull(o1) || Objects.isNull(o2)) {
            return flag;
        }
        return !o1.equals(o2);
    }

    /**
     * 校验该字符串是否包含数字
     * @param str 字符串
     * @return 结果
     */
    public static boolean containsNumber(String str) {
        return str.matches(".*\\d+.*");
    }

}
