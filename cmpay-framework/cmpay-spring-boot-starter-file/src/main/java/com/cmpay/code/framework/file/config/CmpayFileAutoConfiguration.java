package com.cmpay.code.framework.file.config;

import com.cmpay.code.framework.file.core.client.FileClientFactory;
import com.cmpay.code.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class CmpayFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
