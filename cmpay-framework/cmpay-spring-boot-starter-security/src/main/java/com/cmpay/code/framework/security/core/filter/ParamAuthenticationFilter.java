package com.cmpay.code.framework.security.core.filter;

import cn.hutool.extra.servlet.ServletUtil;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.util.json.JsonUtil;
import com.cmpay.code.framework.common.util.servlet.ServletUtils;
import com.cmpay.code.framework.common.util.thread.ThreadLocalUtil;
import com.cmpay.code.framework.security.config.SecurityProperties;
import com.cmpay.code.framework.security.core.util.SecurityFrameworkUtils;
import com.cmpay.code.framework.web.core.handler.GlobalExceptionHandler;
import com.cmpay.code.module.system.api.constants.redis.OAuth2AccessTokenRedisUtil;
import com.cmpay.code.module.system.api.oauth2.dto.OAuth2AccessTokenRedisDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.cmpay.code.framework.common.exception.enums.GlobalErrorCodeConstants.*;
import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cmpay.code.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * Token 验证权限filter
 */
@RequiredArgsConstructor
@Slf4j
public class ParamAuthenticationFilter extends OncePerRequestFilter {
    private final SecurityProperties securityProperties;

    @Resource
    private OAuth2AccessTokenRedisUtil oAuth2AccessTokenRedisUtil;

    @Resource
    private GlobalExceptionHandler globalExceptionHandler;

    /**
     * 多长时间内
     */
    @Value("${interfaceAccess.second}")
    private Long second;

    /**
     * 访问次数
     */
    @Value("${interfaceAccess.requestCount}")
    private Long requestCount;

    /**
     * 禁用时长--单位/秒
     */
    @Value("${interfaceAccess.lockTime}")
    private Long lockTime;

    /**
     * 锁住时的key前缀
     */
    public static final String LOCK_PREFIX = "LOCK:";

    /**
     * 统计次数时的key前缀
     */
    public static final String COUNT_PREFIX = "COUNT:";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            List<String> openCheckList = Arrays.asList("/admin/instances", "/actuator", "/actuator/health", "/instances", "/admin");
            //这里是防接口刷新逻辑
            String url = request.getRequestURI();
            if (openCheckList.contains(url)) {
                // 如果请求的 URL 满足规定的url 则直接放行
                filterChain.doFilter(request, response);
                return;
            }
            String ip1 = request.getHeader("X-Forwarded-For");
            String uniquePcCode = request.getHeader("uniquepccode");
            uniquePcCode = StringUtils.isNotEmpty(uniquePcCode) ? uniquePcCode : "";
            log.info("req doFilterInternal ip:{}", ip1);
            String ip = ServletUtil.getClientIP(request); // 这里忽略代理软件方式访问，默认直接访问，也就是获取得到的就是访问者真实ip地址
            log.info("ServletUtil.getClientIP:[错误]{}[正确]{}", ip1, ip);
            ThreadLocalUtil.set("ip", ip);
            ThreadLocalUtil.set("device", uniquePcCode);
            String lockKey = LOCK_PREFIX + ip + url;
            String isLock = oAuth2AccessTokenRedisUtil.getIpCount(lockKey);
            if (Objects.isNull(isLock)) {
                // 还未被禁用
                String countKey = COUNT_PREFIX + ip + url;
                String count = oAuth2AccessTokenRedisUtil.getIpCount(countKey);
                if (Objects.isNull(count)) {
                    // 首次访问
                    log.info("首次访问");
                    oAuth2AccessTokenRedisUtil.set(countKey, "1", second, TimeUnit.SECONDS);
                    log.info("首次访问添加redis");
                } else {
                    // 此用户前一点时间就访问过该接口
                    if (Integer.parseInt(count) < requestCount) {
                        // 放行，访问次数 + 1
                        oAuth2AccessTokenRedisUtil.increment(countKey);
                        log.info("访问次数:{}", count);
                    } else {
                        log.info("{}禁用访问{}", ip, url);
                        // 禁用
                        oAuth2AccessTokenRedisUtil.set(lockKey, "1", lockTime, TimeUnit.SECONDS);
                        // 删除统计
                        oAuth2AccessTokenRedisUtil.deleteCount(countKey);
                        throw new RuntimeException("请勿点击那么快，稍等一下！");
                    }
                }
            } else {
                // 此用户访问此接口已被禁用
                throw new RuntimeException("请勿点击那么快，稍等一下！");
            }
        } catch (Exception ex) {
            CommonResult<?> result = globalExceptionHandler.notPermittedExceptionHandler(request, ex);
            ServletUtils.writeJSON(response, result);
            return;
        }

        //这里是越权访问逻辑
        Long loginUserId = getLoginUserId();
        String token = SecurityFrameworkUtils.obtainAuthorization(request, securityProperties.getTokenHeader());
        if (token != null) {
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            // 检查内容长度是否大于等于0
            int contentLength = request.getContentLength();
            String queryString = request.getQueryString();
            if (contentLength < 0 && StringUtils.isEmpty(queryString)) {
                filterChain.doFilter(request, response);
                return;
            }
            List<String> openEndpoints = Arrays.asList("/admin-api/system/auth/get-permission-info",
                    "/admin-api/system/dict-data/list-all-simple", "/admin-api/system/auth/logout",
                    "/admin-api/system/auth/refresh-token", "/admin-api/system/auth/list-menus", "/admin-api/permission/get-button",
                    "/admin-api/cmpay-login/get-branch", "/admin-api/system/permission/list-role-resources", "/admin-api/infra/codegen/preview",
                    "/admin-api/cmpay-login/login", "/admin-api/domain-name-msg/get-by-domainname");
            String substring = requestURI.substring(0, 18);

            if (openEndpoints.contains(requestURI) || substring.equals("/admin-api/system/")) {
                // 如果请求的 URL 满足规定的url 则直接放行
                filterChain.doFilter(request, response);
                return;
            }
            if (requestURI.contains("/admin-api/infra/")) {
                // 如果请求的 URL 满足规定的url 则直接放行
                filterChain.doFilter(request, response);
                return;
            }
            OAuth2AccessTokenRedisDTO oAuth2AccessTokenRedisDTO = oAuth2AccessTokenRedisUtil.get(token);
            if (oAuth2AccessTokenRedisDTO == null) {
                CommonResult<?> result = CommonResult.error(UNAUTHORIZED);
                ;
                ServletUtils.writeJSON(response, result);
            }
            //获取请求的platform比对是不是登录的platform
            String platform = "";
            String shopId = "";
            String acceptId = "";
            String agentId = "";
            String partnerId = "";
            String branchId = "";
            String trueid = "";
            if ("GET".equals(method) || "DELETE".equals(method)) {
                platform = request.getParameter("platform");
                shopId = request.getParameter("shopId");
                acceptId = request.getParameter("acceptId");
                agentId = request.getParameter("agentId");
                partnerId = request.getParameter("partnerId");
                branchId = request.getParameter("branchId");
                trueid = request.getParameter("username");
            } else if ("POST".equals(method) || "PUT".equals(method)) {
                JSONObject parameters = JsonUtil.getParameters(request);
                try {
                    if (Objects.isNull(parameters)) {
                        platform = oAuth2AccessTokenRedisDTO.getPlatform().toString();
                        shopId = oAuth2AccessTokenRedisDTO.getShopId();
                        acceptId = oAuth2AccessTokenRedisDTO.getAcceptId();
                        agentId = oAuth2AccessTokenRedisDTO.getAgentId();
                        partnerId = oAuth2AccessTokenRedisDTO.getPartnerId();
                        branchId = oAuth2AccessTokenRedisDTO.getBranchId();
                        trueid = oAuth2AccessTokenRedisDTO.getTrueid();
                    } else {
                        platform = parameters.getString("platform");
                        shopId = parameters.getString("shopId");
                        acceptId = parameters.getString("acceptId");
                        agentId = parameters.getString("agentId");
                        partnerId = parameters.getString("partnerId");
                        branchId = parameters.getString("branchId");
                        trueid = parameters.getString("username");
                    }
                } catch (JSONException e) {
                }
            }
            //判断platform是否存在
            if (StringUtils.isNotEmpty(platform)) {
                //判断平台号是否和登录人的平台号相同
                if (platform.equals(oAuth2AccessTokenRedisDTO.getPlatform().toString())) {
                    //登录商户平台
                    if ("5".equals(request.getParameter("platform"))) {
                        //判断商户号是否存在
                        if (StringUtils.isNotEmpty(shopId) || StringUtils.isNotEmpty(trueid)) {
                            //判断商户号是否和登录人商户号相同
                            if (!(StringUtils.isEmpty(shopId) ? "" : shopId).equals(oAuth2AccessTokenRedisDTO.getShopId()) && !((StringUtils.isEmpty(trueid) ? "" : trueid).equals(oAuth2AccessTokenRedisDTO.getTrueid()))) {
                                throw exception(SHOPID_IS_SUCCESS);
                            }
                        } else {//商户号不存在
                            throw exception(SHOPID_IS_EXIST);
                        }
                    }
                    //登录网点平台
                    if ("4".equals(request.getParameter("platform"))) {
                        //判断网点id是否存在
                        if (StringUtils.isNotEmpty(acceptId)) {
                            //判断网点id是否和登录人网点id相同
                            if (!acceptId.equals(oAuth2AccessTokenRedisDTO.getAcceptId())) {
                                throw exception(ACCEPTID_IS_SUCCESS);
                            }
                        } else {//网点id不存在
                            throw exception(ACCEPTID_IS_EXIST);
                        }
                    }

                    //登录区县平台
                    if ("3".equals(request.getParameter("platform"))) {
                        //判断区县id是否存在
                        if (StringUtils.isNotEmpty(agentId)) {
                            //判断区县id是否和登录人区县Idid相同
                            if (!agentId.equals(oAuth2AccessTokenRedisDTO.getAgentId())) {
                                throw exception(AGENTID_IS_SUCCESS);
                            }
                        } else {//区县id不存在
                            throw exception(AGENTID_IS_EXIST);
                        }
                    }

                    //登录区县平台
                    if ("2".equals(request.getParameter("platform"))) {
                        //判断市级id是否存在
                        if (StringUtils.isNotEmpty(partnerId)) {
                            //判断市级id是否和登录人市级id相同
                            if (!partnerId.equals(oAuth2AccessTokenRedisDTO.getPartnerId())) {
                                throw exception(PARTNERID_IS_SUCCESS);
                            }
                        } else {//市级id不存在
                            throw exception(PARTNERID_IS_EXIST);
                        }
                    }

                    //登录区县平台
                    if ("1".equals(request.getParameter("platform"))) {
                        //判断省id是否存在
                        if (StringUtils.isNotEmpty(branchId)) {
                            //判断省id是否和登录人省id相同
                            if (!branchId.equals(oAuth2AccessTokenRedisDTO.getBranchId())) {
                                throw exception(BRANCHID_IS_SUCCESS);
                            }
                        } else {//省id不存在
                            throw exception(BRANCHID_IS_EXIST);
                        }
                    }

                } else {//平台号和登录人的平台号不相同
                    throw exception(INTERNAL_SERVER_ERROR);
                }
            } else {//平台号不存在
                throw exception(PLATFORM_IS_EXIST);
            }

        }
        log.info("操作人 ==> {}", SecurityFrameworkUtils.getLoginUserId());
        filterChain.doFilter(request, response);

    }
}