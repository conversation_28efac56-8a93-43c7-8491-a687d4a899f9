package com.cmpay.code.cmpaylogin.controller.admin.login;

import com.cmpay.code.cmpaylogin.controller.admin.login.vo.BrandchIdRespVO;
import com.cmpay.code.cmpaylogin.service.LoginService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import com.cmpay.code.module.system.controller.admin.auth.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 登录")
@RestController
@RequestMapping("/cmpay-login/")
@Validated
public class LoginController {

    @Resource
    private LoginService loginService;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(loginService.login(reqVO));
    }

    @GetMapping("/get-branch")
    @Operation(summary = "获取登录人的省级id以及市级id")
    public CommonResult<BrandchIdRespVO> getBranchId(@RequestParam("username") String username, @RequestParam("platform") Integer platform) {
        return success(loginService.getBranchId(username, platform));
    }


    @PostMapping("/sendCode")
    @PermitAll
    @Operation(summary = "登录发送验证码")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<LoginSendCodeRespVO> sendCode(@RequestBody @Valid LoginSendCodeReqVO loginSendCodeReqVO) {
        return success(loginService.sendCode(loginSendCodeReqVO));
    }

    @PostMapping("/verifyCode")
    @PermitAll
    @Operation(summary = "登录校验验证码")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Object> verifyCode(@RequestBody @Valid LoginVerifyCodeReqVO loginVerifyCodeReqVO) {
        return success(loginService.verifyCode(loginVerifyCodeReqVO));
    }

    @PostMapping("/wechat/auth/refresh")
    @PermitAll
    @Operation(summary = "微信认证刷新")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<WechatAuthRefreshRespVO> wechatAuthRefresh(@RequestBody @Valid WechatAuthRefreshReqVO wechatAuthRefreshReqVO) {
        return success(loginService.wechatAuthRefresh(wechatAuthRefreshReqVO));
    }

    @PostMapping("/wechat/auth/status")
    @PermitAll
    @Operation(summary = "微信认证状态")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<WechatAuthStatusRespVO> wechatAuthStatus(@RequestBody @Valid WechatAuthStatusReqVO wechatAuthStatusReqVO) {
        return success(loginService.wechatAuthStatus(wechatAuthStatusReqVO));
    }


}