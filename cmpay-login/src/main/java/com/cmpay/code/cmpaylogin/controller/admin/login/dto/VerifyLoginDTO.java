package com.cmpay.code.cmpaylogin.controller.admin.login.dto;

import com.cmpay.code.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Title: VeriftLoginDTO
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaylogin.controller.admin.login.dto
 * @Date 2025/4/11 14:48
 * @description: 校验登录对象
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class VerifyLoginDTO {
    private String username;
    private String password;
    private String platform;
    private String phone;
    private AuthLoginRespVO authLoginRespVO;
}
