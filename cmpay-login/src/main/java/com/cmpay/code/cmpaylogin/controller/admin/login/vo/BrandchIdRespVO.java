package com.cmpay.code.cmpaylogin.controller.admin.login.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BrandchIdRespVO {

    @Schema(description = "省级id")
    private String branchId;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "大商户号")
    private String agentSubId;

    @Schema(description = "大商户名称")
    private String agentSubName;

    @Schema(description = "省级名称")
    private Integer brandchName;

    @Schema(description = "市级名称")
    private String partnerName;

    @Schema(description = "区县名称")
    private String agentName;

    @Schema(description = "网点名称")
    private String acceptName;

    @Schema(description = "操作人账号")
    private String account;
}
