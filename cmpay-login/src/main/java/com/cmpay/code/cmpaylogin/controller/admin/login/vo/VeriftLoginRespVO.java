package com.cmpay.code.cmpaylogin.controller.admin.login.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Title: VeriftLoginRespVO
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaylogin.controller.admin.login.vo
 * @Date 2025/4/11 15:01
 * @description: 校验登录对象相应类
 */
@Data
@Accessors(chain = true)
public class VeriftLoginRespVO {
    private String loginId;
    private String authId;
    private String phone;
    private String weChatAuthUrl;
    private Boolean existPhone;
    private Boolean existWeChatAuth;
}
