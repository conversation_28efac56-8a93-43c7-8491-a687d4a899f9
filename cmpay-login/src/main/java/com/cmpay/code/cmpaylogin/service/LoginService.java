package com.cmpay.code.cmpaylogin.service;

import com.cmpay.code.cmpaylogin.controller.admin.login.vo.BrandchIdRespVO;
import com.cmpay.code.module.system.controller.admin.auth.vo.*;

import javax.validation.Valid;

public interface LoginService {
    /**
     * 登录
     *
     * @param reqVO
     * @return
     */
    AuthLoginRespVO login(AuthLoginReqVO reqVO);

    /**
     * 获取登录人的省级id以及市级id
     *
     * @param username
     * @param platform
     * @return
     */
    BrandchIdRespVO getBranchId(String username, Integer platform);

    /**
     * 登录发送验证码
     *
     * @param loginSendCodeReqVO
     * @return
     */
    LoginSendCodeRespVO sendCode(@Valid LoginSendCodeReqVO loginSendCodeReqVO);

    /**
     * 校验验证码
     *
     * @param loginVerifyCodeReqVO
     * @return
     */
    Object verifyCode(@Valid LoginVerifyCodeReqVO loginVerifyCodeReqVO);

    /**
     * 微信认证刷新
     *
     * @param wechatAuthRefreshReqVO 登录刷新微信认证二维码请求对象
     */
    WechatAuthRefreshRespVO wechatAuthRefresh(@Valid WechatAuthRefreshReqVO wechatAuthRefreshReqVO);

    /**
     * 微信授权状态
     *
     * @param wechatAuthStatusReqVO 微信授权状态
     * @return 响应
     */
    WechatAuthStatusRespVO wechatAuthStatus(@Valid WechatAuthStatusReqVO wechatAuthStatusReqVO);
}
