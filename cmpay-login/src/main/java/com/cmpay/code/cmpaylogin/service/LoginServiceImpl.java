package com.cmpay.code.cmpaylogin.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cmpay.code.cmpaylogin.controller.admin.login.dto.VerifyLoginDTO;
import com.cmpay.code.cmpaylogin.controller.admin.login.vo.BrandchIdRespVO;
import com.cmpay.code.cmpaylogin.controller.admin.login.vo.VeriftLoginRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agentsub.AgentSubDO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.loginhistory.LoginHistoryDO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.loginsecurity.LoginSecurityDO;
import com.cmpay.code.cmpaymoduleagent.service.agentsub.AgentSubService;
import com.cmpay.code.cmpaymoduleagent.service.loginhistory.LoginHistoryService;
import com.cmpay.code.cmpaymoduleagent.service.loginsecurity.LoginSecurityService;
import com.cmpay.code.cmpaymoduleagent.service.loginwechatauth.LoginWechatAuthService;
import com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo.InitOneUsersReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.ShopRespVO;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.merchant.MerchantDO;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO;
import com.cmpay.code.cmpaymodulepc.service.chinaareano.ChinaAreaNoService;
import com.cmpay.code.cmpaymodulepc.service.environmentconfig.EnvironmentConfigService;
import com.cmpay.code.cmpaymodulepc.service.merchant.MerchantService;
import com.cmpay.code.cmpaymodulepc.service.merchantcashier.MerchantCashierService;
import com.cmpay.code.cmpaymodulepc.service.partnerchannel.PartnerChannelService;
import com.cmpay.code.cmpaymodulepc.service.statisticsday.StatisticsDayService;
import com.cmpay.code.framework.common.config.SystemConfig;
import com.cmpay.code.framework.common.constant.CacheConstants;
import com.cmpay.code.framework.common.constant.LoginConstant;
import com.cmpay.code.framework.common.constant.RedisKeyConstant;
import com.cmpay.code.framework.common.core.LoginType;
import com.cmpay.code.framework.common.exception.BizException;
import com.cmpay.code.framework.common.exception.GlobalErrorCodePc;
import com.cmpay.code.framework.common.exception.RoleException;
import com.cmpay.code.framework.common.exception.ServiceException;
import com.cmpay.code.framework.common.util.HttpUrlConnectionToInterface;
import com.cmpay.code.framework.common.util.date.DateUtil;
import com.cmpay.code.framework.common.util.id.IdUtil;
import com.cmpay.code.framework.common.util.md5.MD5Util2;
import com.cmpay.code.framework.common.util.number.NumberUtils;
import com.cmpay.code.framework.common.util.redis.RedisUtils;
import com.cmpay.code.framework.common.util.thread.ThreadLocalUtil;
import com.cmpay.code.module.system.controller.admin.auth.vo.*;
import com.cmpay.code.module.system.controller.admin.minipcloginuser.vo.MinPcLoginUserRespVO;
import com.cmpay.code.module.system.controller.admin.minipcloginuser.vo.MiniPcLoginUserCreateVO;
import com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO;
import com.cmpay.code.module.system.dal.dataobject.loginmandatoryverify.LoginMandatoryVerifyDO;
import com.cmpay.code.module.system.dal.dataobject.permission.RoleDO;
import com.cmpay.code.module.system.dal.dataobject.permission.UserRoleDO;
import com.cmpay.code.module.system.dal.dataobject.sysaccept.SysAcceptDO;
import com.cmpay.code.module.system.dal.dataobject.sysagent.SysAgentDO;
import com.cmpay.code.module.system.dal.dataobject.user.AdminUserDO;
import com.cmpay.code.module.system.enums.ErrorCodeConstants;
import com.cmpay.code.module.system.enums.logger.LoginLogTypeEnum;
import com.cmpay.code.module.system.service.assistantuser.AssistantUserService;
import com.cmpay.code.module.system.service.auth.AdminAuthService;
import com.cmpay.code.module.system.service.init.InitConstantService;
import com.cmpay.code.module.system.service.loginmandatoryverify.LoginMandatoryVerifyService;
import com.cmpay.code.module.system.service.minipcloginuser.MiniPcLoginUserService;
import com.cmpay.code.module.system.service.permission.RoleService;
import com.cmpay.code.module.system.service.permission.UserRoleService;
import com.cmpay.code.module.system.service.sysaccept.SysAcceptService;
import com.cmpay.code.module.system.service.sysagent.SysAgentService;
import com.cmpay.code.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.cmpay.code.framework.common.constant.PlatformConstant.*;
import static com.cmpay.code.framework.common.exception.GlobalErrorCodePc.*;
import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cmpay.code.framework.common.util.number.NumberUtils.validatePhoneNumber;
import static com.cmpay.code.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.cmpay.code.module.system.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;
import static com.cmpay.code.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS;
import static java.time.LocalTime.now;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class LoginServiceImpl implements LoginService {

    @Value("${user.password.maxRetryCount}")
    private Integer maxRetryCount;

    @Value("${user.password.lockTime}")
    private Integer lockTime;
    @Resource
    private AdminAuthService adminAuthService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private MerchantCashierService merchantCashierService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private RoleService roleService;

    @Resource
    private MiniPcLoginUserService miniPcLoginUserService;

    @Resource
    private PartnerChannelService partnerChannelService;

    @Resource
    private SysAcceptService sysAcceptService;

    @Resource
    private SysAgentService sysAgentService;

    @Resource
    private StatisticsDayService statisticsDayService;

    @Resource
    private AssistantUserService assistantUserService;

    @Resource
    private AgentSubService agentSubService;

    @Resource
    private SystemConfig systemConfig;

    @Resource
    private LoginHistoryService loginHistoryService;

    @Resource
    private EnvironmentConfigService environmentConfigService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ChinaAreaNoService chinaAreaNoService;

    @Resource
    private InitConstantService initConstantService;

    @Resource
    private LoginWechatAuthService loginWechatAuthService;

    @Resource
    private LoginSecurityService loginSecurityService;

    @Resource
    private LoginMandatoryVerifyService loginMandatoryVerifyService;


    private Map<String, AuthLoginReqVO> users = new HashMap<>();

    @Override
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        log.info("req login param:{}", reqVO);
        AuthLoginRespVO tokenAfterLoginSuccess = null;
        Integer platform = reqVO.getPlatform();
        String username = reqVO.getUsername();
        String password = reqVO.getPassword();
        //测试用
        List<String> whitList = Arrays.asList("6041175398", "11012346", "11111112", "lishengnan", "ceshi1", "22000022");
        if (!whitList.contains(reqVO.getUsername())) {
            //校验验证码
            adminAuthService.validateCaptcha(reqVO);
        }
        //查询用户根据用户名
        AdminUserDO userByUsername = adminUserService.getByUsername(reqVO.getUsername());
        MerchantDO merchantByTrueid = null;
        if (PLATFORM_MERCHANT.equals(reqVO.getPlatform())) {
            if (!NumberUtils.validatePhoneNumber(reqVO.getUsername())) {
                //查询商户根据短商户号
                merchantByTrueid = merchantService.getMerchantByTrueid(reqVO.getUsername());
                if (merchantByTrueid == null) {
                    throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
                }
                userByUsername = adminUserService.getByUsername(merchantByTrueid.getPhone());
            }
            log.info("用户登录 - 商户查询：" + merchantByTrueid);
        }
        String mobile = null;
        if (ObjectUtils.isNotEmpty(userByUsername)) {
            mobile = userByUsername.getMobile();
        }
        boolean adminPassword = ObjectUtils.notEqual("cm_zhiyunadmin", reqVO.getPassword()) ? Boolean.FALSE : Boolean.TRUE;
        if (!"cm_zhiyunadmin".equals(reqVO.getPassword())) {
            //校验密码用
            if (userByUsername == null) {
                throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
            }
            String checkPassword = userByUsername.getPassword();
            boolean equals = checkPassword.equals(MD5Util2.encode(reqVO.getPassword()));
            checkLogin(LoginType.PASSWORD, reqVO.getUsername(), equals);
        }
        try {
            log.info("用户登录：" + reqVO);
            String shopId = null;
            String trueid = null;
            Integer isTrueid = 0;
            List<UserRoleDO> userRoleByPlatform = null;
            //判断是不是大商户
            if (reqVO.getPlatform() == PLATFORM_LARGE_MERCHANT) {
                AgentSubDO byPhone = agentSubService.getByPhone(reqVO.getUsername());
                if (byPhone == null) {
                    throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
                }
            }
            //判断是不是商户
            if (reqVO.getPlatform() == PLATFORM_MERCHANT) {
                mobile = userByUsername.getUsername();
                trueid = reqVO.getUsername();
                if (reqVO.getUsername().length() == 10) {
                    //加密密码
                    String encode = MD5Util2.encode(reqVO.getPassword());
                    //根据商户手机号查询用户
                    AdminUserDO userDO = adminUserService.getUserByUsername(merchantByTrueid.getPhone());
                    log.info("用户登录 - 用户查询：" + userDO);
                    InitOneUsersReqVo initOneUsersReqVo = new InitOneUsersReqVo();
                    initOneUsersReqVo.setPhone(merchantByTrueid.getPhone());
                    initOneUsersReqVo.setShopId(merchantByTrueid.getShopId());
                    initOneUsersReqVo.setPassword(encode);
                    String xmid = merchantCashierService.initMerchantUpdateXmid(initOneUsersReqVo);
                    log.info("用户登录 - xmid查询：" + xmid);
                    //定义用户id
                    Long userId;
                    if (userDO == null) {
                        //添加
                        AdminUserDO adminUserDO = new AdminUserDO()
                                .setUserIdOld(xmid)
                                .setPassword(reqVO.getPassword())
                                .setPassword(encode)
                                .setUsername(merchantByTrueid.getPhone())
                                .setNickname(merchantByTrueid.getShopKeeper());
                        userId = adminUserService.insertSystemUser(adminUserDO);
                    } else {
                        userId = userDO.getId();
                    }
                    log.info("用户登录 - userId查询：" + userId);
                    userRoleByPlatform = userRoleService.searchOrangeUser(userId, merchantByTrueid.getShopId(), 5);
                    if (CollectionUtils.isEmpty(userRoleByPlatform)) {
                        UserRoleDO userRoleDO = new UserRoleDO()
                                .setUserId(userId)
                                .setRoleId(129L)
                                .setPlatformId(merchantByTrueid.getShopId())
                                .setPlatform(5)
                                .setCompanyName(merchantByTrueid.getShopName())
                                .setIsMerchant(1);
                        userRoleService.insertUserRole(userRoleDO);
                    } else {
                        if (129L != userRoleByPlatform.get(0).getRoleId()) {
                            log.info("用户登录 - 修改用户角色开始时间：" + now());
                            userRoleService.updateUserRoleByUserId(userId, 4, merchantByTrueid.getShopId());
                            log.info("用户登录 - 修改用户角色开始时间用户角色结束时间：" + now());
                        }

                    }
                    if (merchantByTrueid != null) {
                        shopId = merchantByTrueid.getShopId();
                    } else {
                        throw exception(USER_EXIST);
                    }
                    reqVO.setUsername(merchantByTrueid.getPhone());
                    isTrueid = 1;
                } else {
                    ShopRespVO shopRespVO = statisticsDayService.getShopRespVOByUserId(null, shopId, reqVO.getUsername(), null);
                    shopId = shopRespVO.getShopId();
                    if (userByUsername == null) {
                        throw exception(USER_EXIST);
                    }
                    List<MerchantCashierDO> list = new ArrayList<>();
                    if (StringUtils.isNotEmpty(userByUsername.getUserIdOld())) {
                        log.info("用户登录 - 查询merchantCashier开始时间：" + now());
                        list = merchantCashierService.selectListByXmid(userByUsername.getUserIdOld());
                        log.info("用户登录 - 查询merchantCashier结束时间{},以及结果{}", now(), list.size());
                    }
                    int count = 0;
                    //判断是否认证
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (MerchantCashierDO merchantCashierDO : list) {
                            if (merchantCashierDO.getIsOrNotCertified() == 1) {
                                break;
                            } else {
                                count++;
                            }
                            if (count == list.size()) {
                                throw exception(USER_NOT_CERTIFIED);
                            }
                        }
                    } else {
                        throw exception(USER_EXCEPTION);
                    }
                }
            }
            //判断是不是手机号
            boolean isPhone = validatePhoneNumber(reqVO.getUsername());
            //校验验证码
            //validateCaptcha(reqVO);
            // 使用账号密码，进行登录
            log.info("用户登录 - 账号登录校验开始时间{}", now());
            AdminUserDO user = adminAuthService.authenticate(reqVO.getUsername(), reqVO.getPassword(), reqVO.getPlatform());
            log.info("用户登录 - 账号登录校验结束时间{}", now());
            //存储登录信息
            if (reqVO.getPlatform() != PLATFORM_MERCHANT
                    && reqVO.getPlatform() != PLATFORM_NTERIOR
                    && reqVO.getPlatform() != PLATFORM_LARGE_MERCHANT) {
                if (isPhone) {
                    MinPcLoginUserRespVO minPcLoginUserRespVO = miniPcLoginUserService.get(reqVO.getUsername(), reqVO.getPlatform());
                    if (minPcLoginUserRespVO == null) {
                        MiniPcLoginUserCreateVO miniPcLoginUserCreateVO = new MiniPcLoginUserCreateVO();
                        //查询角色关联
                        log.info("用户登录 - 查询角色关联开始时间{}", now());
                        userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), reqVO.getPlatform());
                        log.info("用户登录 - 查询角色关联结束时间{}", now());
                        miniPcLoginUserCreateVO.setUserId(userByUsername.getId().toString());
                        miniPcLoginUserCreateVO.setNickname(userByUsername.getNickname());
                        miniPcLoginUserCreateVO.setPlatform(reqVO.getPlatform());
                        miniPcLoginUserCreateVO.setUsername(reqVO.getUsername());
                        if (userRoleByPlatform.size() > 0) {
                            if (userRoleByPlatform.get(0).getPlatformId() == null) {
                                miniPcLoginUserCreateVO.setPlatformId("");
                            } else {
                                miniPcLoginUserCreateVO.setPlatformId(userRoleByPlatform.get(0).getPlatformId());
                            }
                            miniPcLoginUserCreateVO.setPlatformName(userRoleByPlatform.get(0).getCompanyName());
                            miniPcLoginUserCreateVO.setRoleId(userRoleByPlatform.get(0).getRoleId());
                            RoleDO role = roleService.getRole(userRoleByPlatform.get(0).getRoleId());
                            miniPcLoginUserCreateVO.setRoleName(role.getName());
                        }
                        miniPcLoginUserService.insert(miniPcLoginUserCreateVO);
                    }
                    // 查询客户经理的openid
                    if (StringUtils.isNotEmpty(user.getUserIdOld())) {
                        AssistantUserDO assistantUser = assistantUserService.getCashierName(user.getUserIdOld());
                        if (assistantUser != null && StringUtils.isNotEmpty(assistantUser.getOpenid())) {
                            user.setOpenid(assistantUser.getOpenid());
                        }
                    }
                }
            }
            //添加用户缓存信息
            userRoleByPlatform = userRoleService.getUserRoleByPlatform(user.getId(), reqVO.getPlatform());
            JSONArray jsonArray = new JSONArray(userRoleByPlatform);
            user.setUserRoleList(jsonArray);
            if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                if (reqVO.getPlatform() == PLATFORM_BRANCH) {
                    user.setBranchId(userRoleByPlatform.get(0).getPlatformId());
                } else if (reqVO.getPlatform() == PLATFORM_PARTNER) {
                    user.setPartnerId(userRoleByPlatform.get(0).getPlatformId());
                } else if (reqVO.getPlatform() == PLATFORM_AGENT) {
                    user.setAgentId(userRoleByPlatform.get(0).getPlatformId());
                } else if (reqVO.getPlatform() == PLATFORM_ACCEPT) {
                    user.setAcceptId(userRoleByPlatform.get(0).getPlatformId());
                }
            } else {
                throw exception(USER_EXCEPTION);
            }
            user.setPlatform(reqVO.getPlatform());
            user.setShopId(shopId);
            if (reqVO.getPlatform() == PLATFORM_MERCHANT) {
                user.setTrueid(trueid);
            }
            // 创建 Token 令牌，记录登录日志
            tokenAfterLoginSuccess = adminAuthService.createTokenAfterLoginSuccess(user, reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, shopId, isTrueid);
            tokenAfterLoginSuccess.setIsLogin(user.getIsLogin());
            Long roleId = userRoleByPlatform.get(0).getRoleId();
            if (roleId == 128L || roleId == 125L) {
                // 客户经理
                mobile = userByUsername.getUsername();
            }


        } catch (Exception e) {
            if (e instanceof RoleException) {
                throw exception(AGENT_NOT_EXISTS);
            }
            log.error("[异常]用户登录:{},{}", reqVO, ExceptionUtils.getStackTrace(e));
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }

        if (!adminPassword) {
            VerifyLoginDTO verifyLoginDTO = new VerifyLoginDTO(userByUsername.getUsername(), password, String.valueOf(platform), mobile, tokenAfterLoginSuccess);
            veriftLogin(verifyLoginDTO);
        }

        log.info("用户登录 - 登录接口完成时间{}", now());
        return tokenAfterLoginSuccess;
    }

    /**
     * 登录校验
     *
     * @param verifyLoginDTO 校验对象
     */
    private void veriftLogin(VerifyLoginDTO verifyLoginDTO) {
        LocalDateTime now = LocalDateTime.now();
        String project = systemConfig.getPlatform();
        String newUsername = verifyLoginDTO.getUsername();
        String newPassword = verifyLoginDTO.getPassword();
        String newPlatform = verifyLoginDTO.getPlatform();
        String phone = verifyLoginDTO.getPhone();
        String ip = ThreadLocalUtil.get("ip");
        String newDevice = ThreadLocalUtil.get("device");
        AuthLoginRespVO authLoginRespVO = verifyLoginDTO.getAuthLoginRespVO();
        // 登录成功的信息
        String authStr = JSON.toJSONString(authLoginRespVO);
        // 本地登录城市信息
        String newCityCode = chinaAreaNoService.getCityCode(ip);
        // 保存历史登录信息
        LoginHistoryDO saveLoginHistoryDO = new LoginHistoryDO();
        saveLoginHistoryDO
                .setProject(project)
                .setPlatform(newPlatform)
                .setUsername(newUsername)
                .setPassword(newPassword)
                .setPhone(phone)
                .setDevice(newDevice)
                .setIp(ip)
                .setCityCode(newCityCode)
                .setUpdateTime(now)
                .setInsertTime(now);
        // 登录安全实体类
        LoginSecurityDO saveLoginSecurityDO = new LoginSecurityDO();
        saveLoginSecurityDO
                .setProject(project)
                .setPlatform(newPlatform)
                .setUsername(newUsername)
                .setDevice(newDevice)
                .setCityCode(newCityCode)
                .setLoginTime(now);

        // 登录ID
        String loginId = IdUtil.get20BitId() + "_" + UUID.randomUUID().toString().replace("-", "");
        String loginKey = RedisKeyConstant.VERIFY_LOGIN_KEY + loginId;
        // 是否绑定手机号
        Boolean existPhone = StringUtils.isNotEmpty(phone) ? Boolean.TRUE : Boolean.FALSE;
        String existPhoneStr = existPhone ? "1" : "0";
        // 是否绑定微信认证
        Boolean existWeChatAuth = loginWechatAuthService.isBindWechatAuth(newUsername);
        String existWeChatAuthStr = existWeChatAuth ? "1" : "0";
        // 获取微信授权地址
        String authId = IdUtil.get20BitId() + "_" + UUID.randomUUID().toString().replace("-", "");
        String authUrl = loginWechatAuthService.getAuthUrl(loginId, authId, newUsername, existWeChatAuth, LoginConstant.TYPE_LOGIN);
        // 当校验不通过的时候的响应结果
        VeriftLoginRespVO veriftLoginRespVO = new VeriftLoginRespVO();
        veriftLoginRespVO
                .setLoginId(loginId)
                .setAuthId(authId)
                .setPhone(phone)
                .setWeChatAuthUrl(authUrl)
                .setExistPhone(existPhone)
                .setExistWeChatAuth(existWeChatAuth);
        if (!existWeChatAuth && !existPhone) {
            setVeriftLoginRedis(loginKey, authStr, authId, newUsername, existPhoneStr, phone, existWeChatAuthStr, saveLoginHistoryDO, saveLoginSecurityDO);
            // 未绑定安全方式
            throw new BizException(ErrorCodeConstants.UNBOUND_SECURITY_METHOD, veriftLoginRespVO);
        }
        // 增加强制校验白名单限制
        LoginMandatoryVerifyDO loginMandatoryVerifyDO = loginMandatoryVerifyService.getById(newUsername);
        if (ObjectUtils.isNotEmpty(loginMandatoryVerifyDO)) {
            // 强制登录
            setVeriftLoginRedis(loginKey, authStr, authId, newUsername, existPhoneStr, phone, existWeChatAuthStr, saveLoginHistoryDO, saveLoginSecurityDO);
            throw new BizException(ErrorCodeConstants.LOGIN_MANDATORY_VERIFY, veriftLoginRespVO);
        }
        // 查询登录信息
        List<LoginSecurityDO> loginSecurityDOS = loginSecurityService.getList(project, newPlatform, newUsername);
        if (CollectionUtils.isEmpty(loginSecurityDOS)) {
            // 如果是账号第一次登录，直接放行即可
            loginSecurityService.save(saveLoginSecurityDO);
            loginHistoryService.save(saveLoginHistoryDO);
            return;
        }
        List<String> historyDevices = new ArrayList<>();
        List<String> historyCitys = new ArrayList<>();
        for (LoginSecurityDO loginSecurityDO : loginSecurityDOS) {
            // 比对是否是存在历史登录设备
            String oldDevice = loginSecurityDO.getDevice();
            String oldCityCode = loginSecurityDO.getCityCode();
            LocalDateTime oldLoginTime = loginSecurityDO.getLoginTime();
            if (Objects.equals(newDevice, oldDevice) && Objects.equals(newCityCode, oldCityCode)) {
                // 如果历史设备存在，查询是否超过30天未登录
                boolean currentTimeAfterDays = DateUtil.isCurrentTimeAfterDays(30, oldLoginTime);
                if (currentTimeAfterDays) {
                    loginSecurityDO.setLoginTime(now);
                    setVeriftLoginRedis(loginKey, authStr, authId, newUsername, existPhoneStr, phone, existWeChatAuthStr, saveLoginHistoryDO, loginSecurityDO);
                    // 长期未登录
                    throw new BizException(ErrorCodeConstants.LONG_TERM_NON_LOGIN, veriftLoginRespVO);
                } else {
                    // 30天内登录不校验
                    loginSecurityDO.setLoginTime(now);
                    loginSecurityService.changeById(loginSecurityDO);
                    loginHistoryService.save(saveLoginHistoryDO);
                    return;
                }
            }
            historyDevices.add(oldDevice);
            historyCitys.add(oldCityCode);
        }
        // 新设备登录
        if (!historyDevices.contains(newDevice)) {
            setVeriftLoginRedis(loginKey, authStr, authId, newUsername, existPhoneStr, phone, existWeChatAuthStr, saveLoginHistoryDO, saveLoginSecurityDO);
            throw new BizException(ErrorCodeConstants.NEW_DEVICE_LOGIN, veriftLoginRespVO);
        }
        if (Objects.equals(newCityCode, "000000")) {
            // 如果当前城市无法识别，则默认通过
            loginSecurityService.insertOrUpdate(saveLoginSecurityDO);
            loginHistoryService.save(saveLoginHistoryDO);
            return;
        }
        // 查询当前设备信息
        LoginSecurityDO loginSecurityDO = loginSecurityService.getByPlatformAndUsernameAndDevice(newPlatform, newUsername, newDevice, "000000");
        if (ObjectUtils.isEmpty(loginSecurityDO)) {
            // 异地登录
            setVeriftLoginRedis(loginKey, authStr, authId, newUsername, existPhoneStr, phone, existWeChatAuthStr, saveLoginHistoryDO, saveLoginSecurityDO);
            throw new BizException(ErrorCodeConstants.REMOTE_LOGIN, veriftLoginRespVO);
        }
        loginSecurityService.insertOrUpdate(saveLoginSecurityDO);
        loginHistoryService.save(saveLoginHistoryDO);
    }

    private void setVeriftLoginRedis(String key, String passStr, String authId, String username, String existPhone, String phone, String existWeChatAuth, LoginHistoryDO loginHistoryDO, LoginSecurityDO loginSecurityDO) {
        String loginSecurity = JSON.toJSONString(loginSecurityDO);
        String loginHistory = JSON.toJSONString(loginHistoryDO);
        phone = StringUtils.isNotEmpty(phone) ? phone : "";
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_AUTH_REDIS_KEY, passStr);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_AUTH_ID_REDIS_KEY, authId);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_USERNAME_REDIS_KEY, username);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_EXIST_PHONE_REDIS_KEY, existPhone);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_LOGIN_HISTORY_REDIS_KEY, loginHistory);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_LOGIN_SECURITY_REDIS_KEY, loginSecurity);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_PHONE_REDIS_KEY, phone);
        stringRedisTemplate.opsForHash().put(key, LoginConstant.VERIFY_LOGIN_EXIST_WECHAT_AUTH_REDIS_KEY, existWeChatAuth);
        stringRedisTemplate.expire(key, 30, TimeUnit.MINUTES);
    }

    /**
     * 获取登录人的省级id以及市级id
     *
     * @param username
     * @param platform
     * @return
     */
    @Override
    public BrandchIdRespVO getBranchId(String username, Integer platform) {
        BrandchIdRespVO brandchIdRespVO = new BrandchIdRespVO();
        //判断是不是手机号
        boolean b = validatePhoneNumber(username);
        if (b) {
//            MinPcLoginUserRespVO minPcLoginUserRespVO = miniPcLoginUserService.get(username, platform);
            if (platform == PLATFORM_ACCEPT) {
//                if(minPcLoginUserRespVO!=null){
//                    if(minPcLoginUserRespVO.getRoleId()==126L||(minPcLoginUserRespVO.getRoleId()==128L)) {//网点超管和客户经理
//                        SysAcceptDO byPhone = sysAcceptService.selectAcceptById(minPcLoginUserRespVO.getPlatformId());
//                        brandchIdRespVO.setAgentId(byPhone.getAgentId());
//                        brandchIdRespVO.setAcceptId(byPhone.getAcceptId());
//                        SysAgentDO sysAgentDO = sysAgentService.getAgentById(byPhone.getAgentId());
//                        brandchIdRespVO.setPartnerId(sysAgentDO.getPartnerId());
//                        if(StringUtils.isNotEmpty(sysAgentDO.getPartnerId())){
//                            PartnerChannelDO partnerChannelDO = partnerChannelService.selectPartnerById(sysAgentDO.getPartnerId());
//                            brandchIdRespVO.setBranchId(partnerChannelDO.getBranchOfficeId());
//                        }
//                    }
//                }else{
                //查询用户和角色
                AdminUserDO userByUsername = adminUserService.getUserByUsername(username);
                List<UserRoleDO> userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), platform);
                //判断是不是空
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    if (userRoleByPlatform.get(0).getRoleId() == 126L || userRoleByPlatform.get(0).getRoleId() == 128L) {//网点超管和客户经理
                        SysAcceptDO byPhone = sysAcceptService.selectAcceptById(userRoleByPlatform.get(0).getPlatformId());
                        brandchIdRespVO.setAgentId(byPhone.getAgentId());
                        brandchIdRespVO.setAcceptId(byPhone.getAcceptId());
                        SysAgentDO sysAgentDO = sysAgentService.getAgentById(byPhone.getAgentId());
                        brandchIdRespVO.setPartnerId(sysAgentDO.getPartnerId());
                        if (StringUtils.isNotEmpty(sysAgentDO.getPartnerId())) {
                            PartnerChannelDO partnerChannelDO = partnerChannelService.selectPartnerById(sysAgentDO.getPartnerId());
                            brandchIdRespVO.setBranchId(String.valueOf(partnerChannelDO.getBranchOfficeId()));
                        }
                    }
                }
//                }
            } else if (platform == PLATFORM_AGENT) {
//                if(minPcLoginUserRespVO!=null){
//                    if(minPcLoginUserRespVO.getRoleId()==123L||(minPcLoginUserRespVO.getRoleId()==125L)) {//区县超管和客户经理
//                        SysAgentDO sysAgentDO = sysAgentService.getAgentById(minPcLoginUserRespVO.getPlatformId());
//                        brandchIdRespVO.setAgentId(sysAgentDO.getAgentId());
//                        brandchIdRespVO.setPartnerId(sysAgentDO.getPartnerId());
//                        if(StringUtils.isNotEmpty(sysAgentDO.getPartnerId())){
//                            PartnerChannelDO partnerChannelDO = partnerChannelService.selectPartnerById(sysAgentDO.getPartnerId());
//                            brandchIdRespVO.setBranchId(partnerChannelDO.getBranchOfficeId());
//                        }
//                    }
//                }else{
                //查询用户和角色
                AdminUserDO userByUsername = adminUserService.getUserByUsername(username);
                List<UserRoleDO> userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), platform);
                if (userRoleByPlatform.get(0).getRoleId() == 123L || (userRoleByPlatform.get(0).getRoleId() == 125L)) {//区县超管和客户经理
                    SysAgentDO sysAgentDO = sysAgentService.getAgentById(userRoleByPlatform.get(0).getPlatformId());
                    brandchIdRespVO.setAgentId(sysAgentDO.getAgentId());
                    brandchIdRespVO.setPartnerId(sysAgentDO.getPartnerId());
                    if (StringUtils.isNotEmpty(sysAgentDO.getPartnerId())) {
                        PartnerChannelDO partnerChannelDO = partnerChannelService.selectPartnerById(sysAgentDO.getPartnerId());
                        brandchIdRespVO.setBranchId(String.valueOf(partnerChannelDO.getBranchOfficeId()));
                    }
                }
//                }

            } else if (platform == PLATFORM_PARTNER) {//市级
                String partner = "";
//                if(minPcLoginUserRespVO!=null){
//                    //根据缓存的平台id查询市级id
//                    brandchIdRespVO.setPartnerId(minPcLoginUserRespVO.getPlatformId());
//                    partner = minPcLoginUserRespVO.getPlatformId();
//                }else{
                //查询用户和角色
                AdminUserDO userByUsername = adminUserService.getUserByUsername(username);
                List<UserRoleDO> userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), platform);
                //判断是不是空
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    UserRoleDO userRoleDO = userRoleByPlatform.get(0);
                    brandchIdRespVO.setPartnerId(userRoleDO.getPlatformId());
                    partner = userRoleDO.getPlatformId();
                } else {
                    throw exception(USER_ROLE_ERROR);
                }
//                }
                //获取省级id根据市级id
                PartnerChannelDO byPartnerId = partnerChannelService.selectByPartnerIdAll(partner);
                if (byPartnerId != null) {
                    brandchIdRespVO.setBranchId(String.valueOf(byPartnerId.getBranchOfficeId()));
                    brandchIdRespVO.setPartnerName(byPartnerId.getCompany());
                }
            } else if (platform == PLATFORM_LARGE_MERCHANT) {//大商户
                String shopId = "";
                AdminUserDO userByUsername = adminUserService.getUserByUsername(username);
                List<UserRoleDO> userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), platform);
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    UserRoleDO userRoleDO = userRoleByPlatform.get(0);
                    shopId = userRoleDO.getPlatformId();
                    AgentSubDO byAgentId = agentSubService.getByTrueId(shopId);
                    if (byAgentId != null) {
                        brandchIdRespVO.setAgentSubId(byAgentId.getAgentId());
                        brandchIdRespVO.setAgentSubName(byAgentId.getCompany());
                    }
                } else {
                    throw exception(USER_ROLE_ERROR);
                }
            } else {
//                if(minPcLoginUserRespVO!=null){
//                    //根据缓存的平台id查询省级id
//                    if(StringUtils.isNotEmpty(minPcLoginUserRespVO.getPlatformId())){
//                        brandchIdRespVO.setBranchId(Integer.parseInt(minPcLoginUserRespVO.getPlatformId()));
//                    }else{
//                        brandchIdRespVO.setBranchId(null);
//                    }
//                }else{
                //查询用户和角色
                AdminUserDO userByUsername = adminUserService.getUserByUsername(username);
                List<UserRoleDO> userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), platform);
                //判断角色是不是空
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    UserRoleDO userRoleDO = userRoleByPlatform.get(0);
                    if (StringUtils.isNotEmpty(userRoleDO.getPlatformId())) {
                        brandchIdRespVO.setBranchId(userRoleDO.getPlatformId());
                    } else {
                        brandchIdRespVO.setBranchId(null);
                    }
                } else {
                    throw exception(USER_ROLE_ERROR);
                }
//                }
            }
        } else {
            //查询用户和角色
            AdminUserDO userByUsername = adminUserService.getUserByUsername(username);
            List<UserRoleDO> userRoleByPlatform = userRoleService.getUserRoleByPlatform(userByUsername.getId(), platform);
            if (platform == 4) {
                //查询用户和角色
                //判断是不是空
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    if ((userRoleByPlatform.get(0).getRoleId() == 126L || userRoleByPlatform.get(0).getRoleId() == 128L) ||
                            (userRoleByPlatform.get(1).getRoleId() == 126L || userRoleByPlatform.get(1).getRoleId() == 128L)) {//网点超管和客户经理
                        SysAcceptDO byPhone = sysAcceptService.selectAcceptById(userRoleByPlatform.get(0).getPlatformId());
                        brandchIdRespVO.setAgentId(byPhone.getAgentId());
                        brandchIdRespVO.setAcceptId(byPhone.getAcceptId());
                        SysAgentDO sysAgentDO = sysAgentService.getAgentById(byPhone.getAgentId());
                        brandchIdRespVO.setPartnerId(sysAgentDO.getPartnerId());
                        if (StringUtils.isNotEmpty(sysAgentDO.getPartnerId())) {
                            PartnerChannelDO partnerChannelDO = partnerChannelService.selectPartnerById(sysAgentDO.getPartnerId());
                            if (partnerChannelDO != null) {
                                brandchIdRespVO.setBranchId(String.valueOf(partnerChannelDO.getBranchOfficeId()));
                            } else {
                                brandchIdRespVO.setBranchId(null);
                            }
                        }
                    }
                }
            } else if (platform == 3) {
                for (UserRoleDO userRoleDO : userRoleByPlatform) {
                    if (userRoleDO.getRoleId() == 123L || (userRoleDO.getRoleId() == 125L)) {//区县超管和客户经理
                        SysAgentDO sysAgentDO = sysAgentService.getAgentById(userRoleByPlatform.get(0).getPlatformId());
                        brandchIdRespVO.setAgentId(sysAgentDO.getAgentId());
                        brandchIdRespVO.setPartnerId(sysAgentDO.getPartnerId());
                        if (StringUtils.isNotEmpty(sysAgentDO.getPartnerId())) {
                            PartnerChannelDO partnerChannelDO = partnerChannelService.selectPartnerById(sysAgentDO.getPartnerId());
                            if (partnerChannelDO != null) {
                                brandchIdRespVO.setBranchId(String.valueOf(partnerChannelDO.getBranchOfficeId()));
                            } else {
                                brandchIdRespVO.setBranchId(null);
                            }
                        }
                    }
                }
            } else if (platform == 2) {
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    //查询上级id
                    PartnerChannelDO byPartnerId = partnerChannelService.selectByPartnerIdAll(userRoleByPlatform.get(0).getPlatformId());
                    brandchIdRespVO.setPartnerId(userRoleByPlatform.get(0).getPlatformId());
                    if (byPartnerId != null) {
                        brandchIdRespVO.setBranchId(String.valueOf(byPartnerId.getBranchOfficeId()));
                        brandchIdRespVO.setPartnerName(byPartnerId.getCompany());
                    } else {
                        brandchIdRespVO.setBranchId(null);
                    }
                } else {
                    throw exception(USER_ROLE_ERROR);
                }
            } else {
                //判断角色是不是空
                if (CollectionUtils.isNotEmpty(userRoleByPlatform)) {
                    UserRoleDO userRoleDO = userRoleByPlatform.get(0);
                    if (StringUtils.isNotEmpty(userRoleDO.getPlatformId())) {
                        brandchIdRespVO.setBranchId(userRoleDO.getPlatformId());
                    } else {
                        brandchIdRespVO.setBranchId(null);
                    }
                } else {
                    throw exception(USER_ROLE_ERROR);
                }
            }
        }
        AdminUserDO user = adminUserService.getUser(getLoginUserId());
        brandchIdRespVO.setAccount(user.getUsername());
        return brandchIdRespVO;
    }


    /**
     * 登录校验
     */
    public void checkLogin(LoginType loginType, String username, Boolean check) {
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username;
        // 获取用户登录错误次数，默认为0 (可自定义限制策略 例如: key + username + ip)
        int errorNumber = ObjectUtil.defaultIfNull(RedisUtils.getCacheObject(errorKey), 0);
        // 锁定时间内登录 则踢出
        if (errorNumber >= maxRetryCount) {
            throw new ServiceException(10851, "用户频繁登录请稍后重试");
        }

        if (!check) {
            // 错误次数递增
            errorNumber++;
            RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(lockTime));
            // 达到规定错误次数 则锁定登录
            if (errorNumber >= maxRetryCount) {
                throw new ServiceException(10852, "您已经连续登录多次，为了您的账户安全，" + "请" + lockTime + "分钟后再次尝试");
            } else {
                // 未达到规定错误次数
                log.info("用户密码错误 ==> [次数]{}[用户名]{}", errorNumber, username);
                throw new ServiceException(10853, "登录失败，账号或密码不正确");
            }
        }
        // 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
    }

    /**
     * 登录发送验证码
     *
     * @param loginSendCodeReqVO
     * @return
     */
    @Override
    public LoginSendCodeRespVO sendCode(LoginSendCodeReqVO loginSendCodeReqVO) {
        String phone = loginSendCodeReqVO.getPhone();
        String loginId = loginSendCodeReqVO.getLoginId();
        String existPhone = String.valueOf(stringRedisTemplate.opsForHash().get(RedisKeyConstant.VERIFY_LOGIN_KEY + loginId, LoginConstant.VERIFY_LOGIN_EXIST_PHONE_REDIS_KEY));
        String username = String.valueOf(stringRedisTemplate.opsForHash().get(RedisKeyConstant.VERIFY_LOGIN_KEY + loginId, LoginConstant.VERIFY_LOGIN_USERNAME_REDIS_KEY));
        String mobile = String.valueOf(stringRedisTemplate.opsForHash().get(RedisKeyConstant.VERIFY_LOGIN_KEY + loginId, LoginConstant.VERIFY_LOGIN_PHONE_REDIS_KEY));
        // 如果非不存在并且两个手机号不相同，直接驳回
        if (ObjectUtils.notEqual(existPhone, LoginConstant.VERIFY_LOGIN_EXIST_PHONE_REDIS_VALUE_NOT_EXIST) && ObjectUtils.notEqual(mobile, phone)) {
            throw new ServiceException(PHONE_EXCEPTION);
        }

        String codeId = IdUtil.get20BitId() + "_" + UUID.randomUUID().toString().replace("-", "");

        com.alibaba.fastjson.JSONObject projectInfo = initConstantService.getProjectInfos();
        String payUrl = (String) projectInfo.get("pay_out");
        String link = payUrl + "/pay/sendphonemessage.do";
        com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
        int code = (int) ((Math.random() * 9 + 1) * 100000);
        message.put("code", code);
        com.alibaba.fastjson.JSONObject constantInfo = initConstantService.getConstantInfos();
        String signName = (String) constantInfo.get("aliyun_send_message_sign");
        String templateCode = (String) constantInfo.get("aliyun_send_message_tmp_id");
        String postStr = "phone=" + phone + "&message=" + message + "&signName=" + signName + "&templateCode=" + templateCode;
        log.info("[请求]登录发送验证码 ==> [调用][请求地址]{}[请求报文]{}", link, postStr);
        String respStr = HttpUrlConnectionToInterface.AccessUrl(link, postStr);
        log.info("[请求]登录发送验证码 ==> [调用][请求地址]{}[请求报文]{}[响应报文]{}", link, postStr, respStr);
        JSONObject respJson = JSON.parseObject(respStr);
        String resultCode = respJson.getString("result_code");
        if (ObjectUtils.notEqual(resultCode, "success")) {
            throw new ServiceException(SEND_CODE_FAIL);
        }
        // 保存到redis
        String codeKey = RedisKeyConstant.LOGIN_SEND_CODE + phone;
        String phoneKey = RedisKeyConstant.LOGIN_SEND_CODE + codeId;
        stringRedisTemplate.opsForValue().set(phoneKey, phone);
        stringRedisTemplate.opsForValue().set(codeKey, String.valueOf(code));
        stringRedisTemplate.expire(codeKey, 6, TimeUnit.MINUTES);
        stringRedisTemplate.expire(phoneKey, 5, TimeUnit.MINUTES);
        return new LoginSendCodeRespVO().setCodeId(codeId);
    }

    /**
     * 校验验证码
     *
     * @param loginVerifyCodeReqVO
     * @return
     */
    @Override
    public Object verifyCode(LoginVerifyCodeReqVO loginVerifyCodeReqVO) {
        String loginId = loginVerifyCodeReqVO.getLoginId();
        String codeId = loginVerifyCodeReqVO.getCodeId();
        String code = loginVerifyCodeReqVO.getCode();
        String codeKey = RedisKeyConstant.LOGIN_SEND_CODE + codeId;
        String loginKey = RedisKeyConstant.VERIFY_LOGIN_KEY + loginId;
        String phone = stringRedisTemplate.opsForValue().get(codeKey);
        if (org.apache.commons.lang3.StringUtils.isEmpty(phone)) {
            throw new ServiceException(GlobalErrorCodePc.LOGIN_CODE_LOSE_EFFICACY);
        }
        String correctCode = stringRedisTemplate.opsForValue().get(RedisKeyConstant.LOGIN_SEND_CODE + phone);
        if (ObjectUtils.notEqual(code, correctCode)) {
            throw new ServiceException(CODE_NOT_SAME);
        }
        String username = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_USERNAME_REDIS_KEY));
        String existPhone = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_EXIST_PHONE_REDIS_KEY));
        String loginHistory = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_LOGIN_HISTORY_REDIS_KEY));
        String loginSecurity = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_LOGIN_SECURITY_REDIS_KEY));
        String mobile = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_PHONE_REDIS_KEY));
        LoginHistoryDO loginHistoryDO = JSON.parseObject(loginHistory, LoginHistoryDO.class);
        LoginSecurityDO loginSecurityDO = JSON.parseObject(loginSecurity, LoginSecurityDO.class);
        // 如果非不存在并且两个手机号不相同，直接驳回
        if (ObjectUtils.notEqual(existPhone, LoginConstant.VERIFY_LOGIN_EXIST_PHONE_REDIS_VALUE_NOT_EXIST) && ObjectUtils.notEqual(mobile, phone)) {
            throw new ServiceException(PHONE_EXCEPTION);
        }
        if (ObjectUtils.notEqual(existPhone, LoginConstant.VERIFY_LOGIN_EXIST_PHONE_REDIS_VALUE_EXIST)) {
            // 更新user表
            AdminUserDO adminUserDO = new AdminUserDO();
            adminUserDO.setUsername(username).setMobile(phone);
            adminUserService.updateByUsername(adminUserDO);
            loginHistoryDO.setPhone(phone);
        }
        String auth = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_AUTH_REDIS_KEY));
        JSONObject data = JSON.parseObject(auth);
        loginHistoryService.save(loginHistoryDO);
        loginSecurityService.insertOrUpdate(loginSecurityDO);
        // 清理缓存
        stringRedisTemplate.delete(codeKey);
        stringRedisTemplate.delete(loginKey);
        stringRedisTemplate.delete(RedisKeyConstant.LOGIN_SEND_CODE + phone);
        return data;
    }

    /**
     * 微信认证刷新
     *
     * @param wechatAuthRefreshReqVO 登录刷新微信认证二维码请求对象
     */
    @Override
    public WechatAuthRefreshRespVO wechatAuthRefresh(WechatAuthRefreshReqVO wechatAuthRefreshReqVO) {
        String authId = wechatAuthRefreshReqVO.getAuthId();
        String loginId = wechatAuthRefreshReqVO.getLoginId();
        String authKey = RedisKeyConstant.WECHAT_AUTH + authId;
        boolean hasKey = Boolean.TRUE.equals(stringRedisTemplate.hasKey(authKey));
        if (hasKey) {
            throw new ServiceException(ErrorCodeConstants.FREQUENT_REFRESH);
        }
        String loginKey = RedisKeyConstant.VERIFY_LOGIN_KEY + loginId;
        boolean loginHasKey = Boolean.TRUE.equals(stringRedisTemplate.hasKey(loginKey));
        if (!loginHasKey) {
            throw new ServiceException(ErrorCodeConstants.LOGIN_TIMEOUT);
        }
        String username = (String) stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_USERNAME_REDIS_KEY);
        String existWeChatAuthStr = (String) stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_EXIST_WECHAT_AUTH_REDIS_KEY);
        String correctAuthId = (String) stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_AUTH_ID_REDIS_KEY);
        if (ObjectUtils.notEqual(authId, correctAuthId)) {
            throw new ServiceException(ErrorCodeConstants.LOGIN_TIMEOUT);
        }
        String newAuthId = IdUtil.get20BitId() + "_" + UUID.randomUUID().toString().replace("-", "");
        Boolean existWeChatAuth = Objects.equals(existWeChatAuthStr, "0") ? Boolean.FALSE : Boolean.TRUE;
        String authUrl = loginWechatAuthService.getAuthUrl(loginId, newAuthId, username, existWeChatAuth, LoginConstant.TYPE_LOGIN);
        stringRedisTemplate.opsForHash().put(loginKey, LoginConstant.VERIFY_AUTH_ID_REDIS_KEY, newAuthId);
        return new WechatAuthRefreshRespVO().setAuthId(newAuthId).setWeChatAuthUrl(authUrl);
    }

    /**
     * 微信授权状态
     *
     * @param wechatAuthStatusReqVO 微信授权状态
     * @return 响应
     */
    @Override
    public WechatAuthStatusRespVO wechatAuthStatus(WechatAuthStatusReqVO wechatAuthStatusReqVO) {
        WechatAuthStatusRespVO wechatAuthStatusRespVO = new WechatAuthStatusRespVO();
        String authId = wechatAuthStatusReqVO.getAuthId();
        String loginId = wechatAuthStatusReqVO.getLoginId();
        // 校验当前刷新是否允许
        String loginKey = RedisKeyConstant.VERIFY_LOGIN_KEY + loginId;
        boolean loginExist = Boolean.TRUE.equals(stringRedisTemplate.hasKey(loginKey));
        if (!loginExist) {
            throw new ServiceException(ErrorCodeConstants.LOGIN_TIMEOUT);
        }
        String correctAuthId = (String) stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_AUTH_ID_REDIS_KEY);
        if (ObjectUtils.notEqual(authId, correctAuthId)) {
            throw new ServiceException(ErrorCodeConstants.LOGIN_TIMEOUT);
        }
        String authKey = RedisKeyConstant.WECHAT_AUTH + authId;
        boolean authExist = Boolean.TRUE.equals(stringRedisTemplate.hasKey(authKey));
        // 失效
        if (!authExist) {
            wechatAuthStatusRespVO.setStatus(LoginConstant.EXPIRE);
        } else {
            String pass = (String) stringRedisTemplate.opsForHash().get(authKey, LoginConstant.VERIFY_PASS_REDIS_KEY);
            // 未使用
            if (Objects.equals(pass, LoginConstant.NOT_USE)) {
                wechatAuthStatusRespVO.setStatus(LoginConstant.NOT_USE);
            }
            // 已扫码
            if (Objects.equals(pass, LoginConstant.SCAN_CODE)) {
                wechatAuthStatusRespVO.setStatus(LoginConstant.SCAN_CODE);
            }
            // 失效
            if (Objects.equals(pass, LoginConstant.EXPIRE)) {
                wechatAuthStatusRespVO.setStatus(LoginConstant.EXPIRE);
                stringRedisTemplate.delete(authKey);

            }
            // 通过
            if (Objects.equals(pass, LoginConstant.PASS)) {
                String auth = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_AUTH_REDIS_KEY));
                JSONObject authJson = JSON.parseObject(auth);
                wechatAuthStatusRespVO.setStatus(LoginConstant.PASS).setAuth(authJson);
                stringRedisTemplate.delete(authKey);
                stringRedisTemplate.delete(loginKey);
            }

        }
        return wechatAuthStatusRespVO;
    }
}
