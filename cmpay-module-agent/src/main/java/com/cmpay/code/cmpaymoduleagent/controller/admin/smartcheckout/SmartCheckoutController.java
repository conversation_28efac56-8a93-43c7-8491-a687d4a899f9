package com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout;

import com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout.vo.ConfigSaveReqVO;
import com.cmpay.code.cmpaymoduleagent.service.smartcheckout.SmartCheckoutService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Title: SmartCheckoutController
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout
 * @Date 2025/8/19 15:27
 * @description: 智慧收银台控制层
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/smart/checkout")
public class SmartCheckoutController {

    @Resource
    private SmartCheckoutService smartCheckoutService;

    /**
     * 智慧收银台配置
     */
    @GetMapping("/config")
    public CommonResult<List<Map<String,Object>>> config(@RequestParam(value = "templateId",required = false) String templateId) {
        return CommonResult.success(smartCheckoutService.config(templateId));
    }


    /**
     * 智慧收银台配置
     */
    @PostMapping("/save")
    @PermitAll
    public CommonResult<?> save(@Valid @RequestBody ConfigSaveReqVO configSaveReqVO) {
        smartCheckoutService.save(configSaveReqVO);
        return CommonResult.success(null);
    }


}
