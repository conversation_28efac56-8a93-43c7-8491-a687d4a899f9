package com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Title: ConfigSaveReqVO
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout.vo
 * @Date 2025/8/21 11:07
 * @description: 保存配置请求VO
 */
@Data
@Accessors(chain = true)
public class ConfigSaveReqVO {
    @NotEmpty(message = "参数不能为空(rank)")
    private String rank;
    @NotEmpty(message = "参数不能为空(orgId)")
    private String orgId;
    @NotEmpty(message = "参数不能为空(templateId)")
    private String templateId;
    @NotEmpty(message = "参数不能为空(ids)")
    private List<Long> ids;

}
