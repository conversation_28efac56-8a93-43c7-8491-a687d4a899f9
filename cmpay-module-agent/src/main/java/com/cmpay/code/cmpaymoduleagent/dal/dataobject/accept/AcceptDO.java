package com.cmpay.code.cmpaymoduleagent.dal.dataobject.accept;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 网点用户 DO
 *
 * <AUTHOR>
 */
@TableName("accept")
@KeySequence("accept_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcceptDO {

    /**
     * 受理商id
     */
    @TableId(type = IdType.INPUT)
    private String acceptId;
    /**
     * 区县id
     */
    private String agentId;
    /**
     * 密码
     */
    private String password;
    /**
     * 归属地
     */
    private String keeper;
    /**
     * 归属电话
     */
    private String keeperphone;
    /**
     * 公司
     */
    private String company;
    /**
     * 公司电话
     */
    private String phone;
    /**
     * 地址
     */
    private String address;
    /**
     * rate_yirongma
     */
    private BigDecimal rateYirongma;
    /**
     * rate_yirongma_300
     */
    @TableField(value = "rate_yirongma_300")
    private BigDecimal rateYirongma300;
    /**
     * rate_yirongma_300up
     */
    @TableField(value = "rate_yirongma_300up")
    private BigDecimal rateYirongma300up;
    /**
     * rate_shoubei
     */
    private BigDecimal rateShoubei;
    /**
     * 插入时间
     */
    private LocalDateTime insertTime;
    /**
     * rate_fufeitong
     */
    private BigDecimal rateFufeitong;
    /**
     * rate_wx_micro
     */
    private BigDecimal rateWxMicro;
    /**
     * rate_jialian
     */
    private BigDecimal rateJialian;
    /**
     * rate_cloudpay
     */
    private BigDecimal rateCloudpay;
    /**
     * rate_cloudpay_50down
     */
    @TableField(value = "rate_cloudpay_50down")
    private BigDecimal rateCloudpay50down;
    /**
     * rate_chuanhua
     */
    private BigDecimal rateChuanhua;
    /**
     * rate_huifu
     */
    private BigDecimal rateHuifu;
    /**
     * rate_suixingfu
     */
    private BigDecimal rateSuixingfu;
    /**
     * rate_duolabao
     */
    private BigDecimal rateDuolabao;
    /**
     * rate_sub
     */
    private BigDecimal rateSub;
    /**
     * rate_subalipay
     */
    private BigDecimal rateSubalipay;
    /**
     * rate_subalipay
     */
    private BigDecimal rateXinhui;
    /**
     * rate_xinhui
     */
    private BigDecimal rateLiandong;
    /**
     * rate_ruiyinxin
     */
    private BigDecimal rateRuiyinxin;
    /**
     * rate_haike
     */
    private BigDecimal rateHaike;
    /**
     * rate_ccb
     */
    private BigDecimal rateCcb;
    /**
     * rate_subunionpay
     */
    private BigDecimal rateSubunionpay;
    /**
     * rate_sjb
     */
    private BigDecimal rateSjb;
    /**
     * rate_wft_hs
     */
    private BigDecimal rateWftHs;
    /**
     * rate_fubei
     */
    private BigDecimal rateFubei;
    /**
     * rate_pufabank
     */
    private BigDecimal ratePufabank;
    /**
     * rate_sxf_tq
     */
    private BigDecimal rateSxfTq;
    /**
     * rate_pabank
     */
    private BigDecimal ratePabank;
    /**
     * rate_tonglian
     */
    private BigDecimal rateTonglian;
    /**
     * rate_zjnx
     */
    private BigDecimal rateZjnx;
    /**
     * rate_lakala
     */
    private BigDecimal rateLakala;
    /**
     * rate_wangkebao
     */
    private BigDecimal rateWangkebao;
    /**
     * rate_lakala_mis
     */
    private BigDecimal rateLakalaMis;
    /**
     * rate_zhifu
     */
    private BigDecimal rateZhifu;
    /**
     * yz_org_id
     */
    private String yzOrgId;
    /**
     * rate_xiandai
     */
    private BigDecimal rateXiandai;
    /**
     * up_pwd_time
     */
    private LocalDateTime upPwdTime;
    /**
     * sjb_pid
     */
    private String sjbPid;
    /**
     * sjb_account
     */
    private String sjbAccount;
    /**
     * rate_dianxin
     */
    private BigDecimal rateDianxin;
    /**
     * 邮政数币通道
     */
    private BigDecimal rateYouzheng;
    /**
     * rate_yz_pay
     */
    private BigDecimal rateYzPay;
    /**
     * rate_ylsw
     */
    private BigDecimal rateYlsw;
    /**
     * 招商银行保底费率
     */
    private Long rateCmb;

}
