package com.cmpay.code.cmpaymoduleagent.dal.dataobject.accept;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 网点进件权限DTO模型
 * Created by 创建人 on 2023-10-24 16:02:58.
 */
@TableName("accept_right")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcceptRightDO {
    /**
     * 网点id
     */
    private String acceptId;
    /**
     * 开户权限：0开，1关
     */
    private Integer isMerchant;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否允许受理商支付；1：允许；0：不允许
     */
    private Integer isCanPay;
    /**
     * 是否显示商户补贴栏目；0:否；1:是。
     */
    private Integer isMerPackage;
    /**
     * 是否开通商户补贴管控修改权限,0:否,1:是
     */
    private Integer auditDataUpdate;
}