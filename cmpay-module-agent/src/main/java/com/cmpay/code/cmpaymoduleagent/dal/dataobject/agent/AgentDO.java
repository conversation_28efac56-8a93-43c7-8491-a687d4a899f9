package com.cmpay.code.cmpaymoduleagent.dal.dataobject.agent;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 区县分公司 DO
 *
 * <AUTHOR>
 */
@TableName("agent")
@KeySequence("agent_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentDO {

    /**
     * 主键，代理商id
     */
    @TableId(type = IdType.INPUT)
    private String agentId;
    /**
     * 代理商登录密码
     */
    private String password;
    /**
     * 负责人
     */
    private String keeper;
    /**
     * 负责人电话，登录电话
     */
    private String keeperphone;
    /**
     * 代理商或区县分公司名称
     */
    private String company;
    /**
     * 客服电话
     */
    private String phone;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 支付sign秘钥
     */
    private String apiKey;
    /**
     * 微信标准通道保底费率
     */
    private BigDecimal rateSub;
    /**
     * 注册时间
     */
    private LocalDateTime agentTime;
    /**
     * 作废
     */
    private Integer normalRebate;
    /**
     * 结算卡户名
     */
    private String cardName;
    /**
     * 结算卡号
     */
    private String cardNo;
    /**
     * 结算银行名称
     */
    private String bankName;
    /**
     * 结算支行名称
     */
    private String bankAddress;
    /**
     * 所属省
     */
    private String province;
    /**
     * 所属城市
     */
    private String city;
    /**
     * 是否SAAS
     */
    private Integer partnerAgent;
    /**
     * SAAS的appid，公众号
     */
    private String merchantAppid;
    /**
     * SAAS的appidse，公众号秘钥
     */
    private String merchantAppidse;
    /**
     * SAAS的审核模板id
     */
    private String merchantAuditTempId;
    /**
     * SAAS的支付模板id
     */
    private String merchantPayoverTempId;
    /**
     * SAAS的结算模板id
     */
    private String merchantPaidoutTempId;
    /**
     * SAAS的结算失败模板id
     */
    private String merchantPaidoutFailTempId;
    /**
     * SAAS的风控模板id
     */
    private String merchantRiskTempId;
    /**
     * SAAS的活动推送模板id
     */
    private String activityPerstoreBuyTempId;
    /**
     * SAAS的品牌名称
     */
    private String brand;
    /**
     * SAAS的品牌logo
     */
    private String brandLogo;
    /**
     * SAAS的公众号关注图片url
     */
    private String gzhImg;
    /**
     * 微信小微保底费率，已不再使用
     */
    private BigDecimal rateMicro;
    /**
     * 富有利楚保底费率
     */
    private BigDecimal rateLc;
    /**
     * 威富通通道保底费率
     */
    private BigDecimal rateWftXy;
    /**
     * 威富通D0保底费率
     */
    private BigDecimal rateWftXyD0;
    /**
     * 邢台银行保底费率
     */
    private BigDecimal rateXtbank;
    /**
     * 乐刷保底费率
     */
    private BigDecimal rateLeshua;
    /**
     * 所属渠道id/所属市级分公司id
     */
    private String partnerId;
    /**
     * 乐刷D0保底费率
     */
    private BigDecimal rateLeshuaD0;
    /**
     * 中汇保底费率
     */
    private BigDecimal rateZhonghui;
    /**
     * 微信小程序保底费率
     */
    private BigDecimal rateWxMicro;
    /**
     * 和融通保底费率
     */
    private BigDecimal rateYirongma;
    /**
     * 随行付保底费率
     */
    private BigDecimal rateSuixingfu;
    /**
     * 和融通300以下费率
     */
    @TableField(value = "rate_yirongma_300")
    private BigDecimal rateYirongma300;
    /**
     * 开户政策默认开户支付通道
     */
    private String defaultChannel;
    /**
     * 和融通300以上费率
     */
    @TableField(value = "rate_yirongma_300up")
    private BigDecimal rateYirongma300up;
    /**
     * 和融通业务员号
     */
    private String yrmSaleId;
    /**
     * 富有机构号
     */
    private String lcAgentId;
    /**
     * 富有机构秘钥
     */
    private String lcAgentKey;
    /**
     * 是否赋佳通道，0：否；1：是
     */
    private Integer isFj;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 收呗保底
     */
    private BigDecimal rateShoubei;
    /**
     * 是有允许开通受理商
     */
    private Integer isAccept;
    /**
     * 付费通保底费率
     */
    private BigDecimal rateFufeitong;
    /**
     * 微收银client_id
     */
    private String wsyClientId;
    /**
     * 嘉联保底
     */
    private BigDecimal rateJialian;
    /**
     * 是否允许开通微信小微，0:否；1：是。
     */
    private Integer isWxmicro;
    /**
     * 微信买单保底费率（腾讯云）
     */
    private BigDecimal rateCloudpay;
    /**
     * 微信买单50以下返佣费率
     */
    @TableField(value = "rate_cloudpay_50down")

    private BigDecimal rateCloudpay50down;
    /**
     * 传化保底
     */
    private BigDecimal rateChuanhua;
    /**
     * 汇付保底
     */
    private BigDecimal rateHuifu;
    /**
     * 哆啦宝保底费率
     */
    private BigDecimal rateDuolabao;
    /**
     * 支付回调地址
     */
    private String payNotifyUrl;
    /**
     * 支付宝直连保底
     */
    private BigDecimal rateSubalipay;
    /**
     * 信汇保底
     */
    private BigDecimal rateXinhui;
    /**
     * 联动优势保底
     */
    private BigDecimal rateLiandong;
    /**
     * 是否允许支付，1：允许；0：否
     */
    private Integer payStatus;
    /**
     * 是否允许关闭订单，1：允许；0：不允许
     */
    private Integer closeStatus;
    /**
     * 瑞银信保底费率
     */
    private BigDecimal rateRuiyinxin;
    /**
     * 海科保底费率
     */
    private BigDecimal rateHaike;
    /**
     * 建行保底费率
     */
    private BigDecimal rateCcb;
    /**
     * 云闪付直连保底费率
     */
    private BigDecimal rateSubunionpay;
    /**
     * 是否支持萤火虫插件；0:否；1：允许。
     */
    private Integer yhcRight;
    /**
     * 四九八保底费率
     */
    private BigDecimal rateSjb;
    /**
     * 威富通徽商保底费率
     */
    private BigDecimal rateWftHs;
    /**
     * 付呗保底费率
     */
    private BigDecimal rateFubei;
    /**
     * 浦发银行保底
     */
    private BigDecimal ratePufabank;
    /**
     * 随行付保底费率（天阙）
     */
    private BigDecimal rateSxfTq;
    /**
     * 平安银行保底费率
     */
    private BigDecimal ratePabank;
    /**
     * 通联保底费率
     */
    private BigDecimal rateTonglian;
    /**
     * 浙江农商行保底费率
     */
    private BigDecimal rateZjnx;
    /**
     * 客户自由通道下单接口url
     */
    private String osMicropayUrl;
    /**
     * 客户自由通道查询接口url
     */
    private String osQueryUrl;
    /**
     * 客户自由通道退款接口url
     */
    private String osRefundUrl;
    /**
     * 客户自由通道关闭订单接口url
     */
    private String osCloseUrl;
    /**
     * 拉卡拉保底费率
     */
    private BigDecimal rateLakala;
    /**
     * 业务员推荐人
     */
    private String agentYwy;
    /**
     * 旺客宝保底
     */
    private BigDecimal rateWangkebao;
    /**
     * 客户自由通道退款查询接口url
     */
    private String osRefundSearchUrl;
    /**
     * 拉卡拉云mis保底
     */
    private BigDecimal rateLakalaMis;
    /**
     * 智付保底
     */
    private BigDecimal rateZhifu;
    /**
     * 现代金控保底费率
     */
    private BigDecimal rateXiandai;
    /**
     * 最后更改密码时间
     */
    private LocalDateTime upPwdTime;
    /**
     * 邮政机构号
     */
    private String yzOrgId;
    /**
     * 电信保底费率
     */
    private BigDecimal rateDianxin;
    /**
     * 邮政数币通道费率
     */
    private BigDecimal rateYouzheng;
    /**
     * 数币直联通道
     */
    private BigDecimal rateYzPay;
    /**
     * 默认保底费率
     */
    private BigDecimal defaultBottomRate;
    /**
     * 投诉回调地址
     */
    private String riskNotifyUrl;

}
