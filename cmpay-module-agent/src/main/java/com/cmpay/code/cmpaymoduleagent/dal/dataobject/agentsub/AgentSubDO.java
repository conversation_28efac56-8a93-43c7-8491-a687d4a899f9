package com.cmpay.code.cmpaymoduleagent.dal.dataobject.agentsub;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 大商户 DO
 *
 * <AUTHOR>
@TableName("agent_sub")
@KeySequence("agent_sub_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentSubDO {

    @Schema(description = "主键ID")
    @TableId
    private Long id;

    @Schema(description = "商户号")
    private String trueid;

    @Schema(description = "大商户ID，对应merchant表sub_agent_id")
    private String agentId;

    @Schema(description = "网点ID")
    private String acceptId;

    @Schema(description = "区县ID")
    private String sourceAgentId;

    @Schema(description = "市ID")
    private String partnerId;

    @Schema(description = "省ID")
    private String branchId;

    @Schema(description = "负责人")
    private String keeper;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "银行卡号")
    private String card;

    @Schema(description = "银行卡开户人姓名")
    private String cardName;

    @Schema(description = "身份证号")
    private String identity;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "商户名称")
    private String company;

    @Schema(description = "商户简称")
    private String companyNickname;

    @Schema(description = "商户地址")
    private String address;

    @Schema(description = "退款模式")
    private Integer refundStatus;

    @Schema(description = "agentTime")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date agentTime;

    @Schema(description = "openid")
    private String openid;

    @Schema(description = "是否作为商户上传数据；0上送；1不上送；")
    private Integer isUploadManage;

}
