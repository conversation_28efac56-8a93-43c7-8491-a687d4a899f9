package com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditdata;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 批量管控商户明细DO模型
 * Created by 创建人 on 2023-07-26 17:06:56.
 */
@TableName("audit_data")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditDataDO {
    /**
     * 主键
     */
    @TableId
    private String id;
    /**
     * 批次编号
     */
    private String batchId;
    /**
     * 商户号
     */
    private String trueid;
    /**
     * 状态：0默认；-1否决；1审核通过；2审核中
     */
    private Integer status;
    /**
     * 记录导入时间
     */
    private Date insertTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 市级id
     */
    private String partnerId;
    /**
     * 区县级id
     */
    private String agentId;
    /**
     * 营业点id
     */
    private String acceptId;
    /**
     * 省级id
     */
    private String branchId;

}