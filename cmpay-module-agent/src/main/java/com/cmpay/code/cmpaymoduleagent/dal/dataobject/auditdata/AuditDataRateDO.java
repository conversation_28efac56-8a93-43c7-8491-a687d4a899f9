package com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditdata;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 审批费率DO模型
 * Created by 创建人 on 2023-07-28 10:44:51.
 */
@TableName("audit_data_rate")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditDataRateDO {
    /**
     * 关联audit_data主键
     */
    private String auditDataId;
    /**
     * 微信费率
     */
    private String wxRate;
    /**
     * 支付宝费率
     */
    private String aliRate;
    /**
     * 微信阈值以上费率
     */
    private String wxThresholdRate;
    /**
     * 支付宝阈值以上费率
     */
    private String aliThresholdRate;
    /**
     * 阈值
     */
    private Integer endAmt;
    /**
     * 补贴金额
     */
    private String subsidyQuota;
    /**
     * 费率包类型
     */
    private String packageType;
    /**
     * 费率包结束时间
     */
    private Date packageEndTime;
    /**
     * 月交易额
     */
    private String monthDetailsMoney;
    /**
     * 月累计补贴额
     */
    private String monthSubisydMoney;
    /**
     * 本月补贴金额
     */
    private String pSubsidyMoney;
    /**
     * 本月手续费费率
     */
    private String monthRate;
}