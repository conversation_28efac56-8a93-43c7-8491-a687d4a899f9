package com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditmsg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 审批信息DTO模型
 * Created by 创建人 on 2023-07-27 14:49:34.
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("audit_msg")
public class AuditMsgDO {
    /**
     * 主键自增
     */
    private Integer id;
    /**
     * 关联审批表主键(可以一条审批操作多次)
     */
    private String auditDataId;
    /**
     * 审批评论
     */
    private String auditMsg;
    /**
     * 修改状态：0驳回；1默认
     */
    private Integer auditStatus;
    /**
     * 是否审批：1是；0否
     */
    private Integer isExamine;
    /**
     * 操作人
     */
    private String auditAccount;
    /**
     * 审批时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date insertTime;
    /**
     * identity
     */
    private String identity;
}