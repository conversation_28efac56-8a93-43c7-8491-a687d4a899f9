package com.cmpay.code.cmpaymoduleagent.dal.dataobject.callchain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: CallChainDO
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaymoduleagent.dal.dataobject.callchain
 * @Date 2025/4/29 10:37
 * @description: 调用链实体类
 */
@Data
@TableName("call_chain")
@NoArgsConstructor
@AllArgsConstructor
public class CallChainDO {
    @TableId
    private Long id;
    private String scene;
    private String start;
    private String end;
    private Integer sort;
}
