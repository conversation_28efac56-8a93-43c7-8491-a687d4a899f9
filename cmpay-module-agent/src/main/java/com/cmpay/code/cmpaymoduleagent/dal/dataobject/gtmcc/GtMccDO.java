package com.cmpay.code.cmpaymoduleagent.dal.dataobject.gtmcc;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 国通行业类目DTO模型
 * Created by 创建人 on 2023-12-14 09:11:21.
 */
@TableName("gt_mcc")
@KeySequence("gt_mcc_seq")
@ToString(callSuper = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GtMccDO {
    
    @Schema(description = "id")
    private Integer id;
    
    @Schema(description = "businessType")
    private String businessType;
    
    @Schema(description = "firstMcc")
    private String firstMcc;
    
    @Schema(description = "firstName")
    private String firstName;
    
    @Schema(description = "secondMcc")
    private String secondMcc;
    
    @Schema(description = "secondName")
    private String secondName;
}

