package com.cmpay.code.cmpaymoduleagent.dal.dataobject.industry;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * 标签DTO模型
 * Created by 创建人 on 2024-01-03 16:52:52.
 */
@TableName("industry")
@KeySequence("industry_seq")
@ToString(callSuper = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndustryDO {

    /**
     * 行业id
     */
    private Integer indId;

    /**
     * 批次id
     */
    private Integer batchId;

    /**
     * 批次名称
     */
    private String batchName;

    /**
     * 一级分类名称
     */
    private String firstName;

    /**
     * 二级分类名称
     */
    private String secondName;

    /**
     * 行业名称
     */
    private String indName;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 级别；1一级分类；2二级分类；3标签；
     */
    @TableField("`rank`")
    private Integer rank;

    @Schema(description = "最小支持选择数量")
    private Integer chooseMin;

    @Schema(description = "最大支持选择数量")
    private Integer chooseMax;

    /**
     * 状态；0停用；1启用；
     */
    private Integer status;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 添加时间
     */
    private Date insertTime;
    /**
     * 标签顺序
     */
    private Integer serialNum;
}