package com.cmpay.code.cmpaymoduleagent.dal.dataobject.orgsmartcheckoutconfig;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 机构智慧收银台配置 DO
 *
 * <AUTHOR>
 */
@TableName("org_smart_checkout_config")
@KeySequence("org_smart_checkout_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrgSmartCheckoutConfigDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 智慧收银台ID
     */
    private Long smartCheckoutId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 插入时间
     */
    private LocalDateTime insertTime;

}
