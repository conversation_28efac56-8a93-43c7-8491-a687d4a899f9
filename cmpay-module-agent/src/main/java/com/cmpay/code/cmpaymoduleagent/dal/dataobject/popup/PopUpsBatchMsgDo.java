package com.cmpay.code.cmpaymoduleagent.dal.dataobject.popup;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024-12-06 11:36:00
 * @version: 1.0
 */
@TableName("pop_ups_batch_msg")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopUpsBatchMsgDo {
    /**
     * 弹窗通知批次号
     */
    @TableId(type = IdType.INPUT)
    private String noticeBatchId;

    private String trueid;

    /**
     * 商户号
     */
    private String shopId;

    /**
     * 手机号
     */
    private String phone;


    /**
     * 通知标题
     */
    private String noticeTitle;

    /**
     * 通知类型
     */
    private String popUpsType;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 永久关闭
     */
    private Integer foreverOver;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建时间
     */
    private String insertTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 通知显示权限
     */
    private String displayPer;

    /**
     * 通知内容
     */
    private String paramJson;

    /**
     * 市id
     */
    private String partnerId;

    private String agentId;

    private String acceptId;

    private Integer branchId;


    /**
     * 发送通知类型
     */
    private Integer type;
    /**
     * xmid
     */
    private String xmid;
    /**
     * 广告性质
     */
    private Integer quality;
    /**
     * 弹窗级别
     */
    private String bpaaType;

    private String isPermClose;
}
