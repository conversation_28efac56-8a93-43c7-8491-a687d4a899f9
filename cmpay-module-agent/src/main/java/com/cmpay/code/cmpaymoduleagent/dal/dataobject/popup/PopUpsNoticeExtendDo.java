package com.cmpay.code.cmpaymoduleagent.dal.dataobject.popup;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024-12-06 11:36:00
 * @version: 1.0
 */
@TableName("pop_ups_notice_extend")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopUpsNoticeExtendDo {
    /**
     * 弹窗通知批次号
     */
    @TableId(type = IdType.INPUT)
    private String noticeBatchId;

    /**
     * 身份
     */
    private String displayPer;

    /**
     *通知类型
     */
    private String popUpsType;

    /**
     *开始时间
     */
    private String startTime;
    /**
     *结束时间
     */
    private String endTime;
    /**
     * 创建时间
     */
    private String insertTime;
    /**
     * 弹窗文件url
     */
    private String noticeUrl;
    /**
     *弹窗模式
     */
    private Integer quality;
    /**
     * 图片连接
     */
    private String imageUrl;

    private Integer type;

    private String agentId;

    private String acceptId;

    private String branchId;

    private String partnerId;

    private String isPermClose;

    private Integer temClose;
}
