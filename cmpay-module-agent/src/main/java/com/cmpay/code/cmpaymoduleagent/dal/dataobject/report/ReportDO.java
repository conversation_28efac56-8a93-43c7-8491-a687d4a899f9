package com.cmpay.code.cmpaymoduleagent.dal.dataobject.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 省分与地市、省分与瑞银信报表信息 DO
 *
 * <AUTHOR>
 */
@TableName("download_report")
@KeySequence("download_report_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDO {

    /**
     * 序号
     */
    @TableId(type = IdType.INPUT)
    private String reportId;
    /**
     * 年份
     */
    private String reportYear;
    /**
     * 月份
     */
    private String reportMonth;
    /**
     * 省份标识：2-广东，3-广西等
     */
    private String branchId;
    /**
     * 省份名称
     */
    private String companyNickname;
    /**
     * 通道类型：ruiyinxin-瑞银信，sxf_tq-随行付
     */
    private String channel;
    /**
     * 市级机构id
     */
    private String partnerId;
    /**
     * 市级名称
     */
    private String partnerCompany;
    /**
     * 区县机构id
     */
    private String agentId;
    /**
     * 网点名称
     */
    private String agentCompany;
    /**
     * 网点机构id
     */
    private String acceptId;
    /**
     * 网点名称
     */
    private String acceptCompany;
    /**
     * 报表类型：sf_ds-省分与地市，sf_ryx-省分与瑞银信，oth_sf_ds-省分与地市（外省）
     */
    private String reportType;
    /**
     * 报表名称
     */
    private String reportName;
    /**
     * 下载地址
     */
    private String reportUrl;
    /**
     * 报表查询开始时间
     */
    private Date startTime;
    /**
     * 报表查询截止时间
     */
    private Date endTime;
    /**
     * 插入时间
     */
    private Date insertTime;
    /**
     * 备用字段1
     */
    private String expare1;
    /**
     * 备用字段2
     */
    private String expare2;
    /**
     * 备用字段3
     */
    private String expare3;

}