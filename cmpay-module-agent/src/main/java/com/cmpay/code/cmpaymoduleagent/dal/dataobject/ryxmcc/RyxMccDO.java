package com.cmpay.code.cmpaymoduleagent.dal.dataobject.ryxmcc;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 商户行业类型 DO
 *
 * <AUTHOR>
@TableName("ryx_mcc")
@KeySequence("ryx_mcc_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RyxMccDO {

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * ryx_type_up
     */
    private String ryxTypeUp;
    /**
     * ryx_type_dwon
     */
    private String ryxTypeDwon;
    /**
     * ryx_mcc
     */
    private String ryxMcc;
    /**
     * hulu_mcc
     */
    private String huluMcc;
    /**
     * 商户类型；1：企业；2：个体户；3：小微；4：其他；
     */
    private Integer businessType;
    /**
     * 最低阈值费率
     */
    private BigDecimal thresholdRate;
    /**
     * mcc政策类型，教育1；普通0； 彩票（瑞银信）2；彩票（随行付）3
     */
    private Integer mccType;
    /**
     * is_from_mini_app
     */
    private Integer isFromMiniApp;
    /**
     * gt_mcc
     */
    private String gtMcc;
}
