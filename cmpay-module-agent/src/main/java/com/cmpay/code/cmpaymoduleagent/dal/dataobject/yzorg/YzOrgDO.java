package com.cmpay.code.cmpaymoduleagent.dal.dataobject.yzorg;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 邮政机构表DO模型
 * Created by 创建人 on 2023-10-25 14:29:04.
 */
@TableName("yz_org")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YzOrgDO {
    /**
     * 网点id
     */
    @TableId
    private String acceptId;
    /**
     * 网点名称
     */
    private String companyAccept;
    /**
     * 网点机构号
     */
    private String yzOrgIdAccept;
    /**
     * 区县id
     */
    private String agentId;
    /**
     * 区县名称
     */
    private String companyAgent;
    /**
     * 区县机构号
     */
    private String yzOrgIdAgent;
    /**
     * 市级id
     */
    private String partnerId;
    /**
     * 市级名称
     */
    private String companyPartner;
    /**
     * 市级机构号
     */
    private String yzOrgIdPartner;
    /**
     * 分公司id
     */
    private Integer branchId;
    /**
     * 省公司名称
     */
    private String companyBranch;
    /**
     * 省际公司机构号
     */
    private String yzOrgIdBranch;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否参与统计，默认1：参与；0：不参与
     */
    private Integer isStatistics;
}