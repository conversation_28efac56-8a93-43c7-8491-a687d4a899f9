package com.cmpay.code.cmpaymoduleagent.dal.mysql.accept;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accept.vo.AcceptPageReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accept.vo.AcceptPageRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accept.AcceptDO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agent.AgentDO;
import com.cmpay.code.cmpaymoduleagent.service.orgname.dto.OrgInfoDTO;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 网点用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AcceptMapper extends BaseMapperX<AcceptDO> {


    /**
     * 查询所有网点超管用户
     */
    List<PartnerChannelDO> getAcceptUserAll();

    /**
     * 根据id查询accept对象
     *
     * @param acceptId id
     * @return 网点对象
     */
    AcceptDO selectAcceptById(@Param("acceptId") String acceptId);

    /**
     * 营业点管理——营业点管理——分页查询
     *
     * @param page       分页参数
     * @param acceptPage 查询条件
     * @return 分页对象
     */
    IPage<AcceptPageRespVo> searchAcceptPage(Page<AcceptPageRespVo> page, @Param("acceptPage") AcceptPageReqVo acceptPage);

//    default AcceptDO selectAcceptByAgentId(){
//
//    }

    @Update("update accept set password = #{pwd},up_pwd_time=now() where accept_id=#{acceptId}")
    void updateAcceptPassword(@Param("acceptId") String acceptId, @Param("pwd") String pwd);

    @Select("select accept_id from accept where agent_id=#{agentId}")
    List<String> selectByAgentId(@Param("agentId") String agentId);

    @Select("select accept_id from accept where keeperphone=#{phone}")
    String selectByPhone(@Param("phone") String phone);

    /**
     * 判断是否为初始超管，根据账号查询网点表
     *
     * @param account 登录账号
     * @return 网点表对象
     */
    List<AcceptDO> selectAccountByAccept(@Param("account") String account);

    List<String> selectByCompany(@Param("company") String company);

    /**
     * 根据网点id查询所有机构信息
     *
     * @param acceptId
     * @return
     */
    OrgInfoDTO selectOrgInfoByAcceptId(@Param("acceptId") String acceptId);


    /**
     * 根据区县id获取营业所具体信息
     *
     * @param agentId
     * @return
     */
    @Select("select * from accept where agent_id=#{agentId} order by UPDATE_TIME desc")
    List<AcceptDO> findByAgentId(@Param("agentId") String agentId);

    /**
     * 根据网点ID获取超管
     *
     * @param acceptId 网点ID
     * @return username用户名
     */
    String selectAcceptAdmin(@Param("acceptId") String acceptId);
}
