package com.cmpay.code.cmpaymoduleagent.dal.mysql.accept;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accept.AcceptRightDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface AcceptRightMapper extends BaseMapperX<AcceptRightDO> {

    @Update("update accept_right set is_can_pay=#{status} where accept_id=#{acceptId}")
    void updateByAcceptId(@Param("status") String status,@Param("acceptId") String acceptId);

    @Update("update accept_right set is_merchant=#{status} where accept_id=#{acceptId}")
    void updateMerchantByAcceptId(@Param("status") String status,@Param("acceptId") String acceptId);

    @Select("select is_can_pay,is_merchant from accept_right where accept_id=#{acceptId}")
    AcceptRightDO selectByAcceptId(@Param("acceptId")String acceptId);


}