package com.cmpay.code.cmpaymoduleagent.dal.mysql.accesstoken;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accesstoken.AccessTokenDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 访问令牌 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccessTokenMapper extends BaseMapperX<AccessTokenDO> {


    /**
     * 根据appid获取token
     * @param appid appid
     * @return token
     */
    AccessTokenDO getAccessToken(@Param("appid") String appid);

    /**
     * 修改token
     * @param accessTokenDO 修改对象
     */
    void updateAccessToken(@Param("accessTokenDO") AccessTokenDO accessTokenDO);

    /**
     * 新增token
     * @param accessTokenDO 新增对象
     */
    void insertAccessToken(@Param("accessTokenDO") AccessTokenDO accessTokenDO);
}
