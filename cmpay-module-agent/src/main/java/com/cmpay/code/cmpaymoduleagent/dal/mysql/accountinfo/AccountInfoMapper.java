package com.cmpay.code.cmpaymoduleagent.dal.mysql.accountinfo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accountinfo.vo.*;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accountinfo.AccountInfoDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 账户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountInfoMapper extends BaseMapperX<AccountInfoDO> {

    IPage<AccountInfoRespVO> getPage(@Param("page") Page<AccountInfoRespVO> page, @Param("req") AccountInfoPageReqVO accountInfoPageReqVO);

    IPage<AccountInfoRespVO> getPageMer(@Param("page") Page<AccountInfoRespVO> page, @Param("req") AccountInfoPageReqVO accountInfoPageReqVO);
    List<AccountInfoRespVO> getPage1(@Param("req") AccountInfoPageReqVO accountInfoPageReqVO);
    IPage<AccountInfoMerchantRespVO> getAccountMerPage(@Param("page")Page<AccountInfoMerchantRespVO> page, @Param("req") AccountMerchantReqVO accountMerchantReqVO);
    AccountInfoMerchantRespVO getAccountMer(@Param("shopId")String shopId);

    @Select("select * from account_info where account_identity = #{accountIdentity} and account_type =#{accountType} and status = '1'")
    AccountInfoDO selectByIdentity(@Param("accountIdentity")String accountIdentity,@Param("accountType")String accountType);

    @Select("select * from account_info where account_phone = #{accountPhone} and account_type =#{accountType} and status = '1'")
    AccountInfoDO selectByPhone(@Param("accountPhone")String accountPhone,@Param("accountType")String accountType);

    @Select("select * from account_info where account_card_no = #{accountCardNo} and account_type =#{accountType}")
    AccountInfoDO selectByAccountCardNo(@Param("accountCardNo")String accountCardNo,@Param("accountType")String accountType);
    List<AccountUserRespVO> getAccountUserList(@Param("shopId")String shopId);

    @Select("select * from account_info where account_id = #{accountId}")
    AccountInfoDO getByAccountId(@Param("accountId") String accountId);

    @Select("SELECT a1.* FROM account_info a1 LEFT JOIN account_merchant a2 ON a1.account_id = a2.account_id WHERE a2.shop_id = #{shopId} and a1.`status` = 1 Limit 1;")
    AccountInfoDO getInfoByShopId(@Param("shopId")String shopId);
}