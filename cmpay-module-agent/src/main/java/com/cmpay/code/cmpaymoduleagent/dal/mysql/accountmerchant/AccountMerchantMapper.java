package com.cmpay.code.cmpaymoduleagent.dal.mysql.accountmerchant;


import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accountmerchant.AccountMerchantDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 账户商户关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountMerchantMapper extends BaseMapperX<AccountMerchantDO> {

    @Select("select count(*) as total from account_merchant where account_id = #{accountId}")
    Integer getAccountMerchantTotal(@Param("accountId") String accountId);

    @Select("select * from account_merchant where account_id = #{accountId}")
    List<AccountMerchantDO> getListByAccountId(@Param("accountId") String accountId);
    @Select("select * from account_merchant where account_id = #{accountId} and shop_id = #{shopId}")
    AccountMerchantDO getByAccountIdAndShopId(@Param("accountId")String accountId,@Param("shopId")String shopId);

    @Delete("delete from account_merchant where account_id = #{accountId} and shop_id = #{shopId}")
    void deleteByAccountAndShopId(@Param("accountId")String accountId,@Param("shopId")String shopId);

    @Select("select * from account_merchant where shop_id = #{shopId} and status = '1'")
    List<AccountMerchantDO> selectByShopId(@Param("shopId")String shopId);

    @Select("select * from account_merchant where account_id = #{accountId}")
    AccountMerchantDO selectByAccountId(@Param("accountId")String accountId);

    @Select("select * from account_merchant where shop_id = #{shopId} and status = '1'")
    List<AccountMerchantDO> selectListByShopId(@Param("shopId")String shopId);

    @Update("update account_merchant set status = #{status} where account_id = #{accountId} and shop_id = #{shopId}")
    void updateStatus(@Param("status")String status,@Param("accountId")String accountId,@Param("shopId")String shopId);

    @Update("update account_merchant set status = #{status} where account_id = #{accountId}")
    void updateStatusByAccountId(@Param("status")String status,@Param("accountId")String accountId);
}