package com.cmpay.code.cmpaymoduleagent.dal.mysql.accountrecord;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accountrecord.vo.AccountRecordReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accountrecord.vo.AccountRecordRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accountrecord.vo.AccountRecordStatisticsRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentorders.vo.SearchAgentOrdersReqVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accountrecord.AccountRecordDO;
import com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.SearchOrdersReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.StatisticsOrdersNumberVo;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 补贴记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountRecordMapper extends BaseMapperX<AccountRecordDO> {

    IPage<AccountRecordRespVO> getPage(@Param("page") Page<AccountRecordRespVO> page, @Param("arrv") AccountRecordReqVO accountRecordReqVO);

    IPage<AccountRecordRespVO> getPageMer(@Param("page") Page<AccountRecordRespVO> page, @Param("arrv") AccountRecordReqVO accountRecordReqVO);
    List<AccountRecordRespVO> getMerList(@Param("arrv") AccountRecordReqVO accountRecordReqVO);

    BigDecimal getSubsidyMoneyByOrderId(@Param("orderId") String orderId);



    StatisticsOrdersNumberVo getOrderIdByShopId(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);

    StatisticsOrdersNumberVo selectOrderIdByShopId(@Param("ordersReqVo") SearchOrdersReqVo ordersReqVo);

    BigDecimal getSubsidyMoney(@Param("shopId") String shopId, @Param("orderId") String orderId, @Param("pay") String pay);

    List<AccountRecordDO> selectYesterdaySub(@Param("number")Integer number,@Param("shopId")String shopId);

    BigDecimal selectYesterdayAccountSub(@Param("number")Integer number,@Param("accountSubId")String accountSubId,@Param("shopId")String shopId);

    BigDecimal selectThirtySub(@Param("shopId") String shopId);

    BigDecimal selectThirtyAccountSub(@Param("accountId") String accountId,@Param("shopId")String shopId);

    List<AccountRecordStatisticsRespVO> selectThirtyStatistics(@Param("shopId") String shopId);

    List<AccountRecordStatisticsRespVO> selectThirtyStatisticsByType(@Param("accountId")String accountId,@Param("type")String type,@Param("shopId")String shopId);
}
