package com.cmpay.code.cmpaymoduleagent.dal.mysql.accountsub;

import com.cmpay.code.cmpaymoduleagent.controller.admin.accountinfo.vo.AccountMerSubRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accountinfo.vo.AccountUserSubReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accountinfo.vo.AccountUserSubRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accountsub.AccountSubDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 子账户资金 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountSubMapper extends BaseMapperX<AccountSubDO> {

    List<AccountSubDO> selectAccountSubByAccountId(@Param("accountId") String accountId,@Param("status") String status);

   List<AccountMerSubRespVO> selectAByAccountIdAndStatus(@Param("accountId") String accountId, @Param("status") String status);
    List<AccountUserSubRespVO> selectSubAndTaskByAccountId(@Param("req") AccountUserSubReqVO accountUserSubReqVO);

    @Update("update account_sub set status = #{status} where account_sub_id = #{accountSubId}")
    void updateStatus(@Param("status") String status,@Param("accountSubId") String accountSubId);

    @Select("select * from account_sub where account_sub_id = #{accountSubId}")
    AccountSubDO selectByAccountSubId(@Param("accountSubId")String accountSubId);

    BigDecimal selectFifteenExpiresSubsidy(@Param("accountId")String accountId);

    List<AccountSubDO> selectByAccountId(@Param("accountId")String accountId);


}