package com.cmpay.code.cmpaymoduleagent.dal.mysql.accounttask;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accounttask.vo.AccountTaskReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.accounttask.vo.AccountTaskRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accounttask.AccountTaskDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 账户任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountTaskMapper extends BaseMapperX<AccountTaskDO> {

    IPage<AccountTaskRespVO> getPage(@Param("page") Page<AccountTaskRespVO> page, @Param("atrv") AccountTaskReqVO accountTaskReqVO);

    IPage<AccountTaskRespVO> getPageMer(@Param("page") Page<AccountTaskRespVO> page, @Param("atrv") AccountTaskReqVO accountTaskReqVO);

    @Select("select * from account_task where id = #{id}")
    AccountTaskRespVO getById(@Param("id")String id);

    @Update("update account_task set status = #{status} where id = #{id}")
    void updateStatus(@Param("status")String status,@Param("id")String id);

    @Update("update account_task set status = #{status} where task_id = #{taskId} and (status = '0' or status = '4')")
    void updateStatusByTaskId(@Param("status") String status ,@Param("taskId") String taskId);

    @Update("update account_task set status = #{status} where task_id = #{taskId} and account_id = #{accountId}")
    void updateStatusByTaskIdAndAccountId(@Param("status")String status,@Param("taskId")String taskId, @Param("accountId")String accountId);

    @Select("select * from account_task where task_id = #{taskId} and account_id = #{accountId} and (status != '0' and status != '4')")
    AccountTaskDO selectByTaskIdAndAccountId(@Param("taskId") String taskId, @Param("accountId") String accountId);

    //用来账户充值批量调整查询
    @Select("select * from account_task where task_id = #{taskId} and account_id = #{accountId} and (status = '0' or status = '4')")
    AccountTaskDO selectByTaskIdAndAccount(@Param("taskId") String taskId, @Param("accountId") String accountId);

    @Select("select * from account_task where task_id = #{taskId} and shop_id = #{shopId} and (status = '0' or status = '4')")
    AccountTaskDO selectByTaskIdAndshopId(@Param("taskId") String taskId, @Param("shopId") String shopId);

    //用来商户账户批量充值校验
    @Select("select * from account_task where task_id = #{taskId} and shop_id = #{shopId}")
    AccountTaskDO selectByTaskIdAndshop(@Param("taskId") String taskId, @Param("shopId") String shopId);
    //用来客户账户批量充值校验
    @Select("select * from account_task where task_id = #{taskId} and account_id = #{accountId}")
    AccountTaskDO selectByAccount(@Param("taskId") String taskId, @Param("accountId") String accountId);

    @Select("select * from account_task where task_id = #{taskId} and (status = '0' or status = '4')")
    List<AccountTaskDO> selectByTaskId(@Param("taskId") String taskId);
    AccountTaskRespVO getByTaskId(@Param("taskId") String taskId,@Param("accountType") String accountType);

    @Select("select * from account_task where  account_sub_id = #{accountSubId} and task_id = #{taskId}")
    AccountTaskDO selectBySubIdAndTaskId(@Param("accountSubId") String accountSubId,@Param("taskId")String taskId);

    List<AccountTaskRespVO> getPageMerment(@Param("atrv") AccountTaskReqVO accountTaskReqVO);

    List<AccountTaskRespVO> getPageMe(@Param("atrv") AccountTaskReqVO accountTaskReqVO);
}