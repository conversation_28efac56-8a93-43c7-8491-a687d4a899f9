package com.cmpay.code.cmpaymoduleagent.dal.mysql.advertisement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisement.vo.AdvertisementPageReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisement.vo.AdvertisementPageRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.advertisement.AdvertisementDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 广告管理DB接口
 * Created by 创建人 on 2023-10-31 09:45:49
 */
@Mapper
public interface AdvertisementMapper extends BaseMapperX<AdvertisementDO> {


    IPage<AdvertisementPageRespVO> selectPage(@Param("page") Page<AdvertisementPageRespVO> page, @Param("apr") AdvertisementPageReqVO advertisementPageReqVO);

    List<AdvertisementPageRespVO> selectPage(@Param("apr") AdvertisementPageReqVO advertisementPageReqVO);

    List<AdvertisementPageRespVO> selectByType(@Param("adType")String adType);

    @Select("select * from advertisement where id = #{id} and status = 1")
    AdvertisementDO selcetLById(@Param("id")Integer id);


}