package com.cmpay.code.cmpaymoduleagent.dal.mysql.advertisementbpaa;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisement.dto.SelectByShopIdAndPlatformDTO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementbpaa.vo.AdvertisementBpaaPageReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementbpaa.vo.AdvertisementBpaaPageRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementbpaa.vo.AdvertisementBpaaUpdateVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.advertisementbpaa.AdvertisementBpaaDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 广告配置DB接口
 * Created by 创建人 on 2023-11-01 14:54:10
 */
@Mapper
public interface AdvertisementBpaaMapper extends BaseMapperX<AdvertisementBpaaDO> {


    IPage<AdvertisementBpaaPageRespVO> selectPage(@Param("page") Page<AdvertisementBpaaPageRespVO> page, @Param("ab") AdvertisementBpaaPageReqVO advertisementBpaaPageReqVO);

    @Update("update advertisement_bpaa set status = #{status} where id = #{id}")
    void updateStatus(@Param("id") String id, @Param("status") String status);

    List<AdvertisementBpaaPageRespVO> selectPage(@Param("ab") AdvertisementBpaaPageReqVO advertisementBpaaPageReqVO);

    /**
     * 根据商户号查询广告id
     *
     * @param shopId   商户号
     * @param platform 平台
     * @return long             广告id
     */
    SelectByShopIdAndPlatformDTO selectByShopIdAndPlatform(@Param("shopId") String shopId, @Param("platform") String platform);

    @Select("select * from advertisement_bpaa where id = #{id} and status = 1")
    AdvertisementBpaaDO selectDecById(@Param("id")String id);

    /**
     * 根据平台id和平台信息查询广告信息
     *
     * @param bpaaId   平台id
     * @param bpaaType 平台
     * @return AdvertisementBpaaDO 广告配置机构实体类
     */
    AdvertisementBpaaDO selectByBpaaIdAndBpaaType(@Param("bpaaId") String bpaaId, @Param("bpaaType") String bpaaType);

    /**
     * 根据平台id和投放平台查询信息查询平台信息
     * @param bpaaId
     * @return
     */
    List<AdvertisementBpaaUpdateVo> selectBybpaaId(@Param("bpaaId") String bpaaId, @Param("platform") String platform,@Param("adMiniType")String adMiniType);

    /**
     *
     * @param proportion
     * @param adId
     */
    @Update("UPDATE advertisement_bpaa SET proportion = #{proportion} WHERE id = #{adId}")
    void updateAdvertisementBpaa(@Param("proportion") Integer proportion, @Param("adId") Integer adId);

    @Update("UPDATE advertisement_bpaa SET proportion = #{proportion} WHERE id = #{Id}")
    void updateBpaa(@Param("proportion") Integer proportion, @Param("Id") String Id);

    String selectByadId(@Param("adId") Integer adId);

    @Select("SELECT * FROM advertisement_bpaa WHERE ad_id = #{id}")
    List<AdvertisementBpaaDO> getByAdvertisementBpaa(@Param("id") Integer id);

    @Select("SELECT bpaa_type FROM advertisement_bpaa WHERE bpaa_id = #{bpaaId}")
    String[] getBpaaType(@Param("bpaaId") String bpaaId);

    @Select("SELECT * FROM advertisement_bpaa")
    List<AdvertisementBpaaDO> getAll();



    @Update("UPDATE advertisement_bpaa SET proportion = #{proportion} WHERE bpaa_id = #{bpaaId} and bpaa_type = #{bpaaType} and ad_id = #{adId} and platform = #{platform}")
    void updateAdvertisementBpaaApi(@Param("proportion") Integer proportion, @Param("adId") String adId, @Param("bpaaId") String bpaaId,@Param("platform")String platform,@Param("bpaaType")String bpaaType);


    @Update("UPDATE advertisement_bpaa SET status = #{status} WHERE bpaa_id = #{bpaaId} and bpaa_type = #{bpaaType} and ad_id = #{adId} and platform = #{platform}")
    void updateAdvertisementStatus(@Param("status") Integer status, @Param("adId") String adId, @Param("bpaaId") String bpaaId,@Param("platform")String platform,@Param("bpaaType")String bpaaType);
}
