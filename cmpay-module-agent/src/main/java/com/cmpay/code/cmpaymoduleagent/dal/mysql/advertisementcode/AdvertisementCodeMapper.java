package com.cmpay.code.cmpaymoduleagent.dal.mysql.advertisementcode;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.advertisementcode.AdvertisementCodeDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 广告编号表DB接口
 * Created by 创建人 on 2023-11-01 10:12:38
 */
@Mapper
public interface AdvertisementCodeMapper extends BaseMapperX<AdvertisementCodeDO> {

    @Select("select * from advertisement_code where ad_code = #{adCode}")
    AdvertisementCodeDO selectByCode(@Param("adCode") String adCode);


}