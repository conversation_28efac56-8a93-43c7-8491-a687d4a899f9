package com.cmpay.code.cmpaymoduleagent.dal.mysql.advertisementlog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementlog.vo.AdvertisementBpaaRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementlog.vo.AdvertisementLogCreateVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementlog.vo.AdvertisementLogPageRepVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.advertisementlog.AdvertisementLogDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 广告配置DB接口
 * Created by <PERSON><PERSON><PERSON>
 */
@Mapper
public interface AdvertisementLogMapper extends BaseMapperX<AdvertisementLogDO>{

    IPage<AdvertisementLogPageRepVo> selectPage(@Param("page") Page<AdvertisementLogPageRepVo> page, @Param("a") AdvertisementBpaaRespVO advertisementBpaaRespVO);

    AdvertisementLogCreateVo searchAdvertisement(@Param("id") Long id);
}
