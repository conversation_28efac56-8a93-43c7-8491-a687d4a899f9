package com.cmpay.code.cmpaymoduleagent.dal.mysql.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agent.vo.*;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.accept.AcceptDO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agent.AgentDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 区县用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AgentMapper extends BaseMapperX<AgentDO> {

    List<AgentDO> getAllAgentUser();

    /**
     * 根据id获取agent对象
     *
     * @param agentId id
     * @return 区县对象
     */
    AgentDO selectAgentById(@Param("agentId") String agentId);

    /**
     * 判断是否为初始超管，根据账号查询区县表
     *
     * @param account 登录账号
     * @return 区县表对象
     */
    List<AgentDO> selectAccountByAgent(@Param("account") String account);

    /**
     * 修改区县用户密码
     *
     * @param agentId  区县id
     * @param password 密码
     */
    @Update("update agent set password = #{password} where agent_id = #{agentId}")
    void updateAgentPassword(@Param("agentId") String agentId, @Param("password") String password);

    /**
     * 区（县）级分公司管理——分页查询
     *
     * @param agentPage 查询条件
     * @return 区县分公司集合
     */
    IPage<AgentPageRespVo> searchAcceptPage(Page<AgentPageRespVo> page, @Param("agentPage") AgentPageReqVo agentPage);

    /**
     * 分店管理——分店管理查询
     *
     * @param deviceManagementPageReqVO 查询条件
     * @return 分页对象
     */
    IPage<DeviceManagementPageRespVO> getDeviceManagementPage(Page<DeviceManagementPageRespVO> page, @Param("deviceManagementPage") DeviceManagementPageReqVO deviceManagementPageReqVO);


    @Update("update agent set pay_status=#{status} where agent_id=#{agentId}")
    void updatePayStatusByAgentId(@Param("status") String status, @Param("agentId") String agentId);

    @Update("update agent set close_status=#{status} where agent_id=#{agentId}")
    void updateCloseStatusByAgentId(@Param("status") String status, @Param("agentId") String agentId);

    @Select("select agent_id from agent where keeperphone = #{keeperphone}")
    String selectByPhone(@Param("keeperphone") String keeperphone);

    IPage<AgentMsgRespVO> getAgentMsg(@Param("page") Page<AgentMsgRespVO> page, @Param("amrv") AgentMsgReqVO agentMsgReqVO);

    /**
     * 根据市id查询所有区县
     *
     * @param partnerId 市级id
     * @return 区县集合
     */
    List<AgentDO> findByPartnerId(@Param("partnerId") String partnerId);

    List<String> selectByCompany(@Param("company") String company);

    /**
     * 根据负责人手机号查询区县信息
     *
     * @param phone 手机号
     * @return 区县信息
     */
    AgentDO findByKeeperPhone(String phone);

    List<String> searchAgentId(@Param("partnerId") List<String> partnerId);

    List<AcceptDO> selectSichuanAgentList();


    String selectAgentAdmin(@Param("agentId") String agentId);
}
