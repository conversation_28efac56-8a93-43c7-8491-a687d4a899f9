package com.cmpay.code.cmpaymoduleagent.dal.mysql.agentcashier;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentcashier.vo.*;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agentcashier.AgentCashierDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import com.cmpay.code.module.system.dal.dataobject.user.AdminUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 区县/网点团队 Mapper
 *
 * <AUTHOR>
@Mapper
public interface AgentCashierMapper extends BaseMapperX<AgentCashierDO> {


    List<CashierUserDO> initAccountAll(@Param("agentCashierId") String agentCashierId);

    IPage<AgentCashierUserRespVO> selectCashierPage(@Param("page") Page<AgentCashierUserRespVO> page, @Param("acurq") AgentCashierUserReqVO agentCashierUserReqVO);
    @Update("update agent_cashier set status = #{status} where id = #{cashierId} ")
    void updateStatus(@Param("status") String status,@Param("cashierId") String cashierId);

    @Select("select b.phone from agent_cashier a,user b where a.openid=b.openid and a.id=#{cashierId} and b.phone = #{phone}")
    String selectPhone(@Param("cashierId") String cashierId, @Param("phone") String phone);
    @Update("update agent_cashier a,user b set a.pw=#{password} where a.openid=b.openid and b.phone=#{phone}")
    void updatePassword(@Param("password") String password,@Param("phone") String phone);

    /**
     * 查询system_user表中名称为空的数据
     * @return 用户集合
     */
    List<AdminUserDO> initUserByName();

    @Select("select ac.* from agent_cashier ac,user u where ac.openid = u.openid and u.phone = #{phone}")
    List<AgentCashierDO> findByPhone(@Param("phone") String phone);

    /**
     * 根据openid修改密码
     * @param password 密码
     * @param openid openid
     */
    @Update("update agent_cashier set pw=#{password} where openid = #{openid}")
    void updatePwByOpenId(@Param("password") String password, @Param("openid") String openid);

    /**
     * 获取该网点的业务员信息
     * @param acceptId 网点id
     * @param agentId 区县id
     * @return 业务员列表
     */
    List<AgentCashierDO> getCashierByAcceptId(@Param("acceptId") String acceptId, @Param("agentId") String agentId);

    List<RealTimeNFCLayResqVo> selectRealTime(@Param("reqVo") RealTimeNFCLayReqVo reqVo);

    IPage<RealTimeNFCLayResqVo> selectRealTime(@Param("page") Page<RealTimeNFCLayResqVo> page, @Param("reqVo") RealTimeNFCLayReqVo reqVo);
    @Select("select * from agent_cashier where openid = #{openId} and `status` = '1' ORDER BY `insert_time` DESC LIMIT 1;")
    AgentCashierDO selectByOpenId(@Param("openId") String openId);

    List<String> getOpenIdByAgentId(@Param("agentId") List<String> agentId);

    @Select("select openid from agent_cashier where accept_id = #{acceptId} and `status` = 1 ;")
    List<String> getOpenIdByAcceptId(@Param("acceptId") String acceptId);

    void updateIsLookShopIdStatus(@Param("cashierId") String cashierId, @Param("status") String status);

    String getIsLookStatus(@Param("ywyOpenid") String ywyOpenid, @Param("agentId") String agentId, @Param("acceptId") String acceptId);
}
