package com.cmpay.code.cmpaymoduleagent.dal.mysql.agentmerchantextend;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agentmerchantextend.AgentMerchantExtendDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商户扩展 Mapper
 *
 * <AUTHOR>
@Mapper
public interface AgentMerchantExtendMapper extends BaseMapperX<AgentMerchantExtendDO> {


    /**
     * 查询瑞信付状态码
     * @param shopId  商户id
     * @return 商户通过的码，瑞信付状态码
     */
    AgentMerchantExtendDO searchRxfCode(@Param("shopId") String shopId);


}
