package com.cmpay.code.cmpaymoduleagent.dal.mysql.agentmerchantmsgupdaterecord;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentmerchantmsgupdaterecord.vo.RateUpdateRecordReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentmerchantmsgupdaterecord.vo.RateUpdateRecordRespVo;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantmsgupdaterecord.MerchantMsgUpdateRecordDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商户信息更新记录 Mapper
 *
 * <AUTHOR>
@Mapper
public interface AgentMerchantMsgUpdateRecordMapper extends BaseMapperX<MerchantMsgUpdateRecordDO> {


    /**
     * 代理商商户管理——查询费率修改记录
     *
     * @param page        分页参数
     * @param recordReqVo 请求参数
     * @return 分页对象
     */
    IPage<RateUpdateRecordRespVo> searchRateUpdateRecord(Page<RateUpdateRecordRespVo> page, @Param("recordReqVo") RateUpdateRecordReqVo recordReqVo);
}
