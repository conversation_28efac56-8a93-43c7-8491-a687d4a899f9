package com.cmpay.code.cmpaymoduleagent.dal.mysql.agentorders;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentorders.vo.AgentOrdersDetailVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentorders.vo.SearchAgentOrdersReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.StatisticsOrdersNumberVo;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.orders.OrdersDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-11 9:07:54
 * @version: 1.0
 */
@Mapper
public interface AgentOrdersMapper extends BaseMapperX<OrdersDO> {

    /**
     * 代理商交易信息——查询成功订单信息
     * @param page 分页查询
     * @param agentOrdersReqVo 查询条件
     * @return 分页对象
     */
    IPage<AgentOrdersDetailVo> searchAgentOrders(Page<AgentOrdersDetailVo> page, @Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);

    /**
     * 代理商交易信息——查询成功订单信息
     * @param agentOrdersReqVo 查询条件
     * @return 分页对象
     */
    List<AgentOrdersDetailVo> searchAgentOrders(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);


    /**
     * 代理商交易信息——查询失败订单信息
     *
     * @param agentOrdersReqVo 查询条件
     * @param search
     * @return 分页数据
     */
    List<AgentOrdersDetailVo> failureAgentOrderList(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo, @Param("type") String type);

    /**
     * 代理商交易信息——查询订单信息——统计所有支付订单
     *
     * @param agentOrdersReqVo 查询条件
     * @return 统计对象
     */
    StatisticsOrdersNumberVo getAllAgentOrders(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);

    /**
     * 代理商交易信息——查询订单信息——统计所有退款订单
     *
     * @param agentOrdersReqVo 查询条件
     * @return 统计对象
     */
    StatisticsOrdersNumberVo getAllAgentRefundOrders(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);
}
