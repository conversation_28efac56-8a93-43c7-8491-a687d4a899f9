package com.cmpay.code.cmpaymoduleagent.dal.mysql.agentstatisticsmerchant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentstatisticsmerchant.vo.*;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agentstatisticsmerchant.AgentStatisticsMerchantDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统计交易信息 Mapper
 *
 * <AUTHOR>
@Mapper
public interface AgentStatisticsMerchantMapper extends BaseMapperX<AgentStatisticsMerchantDO> {


    /**
     * 代理商统计管理——交易统计查询——商户查询分页
     * @param page 分页参数
     * @param merchantStatisticsOrdersReqVo 查询条件
     */
    IPage<MerchantStatisticsOrdersVo> searchMerchantStatisticsOrders(Page<MerchantStatisticsOrdersVo> page, @Param("merchantStatisticsOrdersReqVo") MerchantStatisticsOrdersReqVo merchantStatisticsOrdersReqVo);

    /**
     * 代理商统计管理——交易统计查询——业务员查询分页
     * @param page 分页参数
     * @param merchantStatisticsOrdersReqVo 查询条件
     */
    IPage<MerchantStatisticsOrdersVo> searchCashierStatisticsOrders(Page<MerchantStatisticsOrdersVo> page,@Param("merchantStatisticsOrdersReqVo") MerchantStatisticsOrdersReqVo merchantStatisticsOrdersReqVo);

    /**
     * 代理商统计管理——交易统计查询——商户统计订单
     * @param merchantStatisticsOrdersReqVo 查询条件
     */
    List<MerchantStatisticsOrdersVo> searchMerchantStatisticsOrders(@Param("merchantStatisticsOrdersReqVo") MerchantStatisticsOrdersReqVo merchantStatisticsOrdersReqVo);

    /**
     * 代理商统计管理——交易统计查询——业务员统计订单
     *
     * @param merchantStatisticsOrdersReqVo 查询条件
     */
    List<MerchantStatisticsOrdersVo> searchCashierStatisticsOrders(@Param("merchantStatisticsOrdersReqVo") MerchantStatisticsOrdersReqVo merchantStatisticsOrdersReqVo);

    /**
     * 统计商户明细——分页
     *
     * @param page  分页参数
     * @param merchantDetailsReqVo 参数
     * @return 分页
     */
    IPage<StatisticalMerchantDetailsRespVo> details(Page<StatisticalMerchantDetailsRespVo> page, @Param("merchantDetailsReqVo") StatisticalMerchantDetailsReqVo merchantDetailsReqVo);

    /**
     * 统计商户明细
     *
     * @param merchantDetailsReqVo 参数
     * @return 所有数据集合
     */
    List<StatisticalMerchantDetailsRespVo> details(@Param("merchantDetailsReqVo") StatisticalMerchantDetailsReqVo merchantDetailsReqVo);


    /**
     * 代理商统计管理——交易统计查询——统计商户数据
     *
     * @param merchantStatisticsOrdersReqVo 查询条件
     */
    StatisticsOrdersDetailVo statisticsMerchantStatisticsOrders(@Param("merchantStatisticsOrdersReqVo") MerchantStatisticsOrdersReqVo merchantStatisticsOrdersReqVo, @Param("type") String type);
}
