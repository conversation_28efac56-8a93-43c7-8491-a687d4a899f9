package com.cmpay.code.cmpaymoduleagent.dal.mysql.agentsub;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentsub.vo.AgentSubPageReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentsub.vo.AgentSubPageRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentsub.vo.AgentSubRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.agentsub.AgentSubDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 大商户 Mapper
 *
 * <AUTHOR>
@Mapper
public interface AgentSubMapper extends BaseMapperX<AgentSubDO> {


    /**
     * 大商户管理——分页查询
     * @param page 分页参数
     * @param pageVO  查询条件
     * @return 分页对象
     */
    IPage<AgentSubPageRespVO> getAgentSubPage(Page<AgentSubPageRespVO> page,@Param("pageVO") AgentSubPageReqVO pageVO);

    AgentSubDO selectByPhone(@Param("phone")String phone);

    AgentSubDO selectByAgentId(@Param("agentId")String agentId);

    List<AgentSubRespVO> selectByOrgId(@Param("branchId")String branchId, @Param("partnerId")String partnerId, @Param("agentId")String agentId, @Param("acceptId")String acceptId);

    AgentSubDO getByTrueId(@Param("shopId") String shopId);

    List<AgentSubDO> synchronizeAffiliationMerchant();
}
