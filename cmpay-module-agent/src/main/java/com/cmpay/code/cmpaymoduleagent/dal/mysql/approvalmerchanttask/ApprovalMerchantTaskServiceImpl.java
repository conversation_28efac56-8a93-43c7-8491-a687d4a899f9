package com.cmpay.code.cmpaymoduleagent.dal.mysql.approvalmerchanttask;

import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.ApprovalDetailVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.approvalmerchanttask.ApprovalMerchantTaskMapper;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.approvalmerchanttask.ApprovalMerchantTaskDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ApprovalMerchantTaskServiceImpl implements ApprovalMerchantTaskService {
    @Resource
    private ApprovalMerchantTaskMapper approvalMerchantTaskMapper;


    @Override
    public ApprovalDetailVO getApprovalMerchantTaskByApprovalId(String approvalId) {
        return approvalMerchantTaskMapper.getApprovalMerchantTaskByApprovalId(approvalId);
    }

    @Override
    public void updateByApprovalId(String approvalId, Integer status, String time) {
        approvalMerchantTaskMapper.updateByApprovalId(approvalId, status, time);
    }
}
