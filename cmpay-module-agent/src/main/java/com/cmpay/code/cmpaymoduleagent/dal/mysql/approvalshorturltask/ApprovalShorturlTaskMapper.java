package com.cmpay.code.cmpaymoduleagent.dal.mysql.approvalshorturltask;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.ApprovalDetailVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.approvalshorturltask.ApprovalShorturlTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ApprovalShorturlTaskMapper extends BaseMapper<ApprovalShorturlTaskDO> {
    @Select("select * from approval_shorturl_task where approval_id=#{approvalId}")
    ApprovalDetailVO getApprovalShorturlTaskByTaskId(@Param("approvalId") String approvalId);
}
