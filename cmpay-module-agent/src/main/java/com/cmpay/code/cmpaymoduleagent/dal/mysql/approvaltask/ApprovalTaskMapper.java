package com.cmpay.code.cmpaymoduleagent.dal.mysql.approvaltask;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.ApprovalTaskProcessrVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.UpdateApprovalReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.approvaltask.ApprovalTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface ApprovalTaskMapper extends BaseMapper<ApprovalTaskDO> {
    @Select(" select * from approval_task where approval_id=#{approvalId} and is_allow=1")
    ApprovalTaskDO getApprovalTaskById(@Param("approvalId") String approvalId);

    @Select(" select * from approval_task where approval_id=#{approvalId} and inst_level=#{instLevel} and is_allow=1")
    ApprovalTaskDO getApprovalTaskByIdAndLevel(@Param("approvalId") String approvalId, @Param("instLevel") String instLevel);

    @Select(" SELECT * FROM approval_task " +
            "WHERE approval_id = #{approvalId} " +
            "ORDER BY FIELD(inst_level, 'accept', 'agent', 'partner', 'branch');")
    List<ApprovalTaskProcessrVO> getApprovalTaskByInstApprovalId(@Param("approvalId") String approvalId);

    @Select(" select * from approval_task where approval_id=#{approvalId} and is_allow=1")
    ApprovalTaskDO getApprovalTaskByAllowStatus(@Param("approvalId") String approvalId);

    @Update(" update approval_task set status = #{updateApprovalReqVO.status},approval_opinions = #{updateApprovalReqVO.approvalOpinions},finish_time = #{updateApprovalReqVO.finishTime},is_allow=0,approval_account=#{updateApprovalReqVO.account} where approval_id =#{updateApprovalReqVO.approvalId}")
    void updateApprovalTaskById(@Param("updateApprovalReqVO") UpdateApprovalReqVO updateApprovalReqVO);

    @Update("UPDATE approval_task SET status = #{vo.status}, approval_opinions = #{vo.approvalOpinions}, " +
            "finish_time = #{vo.finishTime}, is_allow = 0, approval_account = #{vo.account} " +
            "WHERE approval_id = #{vo.approvalId} AND inst_level = #{vo.instLevel}")
    void updateApprovalTaskByIdAndLevel(@Param("vo") UpdateApprovalReqVO vo);

    @Update(" update approval_task set is_allow=1 where approval_id =#{updateApprovalReqVO.approvalId} and inst_level=#{updateApprovalReqVO.instLevel}")
    void updateApprovalTaskIsAllowById(@Param("updateApprovalReqVO") UpdateApprovalReqVO updateApprovalReqVO);
}
