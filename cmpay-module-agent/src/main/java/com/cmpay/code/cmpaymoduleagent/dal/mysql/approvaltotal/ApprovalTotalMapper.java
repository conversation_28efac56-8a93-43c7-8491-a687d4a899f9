package com.cmpay.code.cmpaymoduleagent.dal.mysql.approvaltotal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.*;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.approvaltotal.ApprovalTotalDO;
import org.apache.ibatis.annotations.*;

@Mapper
public interface ApprovalTotalMapper extends BaseMapper<ApprovalTotalDO> {
    IPage<ApprovalTotalRespVO> selectApprovalTotal(Page<ApprovalTotalRespVO> page, @Param("approvalTotalReqVO") ApprovalTotalReqVO approvalTotalReqVO);

    @Update(" update approval_total set approval_status=#{updateApprovalReqVO.status},reject_msg=#{updateApprovalReqVO.approvalOpinions},update_time=#{updateApprovalReqVO.finishTime} where approval_id=#{updateApprovalReqVO.approvalId}")
    void updateApprovalStatusAndMsg(@Param("updateApprovalReqVO") UpdateApprovalReqVO updateApprovalReqVO);

    @Update(" update approval_total set approval_status=#{updateApprovalReqVO.status},update_time=#{updateApprovalReqVO.finishTime} where approval_id=#{updateApprovalReqVO.approvalId}")
    void updateApprovalStatus(@Param("updateApprovalReqVO") UpdateApprovalReqVO updateApprovalReqVO);

    @Update("update approval_total set update_time=#{time} where approval_id=#{approvalId}")
    void updatTime(@Param("approvalId") String approvalId,@Param("time") String time);

    @Select(" select u.name,a.insert_time,a.approval_status,a.branch_id,a.approval_type," +
            "a.partner_id," +
            "a.agent_id," +
            "a.accept_id from approval_total a,`user` u where a.xmid = u.xmid and a.approval_id=#{approvalId};")
    ViewProcessrRespVO getProcessByApprovalId(@Param("approvalId") String approvalId);

    @Select(" select * from approval_total where approval_id=#{approvalId}")
    ApprovalTotalDO selectApprovalTotalByApprovalId(@Param("approvalId") String approvalId);

    IPage<ApprovalYwyRespVO> selectApprovalYwy(IPage<ApprovalYwyRespVO> page,@Param("vo") ApprovalYwyReqVO approvalYwyReqVO);

    int getCountByStatus(@Param("vo") ApprovalYwyReqVO approvalYwyReqVO,@Param("status") int status);

    @Select(" select * from approval_total where approval_id = #{approvalId} ")
    ApprovalTotalDO selectByApprovalId(@Param("approvalId") String approvalId);
}
