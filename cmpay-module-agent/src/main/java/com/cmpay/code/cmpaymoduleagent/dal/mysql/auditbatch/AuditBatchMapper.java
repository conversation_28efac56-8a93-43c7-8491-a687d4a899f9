package com.cmpay.code.cmpaymoduleagent.dal.mysql.auditbatch;

import com.cmpay.code.cmpaymoduleagent.controller.admin.auditdata.vo.AuditBatchRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditbatch.AuditBatchDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批量审核DB接口
 * Created by 创建人 on 2023-07-27 15:28:24
 */
@Mapper
public interface AuditBatchMapper extends BaseMapperX<AuditBatchDO> {

    List<AuditBatchRespVO> selectBatchByBranchId(@Param("branchId") String branchId);


}