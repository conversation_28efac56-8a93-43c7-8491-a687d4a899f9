package com.cmpay.code.cmpaymoduleagent.dal.mysql.auditdata;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.auditdata.vo.AuditDataReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.auditdata.vo.AuditDataRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditdata.AuditDataDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批量管控商户明细DB接口
 * Created by 创建人 on 2023-07-26 17:06:56
 */
@Mapper
public interface AuditDataMapper extends BaseMapperX<AuditDataDO> {

    IPage<AuditDataRespVO> selectPage(@Param("page")Page<AuditDataRespVO> page, @Param("adrv")AuditDataReqVO auditDataReqVO);

    List<AuditDataRespVO> selectPage(@Param("adrv")AuditDataReqVO auditDataReqVO);

    AuditDataRespVO selectAuditDataById(@Param("id") String id);
}