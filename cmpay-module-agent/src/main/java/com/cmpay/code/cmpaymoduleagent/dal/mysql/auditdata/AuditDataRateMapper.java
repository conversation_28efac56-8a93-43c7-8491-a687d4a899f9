package com.cmpay.code.cmpaymoduleagent.dal.mysql.auditdata;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditdata.AuditDataRateDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 审批费率DB接口
 * Created by 创建人 on 2023-07-28 10:44:51
 */
@Mapper
public interface AuditDataRateMapper extends BaseMapperX<AuditDataRateDO> {

    AuditDataRateDO selectAuditDataById(@Param("id") String id);
}