package com.cmpay.code.cmpaymoduleagent.dal.mysql.auditimporturl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.auditimporturl.vo.AuditImportUrlReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.auditimporturl.vo.AuditImportUrlRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditimporturl.AuditImportUrlDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AuditImportUrlMapper extends BaseMapperX<AuditImportUrlDO> {

    IPage<AuditImportUrlRespVO> selectAllPage(@Param("page") Page<AuditImportUrlRespVO> page,@Param("aiu") AuditImportUrlReqVO auditImportUrlReqVO);

}
