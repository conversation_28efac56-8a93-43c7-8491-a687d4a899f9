package com.cmpay.code.cmpaymoduleagent.dal.mysql.auditmsg;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.auditmsg.AuditMsgDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AuditMsgMapper extends BaseMapperX<AuditMsgDO> {

    @Select("select * from audit_msg where audit_data_id = #{auditDataId} order by insert_time desc limit 1")
    AuditMsgDO selectByDataId(@Param("auditDataId") String auditDataId);

    @Select("select id,audit_data_id,audit_msg,audit_status,audit_account,insert_time,identity,is_examine from audit_msg where audit_data_id=#{auditDataId}")
    List<AuditMsgDO> selectMsgByDataId(@Param("auditDataId") String auditDataId);
}
