package com.cmpay.code.cmpaymoduleagent.dal.mysql.batchimportdata;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.batchimportdata.BatchImportDataDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 批量导入数据表DB接口
 * Created by 创建人 on 2023-08-04 14:47:56
 */
@Mapper
public interface BatchImportDataMapper extends BaseMapperX<BatchImportDataDO> {

    List<BatchImportDataDO> selectBatchImportDataMsg(@Param("batchId") String batchId,@Param("sonBatchId") String sonBatchId,@Param("status") String status);

    @Update("update batch_import_data set result_msg=#{resultMsg},status=#{status} where id=#{id}")
    void updateBatchImportDataMsgResult(@Param("resultMsg")String resultMsg,@Param("status")String status, @Param("id")Integer id);
}