package com.cmpay.code.cmpaymoduleagent.dal.mysql.changemerchantywyopenid;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.changemerchantywyopenid.ChangeMerchantYwyOpenidDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ChangeMerchantYwyOpenidMapper extends BaseMapperX<ChangeMerchantYwyOpenidDO> {

    @Select("select * from change_merchant_ywy_openid where shop_id = #{shopId} order by id desc limit 1")
    List<ChangeMerchantYwyOpenidDO> selectChanage(@Param("shopId") String shopId);

}