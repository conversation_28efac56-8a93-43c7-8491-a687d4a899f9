package com.cmpay.code.cmpaymoduleagent.dal.mysql.changesorders;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentmerchant.vo.MerchantSubsidyPageReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentmerchant.vo.MerchantSubsidyRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.changesorders.MerchantSubsidyChangeOrdersDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 补贴变更表DB接口
 * Created by 创建人 on 2023-10-13 15:14:58
 */
@Mapper
public interface MerchantSubsidyChangeOrdersMapper extends BaseMapperX<MerchantSubsidyChangeOrdersDO> {

    IPage<MerchantSubsidyRespVO> selectAllPage(Page<MerchantSubsidyRespVO> page, @Param("m") MerchantSubsidyPageReqVO merchantSubsidyPageReqVO);

    List<MerchantSubsidyChangeOrdersDO> selectAllList(@Param("shopId") String shopId, @Param("startTime") String startTime, @Param("endTime") String endTime);
}