package com.cmpay.code.cmpaymoduleagent.dal.mysql.gtmcc;

import com.cmpay.code.cmpaymoduleagent.controller.admin.merchanttypeleshua.vo.LeshuaMccRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.gtmcc.GtMccDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-12-14 9:13:03
 * @version: 1.0
 */
@Mapper
public interface GtMccMapper extends BaseMapperX<GtMccDO> {

    /**
     * 获取商户类型的国通mcc代码
     * @param businessType 商户类型
     * @return 国通mcc代码
     */
    List<LeshuaMccRespVo> getGtMccType(@Param("businessType") String businessType);
}
