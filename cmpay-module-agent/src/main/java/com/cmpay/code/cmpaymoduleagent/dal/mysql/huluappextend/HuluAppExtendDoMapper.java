package com.cmpay.code.cmpaymoduleagent.dal.mysql.huluappextend;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.huluappextend.HuluAppExtendDo;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 葫芦通道申请，签约，提现
 * Created by sangaiyuan on 2024-10-23 17:43:21.
 */
@Mapper
public interface HuluAppExtendDoMapper extends BaseMapperX<HuluAppExtendDo> {
    @Select("select * from hulu_app_extend where shop_id = #{shopId}")
    HuluAppExtendDo selectHuluAppbyShopId(String shopId);

    @Update("update hulu_app_extend set is_apply = #{isApply},sign_contract_status = #{signContractStatus} where shop_id = #{shopId} ")
    void updateHuluImport(@Param("isApply") String isApply,@Param("signContractStatus") String signContractStatus,@Param("shopId") String shopId);
}
