package com.cmpay.code.cmpaymoduleagent.dal.mysql.industry;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.industry.IndustryDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标签DB接口
 * Created by 创建人 on 2024-01-03 16:52:52
 */
@Mapper
public interface IndustryMapper extends BaseMapperX<IndustryDO> {

    List<IndustryDO> selectIndustry(@Param("partnerId") String partnerId, @Param("indName") String indName);


    List<IndustryDO> getIndustryByPartnerId(@Param("partnerId") String partnerId);
}