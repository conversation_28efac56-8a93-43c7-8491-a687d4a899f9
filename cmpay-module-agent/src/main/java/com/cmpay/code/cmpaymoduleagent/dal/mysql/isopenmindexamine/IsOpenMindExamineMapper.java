package com.cmpay.code.cmpaymoduleagent.dal.mysql.isopenmindexamine;

import com.cmpay.code.cmpaymoduleagent.controller.admin.isopenmindexamine.vo.SearchAccountIsOpenAuditReqVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.isopenmindexamine.IsOpenMindExamineDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IsOpenMindExamineMapper extends BaseMapperX<IsOpenMindExamineDO> {


    /**
     * 代理商商户管理——@Schema——查询用户是否开启自动审核
     *
     * @param accountIsOpenAuditReqVo 查询条件
     */
    IsOpenMindExamineDO searchAccountIsOpenAudit(@Param("accountIsOpenAuditReqVo") SearchAccountIsOpenAuditReqVo accountIsOpenAuditReqVo);

    /**
     * 修改用户是否开启自动审核
     *
     * @param openMindExamineDO 修改参数
     */
    void updateExamineAccount(@Param("openMindExamineDO") IsOpenMindExamineDO openMindExamineDO);

    /**
     * 新增用户是否开启自动审核
     *
     * @param openMindExamineDO 修改参数
     */
    void insertExamineAccount(@Param("openMindExamineDO") IsOpenMindExamineDO openMindExamineDO);

    IsOpenMindExamineDO selectByAgentId(@Param("agentId")String agentId);

    List<IsOpenMindExamineDO> selectAll();

    @Select("select pc.agent_id from agent pc join agent_right ag on pc.agent_id = ag.agent_id left join is_open_mind_examine ie on pc.agent_id = ie.account  where ag.is_policy_update = '0'")
    List<String> getAgentIdList();

}

