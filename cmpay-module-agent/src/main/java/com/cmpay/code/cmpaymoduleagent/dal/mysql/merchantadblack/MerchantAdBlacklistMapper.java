package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantadblack;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantadblack.vo.MerchantAdBlacklistReqVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantadblack.MerchantAdBlacklistDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *  商户广告黑名单DB接口
 *  liujia
 */
@Mapper
public interface MerchantAdBlacklistMapper extends BaseMapperX<MerchantAdBlacklistDO> {
    IPage<MerchantAdBlacklistReqVo> selectAllPage(@Param("page") Page<MerchantAdBlacklistReqVo> page,
                                                  @Param("mabrv") MerchantAdBlacklistReqVo mabrv);
                                                 }