package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantbounty;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 奖励金DB接口
 * Created by 创建人 on 2023-07-20 10:12:33
 */
@Mapper
public interface MerchantBountyMapper {

    @Update("update merchant_bounty set openid=#{openid} where type='manager' and shop_id=#{shopId}")
    void updateOpenidByShopId(@Param("openid") String openid,@Param("shopId") String shopId);
}