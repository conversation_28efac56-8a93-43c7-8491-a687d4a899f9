package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantcontrol;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantcontrol.vo.ControlBatchPageReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantcontrol.ControlBatchDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 管控批次表mapper
 * liujia 创建人 on 2023年11月17日 11:15:36
 */
@Mapper
public interface ControlBatchMapper extends BaseMapperX<ControlBatchDO> {

    @Select("select * from control_batch where batch_id = #{batchId}")
    ControlBatchDO selectByBatchId(@Param("batchId") String batchId);

    IPage<ControlBatchDO> selectAllPage(@Param("page") Page<ControlBatchDO> page,@Param("request") ControlBatchPageReqVO controlBatchPageReqVO);
    @Update("update control_batch set status = #{status} where batch_id = #{batchId}")
    void updateStatusByBatchId(@Param("batchId") String batchId,@Param("status") Integer status);
}