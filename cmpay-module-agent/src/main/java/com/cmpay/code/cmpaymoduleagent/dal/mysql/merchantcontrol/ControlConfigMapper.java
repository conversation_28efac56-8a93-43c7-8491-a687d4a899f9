package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantcontrol;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantcontrol.ControlConfigDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 管控配置表mapper
 * liujia 创建人 on 2023年11月17日 11:15:36
 */
@Mapper
public interface ControlConfigMapper extends BaseMapperX<ControlConfigDO> {

    @Select("select * from control_config where branch_id = #{branchId}")
    ControlConfigDO selectByBranchId(@Param("branchId") String branchId);


}