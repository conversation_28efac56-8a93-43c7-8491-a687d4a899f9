package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantcontrol;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantcontrol.vo.ControlDataPageReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantcontrol.vo.ControlDataPageRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantcontrol.ControlDataDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 管控商户状态表mapper
 * liujia 创建人 on 2023年11月17日 11:15:36
 */
@Mapper
public interface ControlDataMapper extends BaseMapperX<ControlDataDO> {

    IPage<ControlDataPageRespVO> selectAllPage(@Param("page") Page<ControlDataPageRespVO> page ,@Param("request") ControlDataPageReqVO controlDataPageReqVO);

    List<ControlDataPageRespVO> selectAllPage(@Param("request") ControlDataPageReqVO controlDataPageReqVO);

    @Update("update control_data set audit_status = #{status} where id = #{id}")
    void updateAuditStatus(@Param("status") String status,@Param("id") Long id);
    @Update("update control_data " +
            "set retain_status = #{status}," +
            "audit_status = #{auditStatus}," +
            "retain_review_user_id = #{retainReviewUserId}," +
            "retain_review_reason = #{retainReviewReason}," +
            "retain_review_time = #{retainReviewTime} " +
            "where id = #{id}")
    void updateRetainStatus(@Param("status") String status, @Param("auditStatus") Integer auditStatus,
                            @Param("id") Long id, @Param("retainReviewUserId") Long retainReviewUserId,
                            @Param("retainReviewReason") String retainReviewReason,@Param("retainReviewTime") Date retainReviewTime);

    @Update("update control_data set audit_status = #{status} where trueid = #{trueid} and batch_id = #{batchId}")
    void updateAuditStatusByBatchIdAndTrueid(@Param("status") String status,@Param("trueid") String trueid,@Param("batchId") String batchId);

    @Select("select * from control_data where trueid = #{trueid} and batch_id = #{batchId}")
    ControlDataDO selectAuditStatusByBatchIdAndTrueid(@Param("trueid") String trueid,@Param("batchId") String batchId);

    @Update("update control_data set revoke_status = #{revokeStatus} , revoke_time = now() where batch_id = #{batchId}")
    void updateByBatchId(@Param("batchId") String batchId ,@Param("revokeStatus") Integer revokeStatus);

    @Select("select * from control_data where batch_id = #{batchId}")
    List<ControlDataDO> selectByBatchId(@Param("batchId") String batchId );
}