package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantcontrol;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantcontrol.ControlImportDataDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 管控商户数据表mapper
 * Created by 创建人 on 2023-11-17 11:24:53
 */
@Mapper
public interface ControlImportDataMapper extends BaseMapperX<ControlImportDataDO> {

    /**
     * 查询子批次号列表
     *
     * @param batchId 主批次号
     * @return List<String>    子批次号集合
     */
    List<String> querySonBatchIdList(String batchId);

    /**
     * 查询全部条数
     *
     * @param batchId 主批次号
     * @return Integer  总条数
     */
    Integer queryAllNumber(String batchId);
    /**
     * 查询执行完成条数
     *
     * @param batchId 主批次号
     * @return Integer  总条数
     */
    Integer queryExecuteNumber(String batchId);

}