package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantcontrol;

import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantcontrol.dto.ControlWhiteListQueryListDto;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantcontrol.ControlWhiteListDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 管控白名单Mapper
 * liujia 创建人 on 2023-11-20 16:55:52
 */
@Mapper
public interface ControlWhiteListMapper extends BaseMapperX<ControlWhiteListDO> {

    @Select("select * from control_white_list where trueid = #{trueid} and status = 1 and is_delete = 0")
    ControlWhiteListDO selectByTrueid(@Param("trueid") String trueid);

    /**
     * 查询白名单商户
     *
     * @param merchantId 商户号
     * @param start      开始时间
     * @param end        结束时间
     * @param pageNo     页码
     * @param pageSize   页长
     * @param partnerId
     * @return list  数组
     */
    List<ControlWhiteListQueryListDto> queryMerchantList(@Param("shopId") String shopId,
                                                         @Param("shopNickname") String shopNickname,
                                                         @Param("start") String start,
                                                         @Param("end") String end,
                                                         @Param("type") String type,
                                                         @Param("pageNo") Integer pageNo,
                                                         @Param("pageSize") Integer pageSize,
                                                         @Param("branchId") String branchId,
                                                         @Param("partnerId") String partnerId,
                                                         @Param("remark") String remark);

    /**
     * 查询白名单商户
     *
     * @param merchantId 商户号
     * @param start      开始时间
     * @param end        结束时间
     * @return list  数组
     */
    List<ControlWhiteListQueryListDto> queryMerchantList(@Param("shopId") String shopId,
                                                         @Param("shopNickname") String shopNickname,
                                                         @Param("start") String start,
                                                         @Param("end") String end,
                                                         @Param("type") String type,
                                                         @Param("branchId") String branchId,
                                                         @Param("partnerId") String partnerId,
                                                         @Param("remark") String remark);

    /**
     * 查询总条数
     *
     * @param merchantId 商户号
     * @param start      开始时间
     * @param end        结束时间
     * @param partnerId
     * @param remark
     * @return count 总条数
     */
    Integer queryMerchantListCount(@Param("merchantId") String merchantId,
                                   @Param("shopNickname") String shopNickname,
                                   @Param("start") String start,
                                   @Param("end") String end,
                                   @Param("branchId") String branchId,
                                   @Param("partnerId") String partnerId,
                                   @Param("remark") String remark);

    @Select("select * from control_white_list where shop_id = #{shopId} and status = 1 and is_delete = 0")
    ControlWhiteListDO selectByShopId(@Param("shopId") String shopId);

    @Update("update control_white_list set status = #{status} where shop_id = #{shopId}")
    void updateByShopId(@Param("shopId") String shopId,@Param("status") String status);
}