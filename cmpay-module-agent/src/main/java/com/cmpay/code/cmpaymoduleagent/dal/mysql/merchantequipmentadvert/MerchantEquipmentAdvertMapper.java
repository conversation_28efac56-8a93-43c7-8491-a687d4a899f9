package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantequipmentadvert;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantequipmentadvert.MerchantEquipmentAdvertDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Title: DeviceBindAdvertMapper
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaymoduleagent.dal.mysql.devicebindadvert
 * @Date 2024/7/9 15:45
 * @description: 设备绑定广告数据库交互层
 */
@Mapper
public interface MerchantEquipmentAdvertMapper extends BaseMapperX<MerchantEquipmentAdvertDO> {
    /**
     * 根据广告id获取分页数组
     *
     * @param adId     广告id
     * @param offset   偏移量
     * @param rowCount 条数
     * @return List<MerchantEquipmentAdvertDO> 商户设备广告表数组
     */
    List<MerchantEquipmentAdvertDO> selectPageByAdId(@Param("adId") Long adId,
                                                     @Param("offset") Integer offset,
                                                     @Param("rowCount") Integer rowCount);

    /**
     * 根据机构类型和机构id查询主键
     *
     * @param bpaaType 机构类型
     * @param bpaaId   机构id
     * @return 主键数组
     */
    List<MerchantEquipmentAdvertDO> selectPageByBpaaIdAndBpaaType(@Param("bpaaType") String bpaaType,
                                                                  @Param("bpaaId") String bpaaId,
                                                                  @Param("offset") Integer offset,
                                                                  @Param("rowCount") Integer rowCount);
}
