package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantindustry;

import com.cmpay.code.cmpaymoduleagent.controller.admin.agentmerchant.vo.MerchantIndustryVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantindustry.MerchantIndustryDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 商户标签DB接口
 * Created by 创建人 on 2024-01-04 16:35:43
 */
@Mapper
public interface MerchantIndustryMapper extends BaseMapperX<MerchantIndustryDO> {

    @Select("SELECT mi.*,i.is_show_audit FROM merchant_industry  mi LEFT JOIN industry i ON mi.ind_id = i.ind_id WHERE mi.shop_id = #{shopId} AND mi.status=1")
    List<MerchantIndustryVO> selectByShopId(@Param("shopId") String shopId);

    /**
     * 根据商户号修改商户标签状态
     * @param shopId 商户号
     */
    @Update("update merchant_industry set status = 0 where shop_id = #{shopId}")
    void updateStatusByShopId(@Param("shopId") String shopId);

    @Delete("delete from merchant_industry where shop_id = #{shopId}")
    void deleteByShopId(String shopId);
}