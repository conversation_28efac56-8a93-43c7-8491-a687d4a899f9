package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantpackagedatas;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentmerchant.vo.InitMerchantPackageDatasRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantpackagedatas.InitMerchantPackageDatasDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商户补贴重置记录DB接口
 * Created by 创建人 on 2023-10-13 16:43:25
 */
@Mapper
public interface InitMerchantPackageDatasMapper extends BaseMapperX<InitMerchantPackageDatasDO> {

    IPage<InitMerchantPackageDatasRespVO> selectAllPage(
            @Param("page")Page<InitMerchantPackageDatasDO> page, @Param("shopId")String shopId, @Param("trueid")String trueid,
            @Param("updateStatus")String updateStatus,@Param("updateType") String updateType,@Param("startTime") String startTime,@Param("endTime")String endTime);

    List<InitMerchantPackageDatasRespVO> selectAllList(@Param("shopId")String shopId, @Param("trueid")String trueid,
                                                       @Param("updateStatus")String updateStatus, @Param("updateType") String updateType, @Param("batchId") String batchId, @Param("startTime") String startTime, @Param("endTime")String endTime);
}