package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantpackageimportdata;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantpackageimportdata.MerchantPackageImportDataDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface MerchantPackageImportDataMapper extends BaseMapperX<MerchantPackageImportDataDO> {

    @Select(" select * from merchant_package_import_data where batch_id=#{batchId} and `status` = #{status} ")
    List<MerchantPackageImportDataDO> selctListByBatchId(@Param("batchId") String batchId,@Param("status") Integer status);

    @Update(" update merchant_package_import_data set `status`= 1,result_msg=#{resultMsg} where id=#{id} ")
    void updateStatusById(@Param("id") Integer id,@Param("resultMsg")String resultMsg);
}
