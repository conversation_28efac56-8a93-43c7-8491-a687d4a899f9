package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantrateactivity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerpolicy.vo.PidListReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantrateactivity.MerchantRateActivityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-12 10:45:20
 * @version: 1.0
 */
@Mapper
public interface MerchantRateActivityMapper {


    /**
     * 根据id查询商户费率活动信息
     * @param rateActivityId 费率活动id
     */
    MerchantRateActivityDO findById(@Param("rateActivityId") String rateActivityId);

    /**
     * 商户管理——开户政策管理——奖励活动下拉列表
     * @param pidList 查询条件
     */
    IPage<MerchantRateActivityDO> searchPidList(@Param("pidList") PidListReqVO pidList, Page<MerchantRateActivityDO> page);
}
