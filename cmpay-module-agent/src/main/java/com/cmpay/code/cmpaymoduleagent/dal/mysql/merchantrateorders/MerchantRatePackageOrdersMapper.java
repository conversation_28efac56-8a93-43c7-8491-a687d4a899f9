package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantrateorders;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantrateorders.vo.MerchantRateOrdersReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantrateorders.vo.MerchantRateOrdersRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantrateorders.MerchantRatePackageOrdersDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 商户费率包修改订单表DB接口
 * Created by 创建人 on 2023-08-02 10:09:48
 */
@Mapper
public interface MerchantRatePackageOrdersMapper extends BaseMapperX<MerchantRatePackageOrdersDO> {

    IPage<MerchantRateOrdersRespVO> selectPackage(@Param("page") Page<MerchantRateOrdersRespVO> page, @Param("mrate") MerchantRateOrdersReqVO merchantRateOrdersReqVO);

    List<MerchantRateOrdersRespVO> selectPackage(@Param("mrate") MerchantRateOrdersReqVO merchantRateOrdersReqVO);

    @Update("update merchant_rate_package_orders set account=#{account},source=#{source},type=#{type} where order_id=#{orderId}")
    void updateMerchantRatePackageAccount(@Param("account") String account,@Param("source") String source,@Param("type") String type,@Param("orderId") String orderId);
}