package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantratepackage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantratepackage.vo.MerchantRatePackageMsgRespVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantratepackage.vo.MerchantRatePackageRelationReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantratepackage.vo.MerchantRatePackageRelationRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantratepackage.MerchantRatePackageRelationDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商户费率包DB接口
 * Created by 创建人 on 2023-07-24 09:27:17
 */
@Mapper
public interface MerchantRatePackageRelationMapper extends BaseMapperX<MerchantRatePackageRelationDO> {

    IPage<MerchantRatePackageRelationRespVO> selectMerchantRatePackagePage(@Param("page") Page<MerchantRatePackageRelationRespVO> page,@Param("mrpr") MerchantRatePackageRelationReqVO merchantRatePackageRelationReqVO);

    MerchantRatePackageRelationDO selectRateById(@Param("shopId") String shopId);

    List<MerchantRatePackageRelationRespVO> selectMerchantRatePackagePage(@Param("mrpr") MerchantRatePackageRelationReqVO merchantRatePackageRelationReqVO);
    @Select("select b.pref_end_amt,b.pref_rate,b.stand_end_amt,b.stand_rate,a.cost_rate,a.status from merchant_rate_package_relation a,merchant_extend b where a.shop_id=b.shop_id and a.shop_id=#{shopId}")
    MerchantRatePackageMsgRespVO selectMerchantRatePackageMsg(@Param("shopId") String shopId);

    @Select("select * from merchant_rate_package_relation where shop_id=#{shopId}")
    MerchantRatePackageRelationRespVO selectMerchantRatePackage(@Param("shopId") String shopId);

    /**
     * 删除商户费率包
     * @param shopId 商户号
     * @return 删除数量
     */
    int deleteMerchantRatePackageByShopId(@Param("shopId") String shopId);

    /**
     * 根据商户号修改补贴包金额
     * @param shopId 商户号
     * @param money 补贴金额
     */
    void updateMerchantRatePackageMoneyByShopId(@Param("shopId") String shopId, @Param("money") Double money);
}