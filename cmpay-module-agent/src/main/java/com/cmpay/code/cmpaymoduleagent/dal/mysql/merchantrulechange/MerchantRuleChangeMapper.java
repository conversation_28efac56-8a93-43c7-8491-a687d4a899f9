package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantrulechange;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantrulechange.MerchantRuleChangeDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MerchantRuleChangeMapper extends BaseMapperX<MerchantRuleChangeDO> {

    @Select("select * from merchant_rule_change where rule_id = #{ruleId}")
    List<MerchantRuleChangeDO> getByRuleId(@Param("ruleId") String ruleId);
}
