package com.cmpay.code.cmpaymoduleagent.dal.mysql.merchantsubsidyrule;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantsubsidyrule.vo.MerchantSubsidyRuleReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merchantsubsidyrule.vo.MerchantSubsidyRuleRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantsubsidyrule.MerchantSubsidyRuleDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 商户补贴规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MerchantSubsidyRuleMapper extends BaseMapperX<MerchantSubsidyRuleDO> {

    IPage<MerchantSubsidyRuleRespVO> getPage(@Param("page") Page<MerchantSubsidyRuleRespVO> page, @Param("req") MerchantSubsidyRuleReqVO merchantSubsidyRuleReqVO);

    IPage<MerchantSubsidyRuleRespVO> getPageMer(@Param("page") Page<MerchantSubsidyRuleRespVO> page, @Param("req") MerchantSubsidyRuleReqVO merchantSubsidyRuleReqVO);

    @Select("select * from account_merchant_subsidy_rule where account_sub_id = #{accountSubId}")
    MerchantSubsidyRuleDO selectByAccountSubId(@Param("accountSubId") String accountSubId);

}