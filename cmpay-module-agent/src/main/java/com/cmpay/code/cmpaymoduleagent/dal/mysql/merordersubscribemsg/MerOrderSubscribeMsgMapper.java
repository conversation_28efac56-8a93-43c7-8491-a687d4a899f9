package com.cmpay.code.cmpaymoduleagent.dal.mysql.merordersubscribemsg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merordersubscribemsg.vo.DeleteOrUpdateUserSubscribeReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merordersubscribemsg.vo.MerchantBroadcastMsgReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.merordersubscribemsg.vo.MerchantBroadcastMsgRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merordersubscribemsg.MerOrderSubscribeMsgDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 语音播报 Mapper
 *
 * <AUTHOR>
@Mapper
public interface MerOrderSubscribeMsgMapper extends BaseMapperX<MerOrderSubscribeMsgDO> {


    /**
     * 代理商商户管理——语音播报管理-查询
     * @param page 分页参数
     * @param broadcastMsgReqVo  查询条件
     * @return 分页对象
     */
    IPage<MerchantBroadcastMsgRespVo> searchMerchantBroadcastMsg(Page<MerchantBroadcastMsgRespVo> page,@Param("broadcastMsgReqVo") MerchantBroadcastMsgReqVo broadcastMsgReqVo);

    /**
     * 根据appid查询appidse
     * @param appid appid
     * @return appidse
     */
    String searchMiniAppiseByAppid(@Param("appid") String appid);

    /**
     * 代理商商户管理——语音播报管理-关闭接受通知
     * @param updateUserSubscribe 修改条件
     */
    void updateUserSubscribe(@Param("updateUserSubscribe") DeleteOrUpdateUserSubscribeReqVo updateUserSubscribe);

    void deleteUserSubscribe(@Param("id") String id);

    /**
     * 根据xmid查询语音播报表
     * @param xmid xmid
     * @return 语音播报信息
     */
    MerOrderSubscribeMsgDO getMerchantBroadcast(@Param("xmid") String xmid);

    /**
     * 根据商户号与商户身份查询订阅消息
     * @param shopId 商户号
     * @param isBoss 商户身份
     */
    List<MerOrderSubscribeMsgDO> findByShopIdAndIsBoss(@Param("shopId") String shopId, @Param("isBoss") String isBoss);

    /**
     * 根据商户号与商户身份修改订阅消息
     * @param xmid 用户xmid
     * @param shopId 商户号
     * @param isBoss 商户身份
     */
    void updateMerSubscribeMsgByShopIdAndIsBoss(@Param("xmid") String xmid, @Param("shopId") String shopId, @Param("isBoss") String isBoss);
}
