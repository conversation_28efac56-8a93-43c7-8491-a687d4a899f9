package com.cmpay.code.cmpaymoduleagent.dal.mysql.ordersnew;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentorders.vo.AgentOrdersDetailVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.agentorders.vo.SearchAgentOrdersReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.ordersnew.vo.MerchantOrdersDetailResqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.ordersnew.vo.MerchantOrdersDetailVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.ordersnew.vo.OrderNewDetailsReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.ordersnew.OrdersNewDO;
import com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.*;
import com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.api.*;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface OrdersNewMapper extends BaseMapperX<OrdersNewDO> {


    /**
     * 交易信息——查询交易信息
     * @param agentOrdersReqVo 查询条件
     */
    IPage<AgentOrdersDetailVo> searchAgentNewOrders(Page<AgentOrdersDetailVo> page, @Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);

    /**
     * 交易信息——查询交易信息
     * @param agentOrdersReqVo 查询条件
     */
    List<AgentOrdersDetailVo> searchAgentNewOrders(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo);

    /**
     * 查询商户订单明细
     * @param agentOrdersReqVo
     * @return
     */
    List<MerchantOrdersDetailVO> searchMerchantOrdersDetail(@Param("agentOrdersReqVo") MerchantOrdersDetailResqVO agentOrdersReqVo);
    /**
     * 统计商户手续费
     *
     * @param agentOrdersReqVo 查询条件
     * @param ordersReqVo
     * @param merchant
     */
    StatisticsOrdersNumberVo statisticsMerchantCommission(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo, @Param("ordersReqVo") SearchOrdersReqVo ordersReqVo, @Param("type") String type);

    /**
     * 统计商户金额信息
     * @param agentOrdersReqVo 查询条件
     */
    StatisticsOrdersNumberVo statisticsMerchantMoneyMSg(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo, @Param("ordersReqVo") SearchOrdersReqVo ordersReqVo, @Param("type") String type);

    /**
     * 统计商户活动金额信息
     * @param agentOrdersReqVo 查询条件
     */
    StatisticsOrdersNumberVo statisticsMerchantActivityMoneyMSg(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo, @Param("ordersReqVo") SearchOrdersReqVo ordersReqVo, @Param("type") String type);

    /**
     * 获取成功订单的退款金额
     *
     * @param shopId 商户号
     * @param orderId 订单号
     */
    BigDecimal getRefundMoney(@Param("shopId") String shopId, @Param("orderId") String orderId, @Param("start") String start, @Param("end") String end);

    /**
     * 交易信息——查询商户交易信息
     * @param page 分页
     * @param ordersReqVo 查询条件
     */
    IPage<OrdersDetailVo> searchMerchantOrders(@Param("page") Page<OrdersDetailVo> page, @Param("ordersReqVo") SearchOrdersReqVo ordersReqVo);

    /**
     * 交易信息——查询商户交易信息
     * @param ordersReqVo 查询条件
     */
    List<OrdersDetailVo> searchMerchantOrders(@Param("ordersReqVo") SearchOrdersReqVo ordersReqVo);

    /**
     * 根据商户单号/订单号查询用户openid
     * @param orderId 商户单号/订单号
     * @return 订单信息
     */
    OrdersNewDO searchOpenId(@Param("orderId") String orderId);

    @Select("select openid from orders_new where order_id = #{orderId}")
    String getOpenIdByOrderId(@Param("orderId")String orderId);

    /**
     * 订单详情
     * @param ordersNewDetails 查询条件
     * @return 订单详情
     */
    OrderNewDetailsRespVO searchOrderNewDetails(@Param("ordersNewDetails") OrderNewDetailsReqVO ordersNewDetails);

    /**
     * 查询退款信息
     *
     * @param orderId       订单号
     * @param shopId        商户号
     * @param refundOrderId
     * @return
     */
    List<RefundOrdersDetailVO> searchRefundOrders(@Param("orderId") String orderId, @Param("shopId") String shopId, @Param("refundOrderId") String refundOrderId);

    /**
     * 统计商户订单金额信息
     *
     * @param agentOrdersReqVo 查询条件
     * @return
     */
    StatisticsOrdersNumberVo statisticsMerchantTotalFeeMSg(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo, @Param("ordersReqVo") SearchOrdersReqVo ordersReqVo, @Param("type") String type);

    /**
     * 收款记录分页(新表)
     *
     * @param pageVO 查询条件
     * @return 分页对象
     */
    List<PaidOutRecordPageRespVO> paidOutRecordPage(@Param("pageVO") PaidOutRecordPageReqVO pageVO);

    /**
     * 查询一天的收款记录信息
     * @param pageVO 查询条件
     * @return 集合对象
     */
    List<OrderDayPageRespVO> orderDayPage(@Param("pageVO") OrderDayPageReqVO pageVO);

    /**
     * 收款订单分页(新表)
     *
     * @param pageVO 查询条件
     * @return 订单集合对象
     */
    List<PaidOutOrdersPageRespVO> paidOutOrdersPage(@Param("pageVO") PaidOutOrdersPageReqVO pageVO);

    /**
     * 收款成功订单详情(新表)
     *
     * @param orderId 订单号
     * @param shopId  商户号
     * @return 订单详情
     */
    PaidOutOrderDetailRespVO paidOutOrderDetail(@Param("orderId") String orderId, @Param("shopId") String shopId, @Param("incomeType") String incomeType);

    /**
     * 收款订单统计(新表)
     * @param pageVO 查询条件
     * @return 统计信息
     */
    StatisticsPaidOutOrdersVO statisticsPaidOutOrders(@Param("pageVO") PaidOutOrdersPageReqVO pageVO);

    /**
     * 统计商户活动金额并分组
     * @return 统计信息
     */
    List<StatisticsMerActivityMoneyVO> statisticsMerActivityMoney(@Param("agentOrdersReqVo") SearchAgentOrdersReqVo agentOrdersReqVo, @Param("ordersReqVo") SearchOrdersReqVo ordersReqVo, @Param("type") String type);
}
