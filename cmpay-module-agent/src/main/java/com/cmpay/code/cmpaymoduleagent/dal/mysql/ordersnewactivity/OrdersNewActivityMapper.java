package com.cmpay.code.cmpaymoduleagent.dal.mysql.ordersnewactivity;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.ordersnewactivity.OrdersNewActivityDO;
import com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.OrdersActivityDetailVO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2024/12/2 15:12
 * @version: 1.0
 */
@Mapper
public interface OrdersNewActivityMapper  extends BaseMapperX<OrdersNewActivityDO> {

    /**
     * 根据订单号查询活动信息
     * @param orderId 订单号
     * @return 活动信息
     */
    List<OrdersActivityDetailVO> findByOrderId(@Param("orderId") String orderId);
}
