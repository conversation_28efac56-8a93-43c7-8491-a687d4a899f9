package com.cmpay.code.cmpaymoduleagent.dal.mysql.organizationdetails;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisementbpaa.vo.AdvertisementBpaaUpdateVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.organizationdetails.vo.CumulativeTransactionVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.organizationdetails.vo.OrganizationOrdersStatisticsReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.organizationdetails.vo.OrganizationOrdersStatisticsVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.organizationdetails.vo.StatisticsTotalVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.organizationdetails.OrganizationDetailsDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-14 16:50:53
 * @version: 1.0
 */
@Mapper
public interface OrganizationDetailsMapper extends BaseMapper<OrganizationDetailsDo> {


    /**
     *  机构情况统计查询当年，当月
     * @param start  开始时间
     * @param end   结束时间
     * @param partnerId 市id
     * @param agentId   区县id
     * @param acceptId  营业点id
     * @return  交易数量，进件数量，交易金额
     */
    StatisticsTotalVo statisticsTotal(@Param("start") String start, @Param("end") String end, @Param("partnerId") String partnerId, @Param("agentId") String agentId, @Param("acceptId") String acceptId);

    /**
     * 统计管理——机构交易查询
     *
     * @param page                         分页参数
     * @param organizationOrdersStatistics 查询条件
     * @return 分页对象
     */
    // List<OrganizationOrdersStatisticsVo> searchOrganizationOrders(Page<OrganizationOrdersStatisticsVo> page, @Param("organizationOrdersStatistics") OrganizationOrdersStatisticsReqVo organizationOrdersStatistics);

    List<OrganizationOrdersStatisticsVo> searchOrganizationOrders(@Param("organizationOrdersStatistics") OrganizationOrdersStatisticsReqVo organizationOrdersStatistics, @Param("type") String type);




    /**
     * 查询当前人的累计换码、进件、交易金额和交易笔数
     *
     * @param partnerId          市id
     * @param agentId            区县id
     * @param acceptId           营业点id
     * @param organizationOrders 查询条件
     * @param number
     * @return 累计交易对象
     */
    CumulativeTransactionVo searchCumulativeTransaction(@Param("organizationOrdersStatistics") OrganizationOrdersStatisticsReqVo organizationOrders, @Param("org") String org, String type);

    /**
     * 查询机构的交易金额（时间做区分）
     *
     * @param organizationOrdersStatistics 查询条件
     * @param type 类型
     */
    StatisticsTotalVo getOrgMoney(@Param("organizationOrdersStatistics") OrganizationOrdersStatisticsReqVo organizationOrdersStatistics, @Param("type") String type);

    /**
     * 查询当前人的累计换码、进件、交易金额和交易笔数
     * @param organizationOrdersStatistics 查询条件
     */
    CumulativeTransactionVo statisticsOrgCumulativeMoney(@Param("organizationOrdersStatistics") OrganizationOrdersStatisticsReqVo organizationOrdersStatistics);

    /**
     * 查询当前人的累计交易量
     * @param advertisementBpaaUpdateVo
     */
    Integer searchWxNum(@Param("advertisementBpaaUpdateVo") AdvertisementBpaaUpdateVo advertisementBpaaUpdateVo);

    Integer searchAlipayNum(@Param("advertisementBpaaUpdateVo") AdvertisementBpaaUpdateVo advertisementBpaaUpdateVo);

    /**
     * 查询机构交易统计查询
     * @return
     */
    List<OrganizationDetailsDo> selectListMsg();

    /**
     * 根据商户号查询机构交易统计
     * @param shopId
     * @return
     */
    OrganizationDetailsDo findById(@Param("id") String shopId);
}
