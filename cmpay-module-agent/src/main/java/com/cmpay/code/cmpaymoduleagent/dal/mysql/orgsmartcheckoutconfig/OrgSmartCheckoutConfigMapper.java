package com.cmpay.code.cmpaymoduleagent.dal.mysql.orgsmartcheckoutconfig;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.orgsmartcheckoutconfig.OrgSmartCheckoutConfigDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机构智慧收银台配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrgSmartCheckoutConfigMapper extends BaseMapperX<OrgSmartCheckoutConfigDO> {

    /**
     * 根据模板ID查询配置列表
     *
     * @param templateId 模板ID
     * @return 配置列表
     */
    List<OrgSmartCheckoutConfigDO> selectByTemplateId(@Param("templateId") String templateId);

    /**
     * 根据智慧收银台ID查询配置列表
     *
     * @param smartCheckoutId 智慧收银台ID
     * @return 配置列表
     */
    List<OrgSmartCheckoutConfigDO> selectBySmartCheckoutId(@Param("smartCheckoutId") Long smartCheckoutId);

    /**
     * 根据模板ID和智慧收银台ID查询配置
     *
     * @param templateId      模板ID
     * @param smartCheckoutId 智慧收银台ID
     * @return 配置信息
     */
    OrgSmartCheckoutConfigDO selectByTemplateIdAndSmartCheckoutId(@Param("templateId") String templateId, 
                                                                  @Param("smartCheckoutId") Long smartCheckoutId);

    /**
     * 根据模板ID批量删除配置
     *
     * @param templateId 模板ID
     * @return 删除的记录数
     */
    int deleteByTemplateId(@Param("templateId") String templateId);

    /**
     * 根据智慧收银台ID批量删除配置
     *
     * @param smartCheckoutId 智慧收银台ID
     * @return 删除的记录数
     */
    int deleteBySmartCheckoutId(@Param("smartCheckoutId") Long smartCheckoutId);

}
