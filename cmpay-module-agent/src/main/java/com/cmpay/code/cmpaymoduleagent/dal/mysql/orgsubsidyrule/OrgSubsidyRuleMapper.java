package com.cmpay.code.cmpaymoduleagent.dal.mysql.orgsubsidyrule;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.orgsubsidyrule.vo.OrgSubsidyRuleReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.orgsubsidyrule.vo.OrgSubsidyRuleRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.orgsubsidyrule.OrgSubsidyRuleDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface OrgSubsidyRuleMapper extends BaseMapperX<OrgSubsidyRuleDO> {

    IPage<OrgSubsidyRuleRespVO> getPage(@Param("page") Page<OrgSubsidyRuleRespVO> page, @Param("req") OrgSubsidyRuleReqVO orgSubsidyRuleReqVO);

    @Select("select * from account_org_subsidy_rule where company_id = #{companyId}")
    OrgSubsidyRuleRespVO getbyCompanyId(@Param("companyId") String companyId);
}
