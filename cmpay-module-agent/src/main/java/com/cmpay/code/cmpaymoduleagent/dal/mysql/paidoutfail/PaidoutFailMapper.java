package com.cmpay.code.cmpaymoduleagent.dal.mysql.paidoutfail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.paidoutfail.vo.PaidoutMsgPageReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.paidoutfail.vo.PaidoutMsgRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.paidoutfail.PaidoutFailDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上游结算数据 Mapper
 *
 * <AUTHOR>
@Mapper
public interface PaidoutFailMapper extends BaseMapperX<PaidoutFailDO> {

    /**
     * 结算信息管理——查询结算信息
     *
     * @param page   分页参数
     * @param pageVO 查询条件
     * @return 分页对象
     */
    IPage<PaidoutMsgRespVO> searchPaidoutMsgPage(Page<PaidoutMsgRespVO> page, @Param("pageVO") PaidoutMsgPageReqVO pageVO);

    /**
     * 结算信息管理——导出结算信息Excel
     *
     * @param pageVO 查询条件
     * @return 分页对象
     */
    List<PaidoutMsgRespVO> searchPaidoutMsgPage(@Param("pageVO") PaidoutMsgPageReqVO pageVO);
}
