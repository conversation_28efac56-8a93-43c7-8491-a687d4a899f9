package com.cmpay.code.cmpaymoduleagent.dal.mysql.paidoutresults;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.paidoutresults.PaidoutResultsDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 上游每日结算信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface  PaidoutResultsMapper extends BaseMapperX<PaidoutResultsDO> {

    @Select("SELECT count(*)  FROM paidout_results WHERE shop_id = #{shopId} AND channel = #{channel} AND payment_status IN (1,2) AND start_time >=current_date() AND paidout_type = 2")
    Integer selectTodaySuccessPaidoutNumber(@Param("shopId") String shopId,@Param("channel") String channel);

}
