package com.cmpay.code.cmpaymoduleagent.dal.mysql.partnerallocate;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerallocate.vo.AcceptPolicyPageReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerallocate.vo.AcceptPolicyRespVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerallocate.vo.SearchPolicyReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerallocate.vo.SearchPolicyRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.partnerallocate.PartnerAllocateDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 政策分配记录 Mapper
 *
 * <AUTHOR>
@Mapper
public interface PartnerAllocateMapper extends BaseMapperX<PartnerAllocateDO> {
    /**
     * 查询该营业点是否存在该政策
     */
    String selectPartnerAllocate(@Param("policyId") String policyId, @Param("agentId") String agentId, @Param("accept") String accept, @Param("partnerId") String partnerId, @Param("type") String type);

    /**
     * 代理商商户管理——查询开户政策
     *
     * @param page        分页参数
     * @param policyReqVo 查询条件
     * @return 分页对象
     */
    IPage<SearchPolicyRespVo> searchPolicy(Page<SearchPolicyRespVo> page, @Param("policyReqVo") SearchPolicyReqVo policyReqVo);

    /**
     * 营业点管理——分配营业点政策——分页查询
     *
     * @param page               分页参数
     * @param acceptPolicyPageVo 查询条件
     * @return 分页对象
     */
    IPage<AcceptPolicyRespVo> searchAcceptPolicyPage(Page<AcceptPolicyRespVo> page,@Param("acceptPolicyPageVo") AcceptPolicyPageReqVo acceptPolicyPageVo);

    /**
     * 代理商商户管理——查询开户政策
     *
     * @param policyReqVo 查询条件
     * @return 分页对象
     */
    List<SearchPolicyRespVo> searchPolicy(@Param("policyReqVo") SearchPolicyReqVo policyReqVo);

    @Select(" select * from partner_allocate where partner_id = #{partnerId}  and policy_id = #{policyId} and status = 1")
    List<PartnerAllocateDO> findByPartnerIdAndPolicyId(@Param("partnerId") String partnerId,@Param("policyId") String policyId);

    void updateStatus(@Param("policyId") String policyId, @Param("agentId") String agentId, @Param("accept") String accept, @Param("partnerId") String partnerId, @Param("type") String type);

    @Select(" select policy_sort from partner_allocate where partner_id = #{partnerId}  and policy_id = #{policyId} and status = 1 and type= 'partner' Limit 1 ")
    Integer selectPolicyPort(@Param("partnerId") String partnerId, @Param("policyId") String policyId);

    @Update(" update partner_allocate set policy_sort=#{policySort} WHERE partner_id = #{partnerId}  and policy_id = #{policyId} and status = 1 ")
    void updateSort(@Param("policyId") String policyId, @Param("partnerId") String partnerId,@Param("policySort") Integer policySort);
}
