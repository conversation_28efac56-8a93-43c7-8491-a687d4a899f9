package com.cmpay.code.cmpaymoduleagent.dal.mysql.partnerpolicy;

import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerpolicy.vo.PartnerPolicyInitOpenAccountRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.partnerpolicy.PartnerPolicyDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合作政策 Mapper
 *
 * <AUTHOR>
@Mapper
public interface PartnerPolicyMapper extends BaseMapperX<PartnerPolicyDO> {


    /**
     * 根据政策id查询政策
     *
     * @param policyId 政策id
     * @return 政策对象
     */
    PartnerPolicyDO findIdByPolicy(@Param("policyId") String policyId);

    /**
     * 根据政策名字搜索市级开户政策
     *
     * @param title 政策名称
     * @param id
     * @return 政策对象
     */
    PartnerPolicyDO findPolicyByName(@Param("title") String title, @Param("id") Integer id);

    /**
     * 逻辑删除政策，修改状态
     *
     * @param policyId 政策id
     */
    void updateStatusById(@Param("policyId") long policyId);

    /**
     * 添加费率包（修改policy_package_rate_id字段）
     *
     * @param policyId            政策id
     * @param policyPackageRateId 费率包id
     */
    void savePolicyPackageRate(@Param("policyId") long policyId, @Param("policyPackageRateId") long policyPackageRateId);

    /**
     * 添加开户渠道（修改policy_pid_id字段）
     *
     * @param policyId    政策id
     * @param policyPidId 开户渠道id
     */
    void savePolicyPid(@Param("policyId") long policyId, @Param("policyPidId") long policyPidId);

    /**
     * 根据政策id修改关联状态（逻辑删除）
     *
     * @param policyId 政策id
     */
    void updateStatusByPolicyId(@Param("policyId") long policyId);

    /**
     * 添加新商户初始化开户政策
     *
     * @param partnerId 市id
     * @param agentId   区县id
     * @param acceptId  网点id
     * @return List<PartnerPolicyInitOpenAccountRespVO> 统一返回类
     */
    List<PartnerPolicyInitOpenAccountRespVO> initOpenAccountPolicy(@Param("partnerId") String partnerId,
                                                                   @Param("agentId") String agentId,
                                                                   @Param("acceptId") String acceptId,
                                                                   @Param("isActivity") String isActivity);

    List<PartnerPolicyDO> selectPolicyList(@Param("title") String title);
}
