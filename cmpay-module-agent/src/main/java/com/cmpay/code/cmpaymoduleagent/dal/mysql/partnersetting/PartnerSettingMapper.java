package com.cmpay.code.cmpaymoduleagent.dal.mysql.partnersetting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnersetting.vo.InitChannelRespVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnersetting.vo.PartnerPayChannelPageReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnersetting.vo.PartnerPayChannelRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.partnersetting.PartnerSettingDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通道支持详情 Mapper
 *
 * <AUTHOR>
@Mapper
public interface PartnerSettingMapper extends BaseMapperX<PartnerSettingDO> {


    /**
     * 查询用户所拥有的通道
     *
     * @param partnerId 查询条件
     * @return 通道详情
     */
    List<InitChannelRespVo> selectMerchantPossessChannel(@Param("partnerId") String partnerId, @Param("branchId") String branchId, @Param("type") String type);

    /**
     * 市级分公司管理——支付通道管理——分页查询
     *
     * @param partnerPayChannel 查询条件
     * @return 分页对象
     */
    IPage<PartnerPayChannelRespVo> searchPartnerPayChannel(Page<PartnerPayChannelRespVo> page,@Param("partnerPayChannel") PartnerPayChannelPageReqVo partnerPayChannel);

    /**
     * 此渠道是否包含次支付通道
     * @param partnerId 市id
     * @param channel 通道
     */
    PartnerSettingDO isHavePayChannel(@Param("partnerId") String partnerId,@Param("channel") String channel);
}
