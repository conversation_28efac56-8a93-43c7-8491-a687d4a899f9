package com.cmpay.code.cmpaymoduleagent.dal.mysql.policypackagerate;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.policypackagerate.PolicyPackageRateDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-17 15:38:14
 * @version: 1.0
 */
@Mapper
public interface PolicyPackageRateMapper extends BaseMapperX<PolicyPackageRateDO> {

    /**
     * 根据名称查询费率包
     * @param name 费率包名称
     */
    @Select("select * from policy_package_rate where name = #{name}")
    List<PolicyPackageRateDO> findByName(@Param("name") String name);
}
