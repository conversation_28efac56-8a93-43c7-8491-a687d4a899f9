package com.cmpay.code.cmpaymoduleagent.dal.mysql.policypid;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.partnerpolicy.vo.PidListReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.policypid.PolicyPidDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-23 14:41:42
 * @version: 1.0
 */
@Mapper
public interface PolicyPidMapper extends BaseMapperX<PolicyPidDO> {


    /**
     * 商户管理——开户政策管理——开户渠道号下拉列表
     *
     * @param page    分页参数
     * @param pidList   查询条件
     * @return 分页对象
     */
    IPage<PolicyPidDO> searchPidList(Page<PolicyPidDO> page, @Param("pidList") PidListReqVO pidList);

    /**
     * 根据渠道名查询开户渠道
     * @param name 渠道名
     * @return 开户渠道实体类
     */
    PolicyPidDO findByName(@Param("name") String name);
}
