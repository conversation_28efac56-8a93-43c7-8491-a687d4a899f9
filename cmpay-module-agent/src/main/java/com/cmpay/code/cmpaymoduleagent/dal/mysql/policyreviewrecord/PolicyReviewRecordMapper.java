package com.cmpay.code.cmpaymoduleagent.dal.mysql.policyreviewrecord;


import com.cmpay.code.cmpaymoduleagent.controller.admin.policyreviewrecord.vo.PolicyReviewRecordRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.policyreviewrecord.PolicyReviewRecordDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 政策审核记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PolicyReviewRecordMapper extends BaseMapperX<PolicyReviewRecordDO> {

    @Select("select * from policy_review_record where policy_update_id = #{policyUpdateId}")
    List<PolicyReviewRecordRespVO> getRecordList(String policyUpdateId);

    PolicyReviewRecordDO getByUpdateId(@Param("policyId") String policyId, @Param("approveOrg") String approveOrg);

}