package com.cmpay.code.cmpaymoduleagent.dal.mysql.policyupdateinfo;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.policyupdateinfo.vo.*;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.policyupdateinfo.PolicyUpdateInfoDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 策略更新信息 Mapper
 *
 * <AUTHOR>
@Mapper
public interface PolicyUpdateInfoMapper extends BaseMapperX<PolicyUpdateInfoDO> {


    /**
     * 代理商商户管理——查询政策审核
     *
     * @param page              分页参数
     * @param policyReviewReqVo 查询条件
     * @return 政策审核分页
     */
    IPage<SearchPolicyReviewRespVo> searchPolicyReview(Page<SearchPolicyReviewRespVo> page, @Param("policyReviewReqVo") SearchPolicyReviewReqVo policyReviewReqVo);

    /**
     * 代理商商户管理——查询政策审核
     *
     * @param policyReviewReqVo 查询条件
     * @return 政策审核分页
     */
    List<SearchPolicyReviewRespVo> searchPolicyReview(@Param("policyReviewReqVo") SearchPolicyReviewReqVo policyReviewReqVo);

    List<SearchPolicyReviewRespVo> selectPolicyList(@Param("policyReviewReqVo") SearchPolicyReviewReqVo policyReviewReqVo);

    List<PolicyRespVO> searchPolicyList(@Param("policyReviewReqVo")SearchPolicyReviewReqVo policyReviewReqVo);

    /**
     * 根据政策更新信息表id查询：市、代理商、受理商名称
     * @param id  政策更新信息表id
     * @return 市、代理商、受理商名称
     */
    ParentCompanyVo getParentCompany(@Param("id") String id);

    PolicyUpdateInfoRespVO selectPolicyUpdateInfoById(@Param("id")String id);

    @Select("select * from policy_update_info where (status = '-2' or status = '0' or status = '-1') and agent_approve_status is null and partner_approve_status is null and insert_time >='2025-01-01 00:00:00'")
    List<PolicyUpdateInfoDO> getList();

    @Select("select * from policy_update_info where status = 1 and agent_approve_status is null and partner_approve_status is null and insert_time >='2025-01-01 00:00:00'")
    List<PolicyUpdateInfoDO> getList1();


}
