package com.cmpay.code.cmpaymoduleagent.dal.mysql.popup;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.popup.vo.PopUpsBatchMsgRepVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.popup.vo.PopUpsBatchMsgRespVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.popup.vo.PopUpsBatchMsgUpdateVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.popup.PopUpsBatchMsgDo;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024-12-06 11:36:00
 * @version: 1.0
 */
@Mapper
public interface PopUpsBatchMsgMapper extends BaseMapperX<PopUpsBatchMsgDo> {

    IPage<PopUpsBatchMsgRespVo> selectPage(@Param("page") Page<PopUpsBatchMsgRespVo> page, @Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    List<PopUpsBatchMsgRespVo> selectPage(@Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    void updateBatchMsg(@Param("updateVo") PopUpsBatchMsgUpdateVo updateVo);

    void updateByshopIdAndXmid(@Param("updateVo") PopUpsBatchMsgUpdateVo updateVo);

    void updateByBatchId(@Param("updateVo") PopUpsBatchMsgUpdateVo updateVo);

    @Select("SELECT EXISTS(SELECT 1 FROM `pop_ups_batch_msg` WHERE `notice_batch_id` = #{isExisit.noticeBatchId}) AS `exists`;")
    Integer isExiSts(@Param("isExisit") PopUpsBatchMsgUpdateVo isExisit);

    @Select("SELECT EXISTS(SELECT 1 FROM `pop_ups_batch_msg` WHERE `notice_batch_id` = #{isExisit.noticeBatchId} AND `pop_ups_type` =  #{isExisit.popUpsType}) AS `exists`;")
    Integer isExiStsByType(@Param("isExisit")PopUpsBatchMsgUpdateVo isExisit);

    @Select("SELECT EXISTS(SELECT 1 FROM `pop_ups_batch_msg` WHERE `notice_batch_id` = #{isExisit.noticeBatchId} AND `shop_id` =  #{shopId}) AS `exists`;")
    Integer isExisStByShopId(@Param("isExisit") PopUpsBatchMsgUpdateVo isExisit,@Param("shopId") String c);

    @Update("update pop_ups_batch_msg set `forever_over` = 2  where `xmid` = #{updateVo.xmid} and `shop_id` = #{updateVo.shopId} and notice_batch_id=#{updateVo.noticeBatchId}")
    void closePopUps(@Param("updateVo") PopUpsBatchMsgUpdateVo updateVo);

    IPage<PopUpsBatchMsgRespVo> selectPage1(@Param("page") Page<PopUpsBatchMsgRespVo> page, @Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    List<PopUpsBatchMsgRespVo> selectPage1(@Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    List<PopUpsBatchMsgRespVo> selectIsBoss(@Param("repVo") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    IPage<PopUpsBatchMsgRespVo> selectPageAgent(@Param("page") Page<PopUpsBatchMsgRespVo> page, @Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);


    List<PopUpsBatchMsgRespVo> selectPageAgent(@Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    void updateByAgentId(@Param("updateVo") PopUpsBatchMsgUpdateVo updateVo);

    @Select("SELECT EXISTS(SELECT 1 FROM `pop_ups_batch_msg` WHERE `notice_batch_id` = #{isExisit.noticeBatchId} AND `phone` =  #{phone}) AS `exists`;")
    Integer isExisStByPhone(@Param("isExisit") PopUpsBatchMsgUpdateVo isExisit,@Param("phone") String phone);

    @Select("select pop_ups_type,display_per,end_time,image_url,start_time,is_perm_close,priority from pop_ups_batch_msg where notice_batch_id = #{noticeBatchId} and shop_id =#{shopId}")
    List<PopUpsBatchMsgRespVo> selectByBatchId(@Param("noticeBatchId") String noticeBatchId,@Param("shopId")String shopId);

    void updateById(@Param("updateVo") PopUpsBatchMsgUpdateVo popUpsBatchMsgUpdateVo);

    IPage<PopUpsBatchMsgRespVo> selectPageIns(@Param("page") Page<PopUpsBatchMsgRespVo> page, @Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);

    List<PopUpsBatchMsgRespVo> selectPageIns(@Param("pu") PopUpsBatchMsgRepVo popUpsBatchMsgRepVo);
}
