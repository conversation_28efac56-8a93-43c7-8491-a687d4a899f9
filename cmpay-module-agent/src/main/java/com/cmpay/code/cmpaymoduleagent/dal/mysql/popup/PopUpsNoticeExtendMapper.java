package com.cmpay.code.cmpaymoduleagent.dal.mysql.popup;

import com.cmpay.code.cmpaymoduleagent.controller.admin.popup.vo.PopUpsNoticeExtendVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.popup.PopUpsNoticeExtendDo;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024-12-06 11:36:00
 * @version: 1.0
 */
@Mapper
public interface PopUpsNoticeExtendMapper extends BaseMapperX<PopUpsNoticeExtendDo> {
    @Select("select * from pop_ups_notice_extend where notice_batch_id = #{noticeBatchId} ;")
    PopUpsNoticeExtendVo searchNotice(@Param("noticeBatchId") String noticeBatchId);

    void updateNaticeExtendMapper(@Param("updateVo") PopUpsNoticeExtendDo popUpsNoticeExtendDo);
}
