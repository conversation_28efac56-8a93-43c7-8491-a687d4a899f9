package com.cmpay.code.cmpaymoduleagent.dal.mysql.profitshare;

import com.cmpay.code.cmpaymoduleagent.controller.admin.profitshare.dto.MerchantProfitShareListDTO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.profitshare.MerchantProfitShareDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author suoh<PERSON><PERSON>
 * date 2023/12/8 11:45
 * version 1.0
 */
@Mapper
public interface MerchantProfitShareMapper extends BaseMapperX<MerchantProfitShareDO> {
    /**
     * 查询分账商户列表
     *
     * @param shopId       商户号
     * @param shopNickname 商户名称
     * @param openStatus   开通状态
     * @param signStatus   签约状态
     * @param pageNo       页码
     * @param pageSize     页长
     * @return CommonResult  统一返回类
     */
    List<MerchantProfitShareListDTO> queryMerchantList(@Param("shopId") String shopId,
                                                       @Param("shopNickname") String shopNickname,
                                                       @Param("openStatus") Integer openStatus,
                                                       @Param("signStatus") Integer signStatus,
                                                       @Param("pageNo") Integer pageNo,
                                                       @Param("pageSize") Integer pageSize,@Param("end")String end,@Param("start") String start);

    /**
     * 查询分账商户列表
     *
     * @param shopId       商户号
     * @param shopNickname 商户名称
     * @param openStatus   开通状态
     * @param signStatus   签约状态
     * @return Integer  总条数
     */
    Integer queryMerchantListCount(@Param("shopId") String shopId,
                                   @Param("shopNickname") String shopNickname,
                                   @Param("openStatus") Integer openStatus,
                                   @Param("signStatus") Integer signStatus,@Param("end")String end,@Param("start")String start);

    String selectReceiveShopIdByShopId(@Param("shopId") String shopId);
}
