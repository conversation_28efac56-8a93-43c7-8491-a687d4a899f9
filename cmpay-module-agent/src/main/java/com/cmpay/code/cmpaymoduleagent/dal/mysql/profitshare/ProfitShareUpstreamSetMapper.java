package com.cmpay.code.cmpaymoduleagent.dal.mysql.profitshare;

import com.cmpay.code.cmpaymoduleagent.controller.admin.profitshare.dto.GetMerchantReceiverListRespDto;
import com.cmpay.code.cmpaymoduleagent.controller.admin.profitshare.vo.QueryMerchantByIdRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.profitshare.ProfitShareUpstreamSetDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author suohong<PERSON>
 * date 2023/12/29 14:41
 * version 1.0
 */
@Mapper
public interface ProfitShareUpstreamSetMapper extends BaseMapperX<ProfitShareUpstreamSetDO> {


    /**
     * 查询管控配置上游信息
     *
     * @param shopId   发起方商户号
     * @param isDelete 是否删除
     * @return List 接收方商户号数组
     */
    List<String> selectListByShopIdAndIsDelete(@Param("shopId") String shopId,
                                               @Param("isDelete") Integer isDelete);

    /**
     * 查询总条数
     *
     * @param merchantId           分账发起方商户号
     * @param receiverMerchantId   分账接收方商户号
     * @param shopNickname         商户发起方简称
     * @param receiverShopNickname 商户接收方简称
     * @param start                开始时间
     * @param end                  结束时间
     * @return Integer 总条数
     */
    Integer getMerchantReceiverCount(@Param("merchantId") String merchantId,
                                     @Param("receiverMerchantId") String receiverMerchantId,
                                     @Param("shopNickname") String shopNickname,
                                     @Param("receiverShopNickname") String receiverShopNickname,
                                     @Param("start") String start,
                                     @Param("end") String end);

    /**
     * 查询分账上游设置列表
     *
     * @param merchantId           分账发起方商户号
     * @param receiverMerchantId   分账接收方商户号
     * @param shopNickname         商户发起方简称
     * @param receiverShopNickname 商户接收方简称
     * @param pageNo               页码
     * @param pageSize             页长
     * @param start                开始时间
     * @param end                  结束时间
     * @return CommonResult        统一返回类
     */
    List<GetMerchantReceiverListRespDto> getMerchantReceiverList(@Param("merchantId") String merchantId,
                                                                 @Param("receiverMerchantId") String receiverMerchantId,
                                                                 @Param("shopNickname") String shopNickname,
                                                                 @Param("receiverShopNickname") String receiverShopNickname,
                                                                 @Param("start") String start,
                                                                 @Param("end") String end,
                                                                 @Param("pageNo") Integer pageNo,
                                                                 @Param("pageSize") Integer pageSize);

    /**
     * 查询分账上游设置商户
     *
     * @param merchantId 商户号
     * @return CommonResult        统一返回类
     */
    QueryMerchantByIdRespVO getMerchantInitiator(@Param("merchantId") String merchantId);
}
