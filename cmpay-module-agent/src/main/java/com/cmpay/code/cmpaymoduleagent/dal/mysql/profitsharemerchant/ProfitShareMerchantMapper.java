package com.cmpay.code.cmpaymoduleagent.dal.mysql.profitsharemerchant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.profitshare.dto.ProfitShareMerchantReceiverDTO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.profitsharemerchant.vo.ProfitShareMerchantPageReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.profitsharemerchant.ProfitShareMerchantDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-25 11:41:20
 * @version: 1.0
 */
@Mapper
public interface ProfitShareMerchantMapper extends BaseMapperX<ProfitShareMerchantDO> {


    /**
     * 分账管理——分账商户设置——分页查询
     *
     * @param profitShareMerchantPage 查询条件
     * @return 分页对象
     */
    IPage<ProfitShareMerchantDO> profitShareMerchantPage(Page<ProfitShareMerchantDO> page, @Param("profitShareMerchantPage") ProfitShareMerchantPageReqVO profitShareMerchantPage);

    /**
     * 分账管理——分账商户设置——分页查询
     *
     * @param profitShareMerchantPage 查询条件
     * @return 分页对象
     */
    List<ProfitShareMerchantDO> profitShareMerchantPage(@Param("profitShareMerchantPage") ProfitShareMerchantPageReqVO profitShareMerchantPage);

    /**
     * 查询分账设置总条数
     *
     * @param merchantId           分账发起方商户号
     * @param receiverMerchantId   分账接收方商户号
     * @param shopNickname         商户发起方简称
     * @param receiverShopNickname 商户接收方简称
     * @param start                开始时间
     * @param end                  结束时间
     * @return CommonResult        统一返回类
     */
    Integer selectMerchantCount(@Param("merchantId") String merchantId,
                                @Param("receiverMerchantId") String receiverMerchantId,
                                @Param("shopNickname") String shopNickname,
                                @Param("receiverShopNickname") String receiverShopNickname,
                                @Param("start") String start,
                                @Param("end") String end);

    /**
     * 查询分账设置列表
     *
     * @param merchantId           分账发起方商户号
     * @param receiverMerchantId   分账接收方商户号
     * @param shopNickname         商户发起方简称
     * @param receiverShopNickname 商户接收方简称
     * @param start                开始时间
     * @param end                  结束时间
     * @param pageNo               页码
     * @param pageSize             页长
     * @return CommonResult        统一返回类
     */
    List<ProfitShareMerchantReceiverDTO> selectMerchantList(@Param("merchantId") String merchantId,
                                                            @Param("receiverMerchantId") String receiverMerchantId,
                                                            @Param("shopNickname") String shopNickname,
                                                            @Param("receiverShopNickname") String receiverShopNickname,
                                                            @Param("start") String start,
                                                            @Param("end") String end,
                                                            @Param("pageNo") Integer pageNo,
                                                            @Param("pageSize") Integer pageSize);

    /**
     * 根据商户号聚合查询总设置比例
     *
     * @param shopId   商户号
     * @param shortKey 终端号
     * @return Double  总和
     */
    Double selectShortKeyRatio(@Param("shopId") String shopId, @Param("shortKey") String shortKey);
}
