package com.cmpay.code.cmpaymoduleagent.dal.mysql.report;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.report.vo.ReportReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.report.vo.ReportRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.report.ReportDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 省分与地市、省分与瑞银信报表信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMapper extends BaseMapperX<ReportDO> {

    /**
     * 查询省分与地市、省分与瑞银信报表信息列表
     * @param page
     * @param reqvo
     * @return
     */
    IPage<ReportRespVO> selectPage(@Param("page") Page<ReportRespVO> page, @Param("reqvo") ReportReqVO reqvo);

    String selectUrl(@Param("reqvo") ReportReqVO reqvo);
    @Select("select * from download_report where report_type = #{reportType} and report_year = #{year} and report_month = #{month}")
    ReportDO selectOne(@Param("reportType") String reportType,@Param("year") String year,@Param("month") String month);

    @Select("select * from download_report where report_type = #{reportType} and report_year = #{year} and report_month = #{month}")
    List<ReportDO> selectListOne(@Param("reportType") String reportType, @Param("year") String reportYear, @Param("month") String reportMonth);
}
