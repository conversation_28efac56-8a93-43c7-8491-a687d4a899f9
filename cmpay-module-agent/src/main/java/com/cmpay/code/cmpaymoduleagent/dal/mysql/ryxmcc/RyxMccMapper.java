package com.cmpay.code.cmpaymoduleagent.dal.mysql.ryxmcc;

import com.cmpay.code.cmpaymoduleagent.controller.admin.ryxmcc.vo.RyxMccParentCategoryReqVo;
import com.cmpay.code.cmpaymoduleagent.controller.admin.ryxmcc.vo.RyxMccParentCategoryRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.ryxmcc.RyxMccDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商户行业类型 Mapper
 *
 * <AUTHOR>
@Mapper
public interface RyxMccMapper extends BaseMapperX<RyxMccDO> {


    /**
     * 根据状态码查询经营类型
     * @param ryxMcc  状态码
     */
    List<String> ryxType(@Param("ryxMcc") String ryxMcc);

    /**
     * 获取瑞银信mcc一级类目
     *
     * @param categoryReqVo 查询条件
     * @return 一级类目
     */
    List<RyxMccParentCategoryRespVo> getRyxMccParentCategory(@Param("categoryReqVo") RyxMccParentCategoryReqVo categoryReqVo);

    /**
     * 获取瑞银信mcc二级类目
     * @param categoryReqVo 查询条件
     * @return 二级类目和mccCode
     */
    List<RyxMccParentCategoryRespVo> getRyxMccSubClassify(@Param("categoryReqVo") RyxMccParentCategoryReqVo categoryReqVo);


    List<String> selectGtMcc(@Param("ryxMcc")String ryxMcc,@Param("businessType") String businessType);

    /**
     * 根据mccCodeId查询mccCode
     * @param mccCodeId mccCodeId
     * @return mcc代码信息
     */
    RyxMccDO findById(@Param("mccCodeId") String mccCodeId);
}
