package com.cmpay.code.cmpaymoduleagent.dal.mysql.shorturlaccepttapprovalrule;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.shorturlacceptapprovalrule.ShorturlAcceptApprovalRuleDO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.CreateApprovalRuleVO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;


@Mapper
public interface ShorturlAcceptApprovalRuleMapper extends BaseMapperX<ShorturlAcceptApprovalRuleDO> {
    void insertAcceptApprovalRule(@Param("vo") CreateApprovalRuleVO createApprovalRuleVO);
}
