package com.cmpay.code.cmpaymoduleagent.dal.mysql.shorturlagentapprovalrule;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.shorturlagentapprovalrule.ShorturlAgentApprovalRuleDO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.approvaltotal.vo.CreateApprovalRuleVO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ShorturlAgentApprovalRuleMapper extends BaseMapperX<ShorturlAgentApprovalRuleDO> {
    void insertAgentApprovalRule(@Param("vo") CreateApprovalRuleVO createApprovalRuleVO);
}
