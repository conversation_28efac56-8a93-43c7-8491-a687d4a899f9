package com.cmpay.code.cmpaymoduleagent.dal.mysql.sparechannel;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.sparechannel.SpareChannelMsgDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import com.cmpay.code.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SpareChannelMsgMapper extends BaseMapperX<SpareChannelMsgDO> {

    default SpareChannelMsgDO selectByShopIdAndChannel(String shopId,String channel){
        return selectOne(new LambdaQueryWrapperX<SpareChannelMsgDO>().eqIfPresent(SpareChannelMsgDO::getShopId,shopId).eqIfPresent(SpareChannelMsgDO::getChannel,channel));
    }

    default SpareChannelMsgDO selectByShopId(String shopId){
        return selectOne(new LambdaQueryWrapperX<SpareChannelMsgDO>().eqIfPresent(SpareChannelMsgDO::getShopId,shopId));
    }





    @Select("select * from spare_channel_msg where channel = #{channel} and shop_id= #{shopId}")
    SpareChannelMsgDO selectByChannelAndShopId(@Param("channel") String channel, @Param("shopId") String shopId);

}
