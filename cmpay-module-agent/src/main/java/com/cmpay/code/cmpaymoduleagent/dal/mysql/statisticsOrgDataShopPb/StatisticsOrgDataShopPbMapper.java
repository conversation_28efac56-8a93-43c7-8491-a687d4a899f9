package com.cmpay.code.cmpaymoduleagent.dal.mysql.statisticsOrgDataShopPb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.statisticswxshopdetialdataorg.vo.WithdrawalDetailsReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.statisticswxshopdetialdataorg.vo.WithdrawalDetailsRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StatisticsOrgDataShopPbMapper {
    IPage<WithdrawalDetailsRespVO> getWithdrawalDetails(@Param("page") Page<WithdrawalDetailsRespVO> page, @Param("wdr") WithdrawalDetailsReqVO withdrawalDetailsReqVO);

    List<WithdrawalDetailsRespVO> getWithdrawalDetailsExport(@Param("wdr") WithdrawalDetailsReqVO withdrawalDetailsReqVO);
}
