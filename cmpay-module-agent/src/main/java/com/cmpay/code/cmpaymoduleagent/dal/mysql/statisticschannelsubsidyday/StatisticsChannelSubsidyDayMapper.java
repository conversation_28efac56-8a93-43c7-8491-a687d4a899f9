package com.cmpay.code.cmpaymoduleagent.dal.mysql.statisticschannelsubsidyday;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.statisticschannelsubsidyday.vo.StatisticsChannelSubsidyDayVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.statisticschannelsubsidyday.vo.SxfSubsidyInfoReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.statisticschannelsubsidyday.StatisticsChannelSubsidyDayDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-11-28 16:37:52
 * @version: 1.0
 */
@Mapper
public interface StatisticsChannelSubsidyDayMapper extends BaseMapperX<StatisticsChannelSubsidyDayDO> {

    /**
     * 数据报表——随行付补贴——分页
     *
     * @param subsidyInfoReqVO 查询条件
     * @return 分页对象
     */
    IPage<StatisticsChannelSubsidyDayVO>searchSxfSubsidyInfo(Page<StatisticsChannelSubsidyDayVO> page, @Param("subsidyInfoReqVO") SxfSubsidyInfoReqVO subsidyInfoReqVO);

    /**
     * 数据报表——随行付补贴——分页
     *
     * @param subsidyInfoReqVO 查询条件
     * @return 分页对象
     */
    List<StatisticsChannelSubsidyDayVO> searchSxfSubsidyInfo(@Param("subsidyInfoReqVO") SxfSubsidyInfoReqVO subsidyInfoReqVO);
}
