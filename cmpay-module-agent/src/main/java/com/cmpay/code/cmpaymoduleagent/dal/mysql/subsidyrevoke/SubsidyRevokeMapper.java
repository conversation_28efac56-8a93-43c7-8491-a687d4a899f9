package com.cmpay.code.cmpaymoduleagent.dal.mysql.subsidyrevoke;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.subsidyrevoke.vo.SubsidyRevokeReqVO;
import com.cmpay.code.cmpaymoduleagent.controller.admin.subsidyrevoke.vo.SubsidyRevokeRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.subsidyrevoke.SubsidyRevokeDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 设置任务记录DB接口
 * Created by 创建人 on 2023-07-26 11:30:44
 */
@Mapper
public interface SubsidyRevokeMapper extends BaseMapperX<SubsidyRevokeDO> {

    IPage<SubsidyRevokeRespVO> selectPage(@Param("page") Page<SubsidyRevokeRespVO> page, @Param("srqv") SubsidyRevokeReqVO subsidyRevokeReqVO);

    @Select("select order_id from subsidy_revoke where shop_id = #{shopId} and status = 0")
    SubsidyRevokeDO selectByShopId(@Param("shopId") String shopId);

    @Update("update subsidy_revoke set status = #{status},remark_msg = #{remarkMsg} where order_id = #{orderId}")
    void updateStatus(@Param("orderId") String orderId,@Param("status") String status,@Param("remarkMsg") String remarkMsg);
}