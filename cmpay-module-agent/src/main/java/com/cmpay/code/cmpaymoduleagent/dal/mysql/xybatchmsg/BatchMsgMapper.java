package com.cmpay.code.cmpaymoduleagent.dal.mysql.xybatchmsg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.xzrecordshow.vo.BatchMsgRespVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.xybatchmsg.BatchMsgDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 批量处理文件记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchMsgMapper extends BaseMapperX<BatchMsgDO> {


    IPage<BatchMsgRespVO> selectBatchMsgPage(@Param("page") Page<BatchMsgRespVO> page, @Param("start")String start, @Param("end") String end);

    default BatchMsgDO selectByBatchId(String batchId) {
            return selectById(batchId);
    }


}