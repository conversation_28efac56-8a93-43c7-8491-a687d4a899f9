package com.cmpay.code.cmpaymoduleagent.dal.mysql.xzrecordshow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.code.cmpaymoduleagent.controller.admin.xzrecordshow.vo.DownloadRecordRespVo;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.xzrecordshow.XzRecordShowDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 下载记录表 Mapper
 *
 * <AUTHOR>
@Mapper
public interface XzRecordShowMapper extends BaseMapperX<XzRecordShowDO> {


    /**
     * 下载管理——下载管理查询
     *
     * @param page
     * @param xzUserType 当前人类型
     * @param xzUserId   当前人id
     * @param start      开始时间
     * @param end        结束时间
     */
    IPage<DownloadRecordRespVo> searchDownloadRecord(Page<DownloadRecordRespVo> page, @Param("xzUserType") String xzUserType, @Param("xzUserId") String xzUserId, @Param("start") String start, @Param("end") String end);

    XzRecordShowDO selectByOrderId(@Param("orderId") String orderId);
}
