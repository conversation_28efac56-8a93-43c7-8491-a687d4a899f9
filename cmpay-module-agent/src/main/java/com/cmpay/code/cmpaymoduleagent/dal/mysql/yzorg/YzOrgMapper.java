package com.cmpay.code.cmpaymoduleagent.dal.mysql.yzorg;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.yzorg.YzOrgDO;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 邮政机构表DB接口
 * Created by 创建人 on 2023-10-25 14:29:04
 */
@Mapper
public interface YzOrgMapper extends BaseMapperX<YzOrgDO> {

    @Select("select accept_id from yz_org where accept_id = #{acceptId}")
    String getIdByAcceptId(@Param("acceptId") String acceptId);

}
