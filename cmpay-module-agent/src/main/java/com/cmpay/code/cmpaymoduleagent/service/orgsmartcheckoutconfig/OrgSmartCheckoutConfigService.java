package com.cmpay.code.cmpaymoduleagent.service.orgsmartcheckoutconfig;

import com.cmpay.code.cmpaymoduleagent.dal.dataobject.orgsmartcheckoutconfig.OrgSmartCheckoutConfigDO;

import java.util.List;

/**
 * 机构智慧收银台配置 Service 接口
 *
 * <AUTHOR>
 */
public interface OrgSmartCheckoutConfigService {

    /**
     * 根据模板ID获取配置列表
     *
     * @param templateId 模板ID
     * @return 配置列表
     */
    List<OrgSmartCheckoutConfigDO> getConfigsByTemplateId(String templateId);

    /**
     * 根据智慧收银台ID获取配置列表
     *
     * @param smartCheckoutId 智慧收银台ID
     * @return 配置列表
     */
    List<OrgSmartCheckoutConfigDO> getConfigsBySmartCheckoutId(Long smartCheckoutId);

    /**
     * 根据模板ID和智慧收银台ID获取配置
     *
     * @param templateId      模板ID
     * @param smartCheckoutId 智慧收银台ID
     * @return 配置信息
     */
    OrgSmartCheckoutConfigDO getConfigByTemplateIdAndSmartCheckoutId(String templateId, Long smartCheckoutId);

    /**
     * 批量保存机构智慧收银台配置
     *
     * @param templateId        模板ID
     * @param smartCheckoutIds  智慧收银台ID列表
     */
    void batchSaveConfigs(String templateId, List<Long> smartCheckoutIds);

    /**
     * 根据模板ID删除所有相关配置
     *
     * @param templateId 模板ID
     * @return 删除的记录数
     */
    int removeConfigsByTemplateId(String templateId);

    /**
     * 根据智慧收银台ID删除所有相关配置
     *
     * @param smartCheckoutId 智慧收银台ID
     * @return 删除的记录数
     */
    int removeConfigsBySmartCheckoutId(Long smartCheckoutId);

    /**
     * 复制模板配置
     *
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     */
    void copyTemplateConfigs(String sourceTemplateId, String targetTemplateId);

    /**
     * 检查模板是否存在配置
     *
     * @param templateId 模板ID
     * @return 是否存在配置
     */
    boolean hasConfigsByTemplateId(String templateId);

    /**
     * 获取模板配置的智慧收银台ID列表
     *
     * @param templateId 模板ID
     * @return 智慧收银台ID列表
     */
    List<Long> getSmartCheckoutIdsByTemplateId(String templateId);

}
