package com.cmpay.code.cmpaymoduleagent.service.orgsmartcheckoutconfig;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.orgsmartcheckoutconfig.OrgSmartCheckoutConfigDO;
import com.cmpay.code.cmpaymoduleagent.dal.mysql.orgsmartcheckoutconfig.OrgSmartCheckoutConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 机构智慧收银台配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrgSmartCheckoutConfigServiceImpl implements OrgSmartCheckoutConfigService {

    @Resource
    private OrgSmartCheckoutConfigMapper orgSmartCheckoutConfigMapper;

    @Override
    public List<OrgSmartCheckoutConfigDO> getConfigsByTemplateId(String templateId) {
        return orgSmartCheckoutConfigMapper.selectByTemplateId(templateId);
    }

    @Override
    public List<OrgSmartCheckoutConfigDO> getConfigsBySmartCheckoutId(Long smartCheckoutId) {
        return orgSmartCheckoutConfigMapper.selectBySmartCheckoutId(smartCheckoutId);
    }

    @Override
    public OrgSmartCheckoutConfigDO getConfigByTemplateIdAndSmartCheckoutId(String templateId, Long smartCheckoutId) {
        LambdaQueryWrapper<OrgSmartCheckoutConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(OrgSmartCheckoutConfigDO::getTemplateId, templateId)
                .eq(OrgSmartCheckoutConfigDO::getSmartCheckoutId, smartCheckoutId);
        return orgSmartCheckoutConfigMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveConfigs(String templateId, List<Long> smartCheckoutIds) {
        if (CollectionUtils.isEmpty(smartCheckoutIds)) {
            return;
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (Long smartCheckoutId : smartCheckoutIds) {
            // 检查是否已存在配置
            OrgSmartCheckoutConfigDO existingConfig = orgSmartCheckoutConfigMapper
                    .selectByTemplateIdAndSmartCheckoutId(templateId, smartCheckoutId);
            
            if (existingConfig == null) {
                OrgSmartCheckoutConfigDO config = OrgSmartCheckoutConfigDO.builder()
                        .templateId(templateId)
                        .smartCheckoutId(smartCheckoutId)
                        .insertTime(now)
                        .updateTime(now)
                        .build();
                orgSmartCheckoutConfigMapper.insert(config);
            }
        }
    }

    @Override
    public int removeConfigsByTemplateId(String templateId) {
        return orgSmartCheckoutConfigMapper.deleteByTemplateId(templateId);
    }

    @Override
    public int removeConfigsBySmartCheckoutId(Long smartCheckoutId) {
        return orgSmartCheckoutConfigMapper.deleteBySmartCheckoutId(smartCheckoutId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyTemplateConfigs(String sourceTemplateId, String targetTemplateId) {
        List<OrgSmartCheckoutConfigDO> sourceConfigs = orgSmartCheckoutConfigMapper.selectByTemplateId(sourceTemplateId);
        
        if (!CollectionUtils.isEmpty(sourceConfigs)) {
            List<Long> smartCheckoutIds = sourceConfigs.stream()
                    .map(OrgSmartCheckoutConfigDO::getSmartCheckoutId)
                    .collect(Collectors.toList());
            
            batchSaveConfigs(targetTemplateId, smartCheckoutIds);
        }
    }

    @Override
    public boolean hasConfigsByTemplateId(String templateId) {
        List<OrgSmartCheckoutConfigDO> configs = orgSmartCheckoutConfigMapper.selectByTemplateId(templateId);
        return !CollectionUtils.isEmpty(configs);
    }

    @Override
    public List<Long> getSmartCheckoutIdsByTemplateId(String templateId) {
        List<OrgSmartCheckoutConfigDO> configs = orgSmartCheckoutConfigMapper.selectByTemplateId(templateId);
        return configs.stream()
                .map(OrgSmartCheckoutConfigDO::getSmartCheckoutId)
                .collect(Collectors.toList());
    }

}
