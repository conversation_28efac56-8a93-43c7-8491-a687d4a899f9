package com.cmpay.code.cmpaymoduleagent.service.smartcheckout;

import com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout.vo.ConfigSaveReqVO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 智慧收银台 Service 接口
 *
 * <AUTHOR>
 */
public interface SmartCheckoutService {


    /**
     * 智慧收银台配置
     */
    List<Map<String,Object>> config(String templateId);


    /**
     * 保存配置
     */
    void save(@Valid ConfigSaveReqVO configSaveReqVO);
}
