package com.cmpay.code.cmpaymoduleagent.service.smartcheckout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmpay.code.cmpaymoduleagent.controller.admin.smartcheckout.vo.ConfigSaveReqVO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.orgsmartcheckoutconfig.OrgSmartCheckoutConfigDO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.smartcheckout.SmartCheckoutDO;
import com.cmpay.code.cmpaymoduleagent.dal.mysql.smartcheckout.SmartCheckoutMapper;
import com.cmpay.code.cmpaymoduleagent.service.orgsmartcheckoutconfig.OrgSmartCheckoutConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 智慧收银台 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmartCheckoutServiceImpl implements SmartCheckoutService {

    @Resource
    private SmartCheckoutMapper smartCheckoutMapper;
    @Resource
    private OrgSmartCheckoutConfigService orgSmartCheckoutConfigService;

    /**
     * 智慧收银台配置
     */
    @Override
    public List<Map<String, Object>> config(String templateId) {
        List<Map<String, Object>> result = new ArrayList<>();
        LambdaQueryWrapper<SmartCheckoutDO> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1
                .eq(SmartCheckoutDO::getParentId, 0)
                .orderByAsc(SmartCheckoutDO::getSort);
        List<SmartCheckoutDO> list = smartCheckoutMapper.selectList(queryWrapper1);
        for (SmartCheckoutDO smartCheckoutDO : list) {
            Map<String, Object> configRespVO1 = new HashMap<>();
            configRespVO1.put("id", smartCheckoutDO.getId());
            configRespVO1.put("name", smartCheckoutDO.getName());
            LambdaQueryWrapper<SmartCheckoutDO> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2
                    .eq(SmartCheckoutDO::getParentId, smartCheckoutDO.getId())
                    .orderByAsc(SmartCheckoutDO::getSort);
            List<SmartCheckoutDO> children = smartCheckoutMapper.selectList(queryWrapper2);
            List<Map<String, Object>> child2 = new ArrayList<>();
            for (SmartCheckoutDO child : children) {
                Map<String, Object> configRespVO2 = new HashMap<>();
                configRespVO2.put("id", child.getId());
                configRespVO2.put("name", child.getName());
                LambdaQueryWrapper<SmartCheckoutDO> queryWrapper3 = new LambdaQueryWrapper<>();
                queryWrapper3
                        .eq(SmartCheckoutDO::getParentId, child.getId())
                        .orderByAsc(SmartCheckoutDO::getSort);
                List<SmartCheckoutDO> children3 = smartCheckoutMapper.selectList(queryWrapper3);
                Map<String, Object> children5 = new HashMap<>();

                List<Map<String, Object>> child3 = new ArrayList<>();
                List<Map<String, Object>> child4 = new ArrayList<>();
                if (StringUtils.isEmpty(templateId)) {
                    for (SmartCheckoutDO checkoutDO : children3) {
                        Integer type = checkoutDO.getType();
                        Map<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("id", checkoutDO.getId());
                        stringObjectHashMap.put("name", checkoutDO.getName());
                        stringObjectHashMap.put("isDefault", checkoutDO.getIsDefault());
                        stringObjectHashMap.put("isSelect", checkoutDO.getIsDefault());
                        if (type == 1) {
                            child3.add(stringObjectHashMap);
                        } else {
                            child4.add(stringObjectHashMap);
                        }
                    }
                } else {
                    for (SmartCheckoutDO checkoutDO : children3) {
                        Integer type = checkoutDO.getType();
                        Map<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("id", checkoutDO.getId());
                        stringObjectHashMap.put("name", checkoutDO.getName());
                        stringObjectHashMap.put("isDefault", checkoutDO.getIsDefault());
                        stringObjectHashMap.put("isSelect", 0);
                        // 查询是否勾选状态
                        OrgSmartCheckoutConfigDO orgSmartCheckoutConfigDO = orgSmartCheckoutConfigService.getConfigByTemplateIdAndSmartCheckoutId(templateId, checkoutDO.getId());
                        if (ObjectUtils.isNotEmpty(orgSmartCheckoutConfigDO)) {
                            stringObjectHashMap.put("isSelect", 1);
                        }
                        if (type == 1) {
                            child3.add(stringObjectHashMap);
                        } else {
                            child4.add(stringObjectHashMap);
                        }
                    }
                }
                children5.put("mode", child3);
                children5.put("function", child4);
                configRespVO2.put("children", children5);
                child2.add(configRespVO2);
            }
            configRespVO1.put("children", child2);
            result.add(configRespVO1);
        }
        return result;
    }

    /**
     * 保存配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ConfigSaveReqVO configSaveReqVO) {
        String rank = configSaveReqVO.getRank();
        String orgId = configSaveReqVO.getOrgId();
        String templateId = configSaveReqVO.getTemplateId();
        List<Long> ids = configSaveReqVO.getIds();
        // 保存该配置




    }
}
