package com.cmpay.code.module.infra.controller.admin.redis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Properties;

@Schema(description = "管理后台 - Redis 监控信息 Response VO")
@Data
@Builder
@AllArgsConstructor
public class RedisMonitorRespVO {

    @Schema(description = "Redis info 指令结果,具体字段，查看 Redis 文档", required = true)
    private Properties info;

    @Schema(description = "Redis key 数量", required = true, example = "1024")
    private Long dbSize;

    @Schema(description = "CommandStat 数组", required = true)
    private List<CommandStat> commandStats;

    @Schema(description = "Redis 命令统计结果")
    @Data
    @Builder
    @AllArgsConstructor
    public static class CommandStat {

        @Schema(description = "Redis 命令", required = true, example = "get")
        private String command;

        @Schema(description = "调用次数", required = true, example = "1024")
        private Long calls;

        @Schema(description = "消耗 CPU 秒数", required = true, example = "666")
        private Long usec;

    }

}
