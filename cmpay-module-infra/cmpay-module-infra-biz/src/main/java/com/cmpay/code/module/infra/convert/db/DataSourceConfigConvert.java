package com.cmpay.code.module.infra.convert.db;

import com.cmpay.code.module.infra.controller.admin.db.vo.DataSourceConfigCreateReqVO;
import com.cmpay.code.module.infra.controller.admin.db.vo.DataSourceConfigRespVO;
import com.cmpay.code.module.infra.controller.admin.db.vo.DataSourceConfigUpdateReqVO;
import com.cmpay.code.module.infra.dal.dataobject.db.DataSourceConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据源配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DataSourceConfigConvert {

    DataSourceConfigConvert INSTANCE = Mappers.getMapper(DataSourceConfigConvert.class);

    DataSourceConfigDO convert(DataSourceConfigCreateReqVO bean);

    DataSourceConfigDO convert(DataSourceConfigUpdateReqVO bean);

    DataSourceConfigRespVO convert(DataSourceConfigDO bean);

    List<DataSourceConfigRespVO> convertList(List<DataSourceConfigDO> list);

}
