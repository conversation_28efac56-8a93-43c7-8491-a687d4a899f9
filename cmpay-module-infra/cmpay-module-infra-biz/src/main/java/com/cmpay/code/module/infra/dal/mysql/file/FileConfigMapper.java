package com.cmpay.code.module.infra.dal.mysql.file;

import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.mybatis.core.mapper.BaseMapperX;
import com.cmpay.code.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.cmpay.code.module.infra.controller.admin.file.vo.config.FileConfigPageReqVO;
import com.cmpay.code.module.infra.dal.dataobject.file.FileConfigDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface FileConfigMapper extends BaseMapperX<FileConfigDO> {

    default PageResult<FileConfigDO> selectPage(FileConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileConfigDO>()
                .likeIfPresent(FileConfigDO::getName, reqVO.getName())
                .eqIfPresent(FileConfigDO::getStorage, reqVO.getStorage())
                .betweenIfPresent(FileConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileConfigDO::getId));
    }

}
