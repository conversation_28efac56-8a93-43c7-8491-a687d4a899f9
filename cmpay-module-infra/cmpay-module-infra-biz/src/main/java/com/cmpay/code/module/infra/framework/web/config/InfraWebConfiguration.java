package com.cmpay.code.module.infra.framework.web.config;

import com.cmpay.code.framework.swagger.config.CmpaySwaggerAutoConfiguration;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * infra 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class InfraWebConfiguration {

    /**
     * infra 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi infraGroupedOpenApi() {
        return CmpaySwaggerAutoConfiguration.buildGroupedOpenApi("infra");
    }

}
