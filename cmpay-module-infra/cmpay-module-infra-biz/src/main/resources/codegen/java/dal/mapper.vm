package ${basePackage}.module.${table.moduleName}.dal.mysql.${table.businessName};

import java.util.*;

import ${PageResultClassName};
import ${QueryWrapperClassName};
import ${BaseMapperClassName};
import ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName}.${table.className}DO;
import org.apache.ibatis.annotations.Mapper;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;

## 字段模板
#macro(listCondition)
#foreach ($column in $columns)
#if (${column.listOperation})
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
#if (${column.listOperationCondition} == "=")##情况一，= 的时候
                .eqIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "!=")##情况二，!= 的时候
                .neIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == ">")##情况三，> 的时候
                .gtIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == ">=")##情况四，>= 的时候
                .geIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "<")##情况五，< 的时候
                .ltIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "<=")##情况五，<= 的时候
                .leIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "LIKE")##情况七，Like 的时候
                .likeIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "BETWEEN")##情况八，Between 的时候
                .betweenIfPresent(${table.className}DO::get${JavaField}, reqVO.get${JavaField}())
#end
#end
#end
#end
/**
 * ${table.classComment} Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ${table.className}Mapper extends BaseMapperX<${table.className}DO> {

    default PageResult<${table.className}DO> selectPage(${sceneEnum.prefixClass}${table.className}PageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<${table.className}DO>()
			#listCondition()
                .orderByDesc(${table.className}DO::getId));## 大多数情况下，id 倒序

    }

    default List<${table.className}DO> selectList(${sceneEnum.prefixClass}${table.className}ExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<${table.className}DO>()
			#listCondition()
                .orderByDesc(${table.className}DO::getId));## 大多数情况下，id 倒序

    }

}
