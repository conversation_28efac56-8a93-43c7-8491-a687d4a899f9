import type {VxeCrudSchema} from '@/hooks/web/useVxeCrudSchemas'
const { t } = useI18n() // 国际化
// 表单校验
export const rules = reactive({
#foreach ($column in $columns)
#if (($column.createOperation || $column.updateOperation) && !$column.nullable && !${column.primaryKey})## 创建或者更新操作 && 要求非空 && 非主键
#set($comment=$column.columnComment)
  $column.javaField: [required],
#end
#end
})
// CrudSchema
const crudSchemas = reactive<VxeCrudSchema>({
  primaryKey: 'id', // 默认的主键ID
  primaryTitle: t('common.index'), // 默认显示的值
  primaryType: 'seq', // 默认为seq，序号模式
  action: true,
  actionWidth: '200', // 3个按钮默认200，如有删减对应增减即可
  columns: [
  #foreach($column in $columns)
    #if ($column.listOperation || $column.listOperationResult || $column.createOperation || $column.updateOperation)
    #set ($dictType = $column.dictType)
    #if(!$column.primaryKey)
    {
      title: '${column.columnComment}',
      field: '${column.javaField}',
      #if (!$column.listOperationResult)
      isTable: false,
      #end
      #if ("" != $dictType)## 有数据字典
      dictType: DICT_TYPE.$dictType.toUpperCase(),
      #if (${column.javaType.toLowerCase()} == "long" || ${column.javaType.toLowerCase()} == "integer")
      dictClass: 'number',
      #else
      dictClass: 'string',
      #end
      #end
      #if (!$column.createOperation && !$column.updateOperation)
      isForm: false,
      #elseif(!("" != $column.dictType))
      #if (${column.javaType.toLowerCase()} == "date" || ${column.javaType.toLowerCase()} == "localdatetime")
      form: {
        component: 'DatePicker',
        componentProps: {
          type: 'datetime',
          valueFormat: 'x'
        }
      },
      #elseif($column.htmlType == "editor")## 文本编辑器
      form: {
        component: 'Editor',
        colProps: {
          span: 24
        },
        componentProps: {
          valueHtml: ''
        }
      },
      #elseif($column.htmlType == "textarea")## 文本框
      form: {
        component: 'Input',
        componentProps: {
          type: 'textarea',
          rows: 4
        },
        colProps: {
          span: 24
        }
      },
      #elseif(${column.javaType.toLowerCase()} == "long" || ${column.javaType.toLowerCase()} == "integer")## 数字类型
      form: {
        component: 'InputNumber',
        value: 0
      },
      #elseif($column.htmlType == "imageUpload")## 图片上传
      form: {
        component: 'UploadImg' // 单图上传，多图为UploadImgs
      },
      #elseif($column.htmlType == "fileUpload")## 图片上传
      form: {
        component: 'UploadFile'
      },
      #end
      #end
      #if ($column.listOperation)
      #if($column.htmlType == "input")
      isSearch: true,
      #elseif("" != $dictType)
      isSearch: true,
      #elseif($column.htmlType == "datetime")
      formatter: 'formatDate',
      search: {
        show: true,
        itemRender: {
          name: 'XDataTimePicker'
        }
      },
      #end
      #end
    },
      #end
    #end
  #end
  ]
})
export const { allSchemas } = useVxeCrudSchemas(crudSchemas)