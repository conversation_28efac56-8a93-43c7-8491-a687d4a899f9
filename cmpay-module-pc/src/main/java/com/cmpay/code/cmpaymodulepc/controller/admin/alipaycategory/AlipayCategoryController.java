package com.cmpay.code.cmpaymodulepc.controller.admin.alipaycategory;

import com.cmpay.code.cmpaymodulepc.controller.admin.alipaycategory.vo.AlipayCategoryRespVO;
import com.cmpay.code.cmpaymodulepc.service.alipaycategory.AlipayCategoryService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Set;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 行业类型")
@RestController
@RequestMapping("/alipaycategory/alipay-category")
@Validated
public class AlipayCategoryController {

    @Resource
    private AlipayCategoryService alipayCategoryService;


    @GetMapping("/get-list")
    @Operation(summary = "获取行业类目")
    @Parameters({ @Parameter(name = "name", description = "一级类目名称，查询二级类目必传",  example = "餐饮"),
            @Parameter(name = "type", description = "类型，查询1级类目传1，查询2级类目传2",  example = "1024")
    })
    public CommonResult<Set<AlipayCategoryRespVO>> createChannel(String name, @RequestParam("type")Integer type) {
        return success(alipayCategoryService.getCategory(name,type));
    }
}
