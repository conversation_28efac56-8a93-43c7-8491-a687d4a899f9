package com.cmpay.code.cmpaymodulepc.controller.admin.alipaycategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 行业类型 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AlipayCategoryBaseVO {

    @Schema(description = "经营类目1")
    private String category1;

    @Schema(description = "经营类目2")
    private String category2;

    @Schema(description = "code")
    private String code;

    @Schema(description = "类型", example = "1")
    private String type;

}
