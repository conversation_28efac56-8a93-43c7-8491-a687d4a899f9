package com.cmpay.code.cmpaymodulepc.controller.admin.alipaycategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 行业类型 Excel 导出 Request VO，参数和 AlipayCategoryPageReqVO 是一致的")
@Data
public class AlipayCategoryExportReqVO {

    @Schema(description = "经营类目1")
    private String category1;

    @Schema(description = "经营类目2")
    private String category2;

    @Schema(description = "code")
    private String code;

    @Schema(description = "类型", example = "1")
    private String type;

}
