package com.cmpay.code.cmpaymodulepc.controller.admin.alipaycategory.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 行业类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlipayCategoryPageReqVO extends PageParam {

    @Schema(description = "经营类目1")
    private String category1;

    @Schema(description = "经营类目2")
    private String category2;

    @Schema(description = "code")
    private String code;

    @Schema(description = "类型", example = "1")
    private String type;

}
