package com.cmpay.code.cmpaymodulepc.controller.admin.alipaycategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 行业类型 Response VO")
@Data
@ToString(callSuper = true)
public class AlipayCategoryRespVO  {
    @Schema(description = "经营类目")
    private String category;

    @Schema(description = "类型", example = "1")
    private String type;

    @Schema(description = "编码")
    private String code;
}
