package com.cmpay.code.cmpaymodulepc.controller.admin.branchoffice;

import com.cmpay.code.cmpaymodulepc.controller.admin.branchoffice.vo.InitBranchReqVO;
import com.cmpay.code.cmpaymodulepc.service.branchoffice.BranchOfficeService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-13 15:01:02
 * @version: 1.0
 */
@Tag(name = "管理后台 - 统计交易信息")
@RestController
@RequestMapping("/branchOffice")
@Validated
public class BranchOfficeController {

    @Resource
    private BranchOfficeService branchOfficeService;

    /**
     * 初始化省级公司
     * @return
     */
    @PostMapping("/initBranchOffice")
    @Operation(summary = "初始化省级公司")
    public CommonResult<List<InitMenuListRespVo>> initBranchOffice(@RequestBody InitBranchReqVO initBranchReqVO) {
        return branchOfficeService.initBranchOffice(initBranchReqVO);
    }
    @GetMapping("/initAochuangAdminUsers")
    @Operation(summary = "初始化内部省级市级用户")
    @PermitAll
    public CommonResult<Boolean> initAochuangAdminUsers(@RequestParam(value = "account",required = false) String account){
        branchOfficeService.initAochuangAdminUsers(account);
        return success(true);
    }
}
