package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo.*;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.brandextend.BrandExtendDO;
import com.cmpay.code.cmpaymodulepc.service.brandextend.BrandExtendService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

/**
 * 千里传音设备HTTP请求控制器
 * Created by 创建人 on 2024-03-01 09:52:47.
 */
@Tag(name = "管理后台 - 千里传音设备")
@Slf4j
@RestController
@RequestMapping(value = "/brandExtend")
public class BrandExtendController {

    @Resource
    private BrandExtendService brandExtendService;

    @GetMapping("/page")
    @Operation(summary = "千里传音设备列表")
    @PreAuthorize("@ss.hasPermission('equipment:warehouse')")
    public CommonResult<PageResult<BrandExtendPageRespVO>> page(BrandExtendPageReqVO branchExtendPageReqVO) {
        return success(brandExtendService.getAllPage(branchExtendPageReqVO));
    }

    @GetMapping("/export")
    @Operation(summary = "千里传音设备导出")
    public void export(BrandExtendPageReqVO branchExtendPageReqVO, HttpServletResponse response) {
        brandExtendService.export(branchExtendPageReqVO, response);
    }

    @PostMapping("/save")
    @Operation(summary = "创建千里传音设备")
    @PreAuthorize("@ss.hasPermission('equipment:warehouse')")
    public CommonResult<Boolean> saveDevice(@RequestBody BrandExtendCreateReqVO brandExtendCreateReqVO) throws Exception {
        brandExtendService.saveDevice(brandExtendCreateReqVO);
        return success(true);
    }

    @PostMapping("/batch-save")
    @Operation(summary = "批量导入千里传音设备")
    @PreAuthorize("@ss.hasPermission('equipment:warehouse')")
    public CommonResult<Boolean> batchSaveDevice(BrandExtendBatchReqVO brandExtendBatchReqVO, HttpServletResponse response) throws IOException {
        brandExtendService.batchSaveDevice(brandExtendBatchReqVO, response);
        return success(true);
    }

    @GetMapping("/batch-generate-device")
    @Operation(summary = "批量生成千里传音设备")
//    @PreAuthorize()
    public CommonResult<String> batchGenerateDevice(String brandMsgId, String brandName, Integer num, String brandSx, String firmwareVersion, HttpServletResponse response) {
        return CommonResult.success(brandExtendService.batchGenerateDevice(brandMsgId, brandName, num, response, brandSx, firmwareVersion));
    }

    @PostMapping("/batch-update-token")
    @Operation(summary = "批量修改千里传音设备token")
    public CommonResult batchUpdateToken(MultipartFile file) {
        CommonResult commonResult = brandExtendService.batchUpdateToken(file);
        return commonResult;
    }

    @PostMapping("/batch-use-device")
    @Operation(summary = "批量更新实际投入使用千里传音设备状态/批量更新开通营销语料设备状态")
    public CommonResult batchUsedDevice(BatchUpdateDeviceReqVO batchUpdateDeviceReqVO) {
        CommonResult commonResult = brandExtendService.batchUsedDevice(batchUpdateDeviceReqVO.getFile(), batchUpdateDeviceReqVO.getUpdateType());
        return commonResult;
    }

    @GetMapping("/get-device-info")
    @Operation(summary = "设备绑定业务异常处理列表")
    public CommonResult<JSONObject> getDeviceInfo(String deviceId) {
        return brandExtendService.getDeviceInfo(deviceId);
    }

    @PostMapping("/unbin-device")
    @Operation(summary = "设备绑定业务异常处理解绑")
    public CommonResult<String> unbindDevice(@RequestBody UnbindDeviceReqVO reqVO) {
        return brandExtendService.unbindDevice(reqVO);
    }


    @PostMapping("/update-device")
    @Operation(summary = "修改设备信息")
    public CommonResult<String> updateDevice(@RequestBody BrandExtendUpdateReqVO brandExtendUpdateReqVO) {
        return success(brandExtendService.updateStaticByDeviceId(brandExtendUpdateReqVO));
    }


    @GetMapping("/get-device")
    @Operation(summary = "获取单设备信息")
    public CommonResult<BrandExtendDO> getByDeviceId(String deviceId) {
        BrandExtendDO byDeviceId = brandExtendService.getByDeviceId(deviceId);
        return success(byDeviceId);
    }

}