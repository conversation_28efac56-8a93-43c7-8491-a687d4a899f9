package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2025/1/17 11:47
 * @version: 1.0
 */
@Accessors(chain = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchDeleteDeviceExcelVO {

    @ExcelProperty("设备SN号")
    private String deviceId;

    @ExcelProperty("品牌Id")
    private String brandMsgId;
}
