package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2025/1/17 14:29
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchDeleteDeviceResultVO {

    @Schema(description = "设备SN号")
    private String deviceId;

    @Schema(description = "品牌Id")
    private String brandMsgId;

    @Schema(description = "结果")
    private String result;
}
