package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class BrandExtendBatchReqVO {

    @Schema(description = "文件")
    private MultipartFile file;

    @Schema(description = "省级id")
    private String branchId;

    @Schema(description = "设备型号id")
    private Long brandMsgId;

    @Schema(description = "设备供应商ID")
    private String firmwareVersion;




}
