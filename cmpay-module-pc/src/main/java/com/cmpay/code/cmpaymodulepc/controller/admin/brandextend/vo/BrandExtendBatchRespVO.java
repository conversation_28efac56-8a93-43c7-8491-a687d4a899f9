package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = false)
@AllArgsConstructor
@NoArgsConstructor
public class BrandExtendBatchRespVO {

    @ExcelProperty("设备SN号")
    private String deviceId;

    @ExcelProperty("设备品牌")
    private String brandMsg;

    @ExcelProperty("设备品牌ID")
    private String brandMsgId;

    @ExcelProperty("设备TOKEN编码（千里传音设备必填）")
    private String deviceToken;
}
