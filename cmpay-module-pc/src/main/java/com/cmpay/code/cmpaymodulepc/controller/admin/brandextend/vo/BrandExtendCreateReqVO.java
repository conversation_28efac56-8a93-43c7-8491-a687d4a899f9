package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BrandExtendCreateReqVO {

    @Schema(description = "设备SN号")
    private String deviceId;

    @Schema(description = "设备品牌ID")
    private Long brandMsgId;

    @Schema(description = "设备TOKEN编码")
    private String deviceToken;

    @Schema(description = "省级id")
    private String branchId;

    @Schema(description = "供应商id")
    private String firmwareVersion;

    @Schema(description = "设备是否投产：1-投产，0-未投产")
    private String ifUse = "1";
}
