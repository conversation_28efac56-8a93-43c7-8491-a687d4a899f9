package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 千里传音设备vo
 */
@Data
public class BrandExtendPageRespVO {
    @Schema(description = "设备SN号")
    private String deviceId;

    @Schema(description = "设备品牌ID")
    private Long brandMsgId;

    @Schema(description = "设备品牌名称")
    private String brandMsgName;

    @Schema(description = "设备类型")
    private String brandMsgType;

    @Schema(description = "设备TOKEN编码")
    private String deviceToken;

    @Schema(description = "是否在线：1在线；0不在线")
    private String isOnline;

    @Schema(description = "信号")
    private String signal;

    @Schema(description = "电量")
    private String electricity;

    @Schema(description = "是否欠费：1是；0否")
    private String isArrear;

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "在线状态信息")
    private String isOnlineStr;

    @Schema(description = "SIM卡iccid")
    private String iccid;

    @Schema(description = "设备供应商ID（支付宝盒或千里传音设备）")
    private String firmwareVersion;

    @Schema(description = "设备在线状态更新时间")
    private String onlineUpdateTime;

    @Schema(description = "设备总内存")
    private String totalSpace;

    @Schema(description = "设备剩余内存")
    private String remaingSpace;

    @Schema(description = "固件版本号")
    private String supplierId;

    @Schema(description = "音量大小")
    private String volume;

    @Schema(description = "播报模板（千里传音）")
    private String hornTemplate;

    @Schema(description = "联网类型:0-2G,1-wifi,2-4G,-1-unknow")
    private String connType;

    @Schema(description = "是否绑定了license及下发了物料；默认0：未绑定；1：已绑定")
    private String isBindLicense;

    @Schema(description = "设备是否投产：1-投产，0-未投产")
    private String ifUse;

    @Schema(description = "是否播报营销语料，平台控制；0：不播报；1：播报")
    private String isPlayAudio;

    @Schema(description = "是否播报取消金额，平台控制；0：不播报；1：播报")
    private String ifBroadcastCancel;

    @Schema(description = "省级id")
    private String branchId;

    @Schema(description = "省名称")
    private String branchName;

    @Schema(description = "添加时间")
    private String insertTime;
}
