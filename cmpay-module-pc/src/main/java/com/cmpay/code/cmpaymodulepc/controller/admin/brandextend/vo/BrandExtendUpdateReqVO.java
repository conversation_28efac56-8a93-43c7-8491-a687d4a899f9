package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BrandExtendUpdateReqVO {
    @Schema(description = "设备SN号")
    private String deviceId;

    @Schema(description = "设备是否投产：0-未投产；1-投产")
    private String ifUse;

    @Schema(description = "是否播报营销语料，平台控制；0：不播报；1：播报")
    private String isPlayAudio;

    @Schema(description = "是否播报取消金额，平台控制；0：不播报；1：播报")
    private String ifBroadcastCancel;
}
