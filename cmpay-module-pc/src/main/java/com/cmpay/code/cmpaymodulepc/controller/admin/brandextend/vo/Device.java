package com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo;

import com.cmpay.code.framework.common.util.device.ToCsv;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2024/4/22 15:25
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class Device implements ToCsv {

    @Schema(description = "设备SN号")
    private String device_id;

    @Schema(description = "设备名称")
    private String nick_name;

    @Schema(description = "设备品牌id")
    private String brand_msg_id;

    @Schema(description = "设备token")
    private String device_token;

    @Override
    public String[] toCsvRow() {
        return new String[] {device_id, nick_name, brand_msg_id,device_token};
    }
}
