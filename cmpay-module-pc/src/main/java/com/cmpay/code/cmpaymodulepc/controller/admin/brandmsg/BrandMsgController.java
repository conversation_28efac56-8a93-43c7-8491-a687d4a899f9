package com.cmpay.code.cmpaymodulepc.controller.admin.brandmsg;


import com.cmpay.code.cmpaymodulepc.controller.admin.brandmsg.vo.BrandMsgSimleRespVO;
import com.cmpay.code.cmpaymodulepc.service.brandmsg.BrandMsgService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 品牌信息")
@RestController
@RequestMapping("/brandmsg")
@Validated
public class BrandMsgController {

    @Resource
    private BrandMsgService brandMsgService;

    @GetMapping("/simle-list")
    @Operation(summary = "设备品牌下拉")
    @Parameter(name = "type", description = "类型", required = true, example = "cash:收银、horn：喇叭、printing：打印")
    public CommonResult<List<BrandMsgSimleRespVO>> getShopTeamPage(@RequestParam("type") String type) {
        return success(brandMsgService.getByBrandType(type));
    }
    @GetMapping("/get-by-brand-id")
    @Operation(summary = "设备品牌下拉")
    @Parameter(name = "brandId", description = "品牌id", required = true)
    public CommonResult<List<BrandMsgSimleRespVO>> selectByBrandId() {
        return success(brandMsgService.getByBrandId());
    }

}
