package com.cmpay.code.cmpaymodulepc.controller.admin.brandmsg.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BrandMsgSimleRespVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "设备品牌id")
    private String brandId;

    @Schema(description = "设备品牌名称")
    private String brandName;

    @Schema(description = "设备品牌名称缩写")
    private String brandSx;
}
