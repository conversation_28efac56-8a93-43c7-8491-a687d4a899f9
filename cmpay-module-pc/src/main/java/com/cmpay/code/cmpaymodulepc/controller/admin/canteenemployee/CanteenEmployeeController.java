package com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee;

import com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee.vo.*;
import com.cmpay.code.cmpaymodulepc.service.canteenemployee.CanteenEmployeeService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 食堂员工管理")
@RestController
@RequestMapping("/canteen/employee")
@Validated
public class CanteenEmployeeController {

    @Resource
    private CanteenEmployeeService canteenEmployeeService;

    @PostMapping("/page")
    @Operation(summary = "食堂收费管理——人员管理分页")
    @PreAuthorize("@ss.hasPermission('canteen:peoples')")
    public CommonResult<PageResult<EmployeePage>> searchEmployeePage(@RequestBody SearchEmployeePageReqVo employeePageReqVo) {
        return canteenEmployeeService.searchEmployeePage(employeePageReqVo);
    }

    @PostMapping("/create")
    @Operation(summary = "食堂收费管理——人员管理——添加人员")
    @PreAuthorize("@ss.hasPermission('canteen:peoples')")
    public CommonResult<String> createEmployee(@RequestBody CreateEmployeeReqVo createReqVO) {
        return success(canteenEmployeeService.createEmployee(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "食堂收费管理——人员管理——更新人员")
    @PreAuthorize("@ss.hasPermission('canteen:peoples')")
    public CommonResult<Boolean> updateEmployee(@RequestBody UpdateEmployeeReqVo updateReqVO) {
        canteenEmployeeService.updateEmployee(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "食堂收费管理——人员管理——删除人员")
    @PreAuthorize("@ss.hasPermission('canteen:peoples')")
    public CommonResult<Boolean> deleteEmployee(@PathVariable("id") Integer id) {
        canteenEmployeeService.deleteEmployee(id);
        return success(true);
    }

    @PostMapping("/resetEmployee")
    @Operation(summary = "食堂收费管理——人员管理——重新认证")
    @PreAuthorize("@ss.hasPermission('canteen:peoples')")
    public CommonResult<String> resetEmployee(@RequestBody ResetEmployeeReqVo resetEmployeeReq) {
        canteenEmployeeService.resetEmployee(resetEmployeeReq);
        return success("");
    }
}
