package com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-30 11:44:22
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateEmployeeReqVo {

    @Schema(description = "员工编号")
    private String employeeId;

    @Schema(description = "员工姓名")
    private String employeeName;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "所属公司")
    private String organization;

    @Schema(description = "商户号")
    private String shopId;
}
