package com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-03 14:24:31
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetEmployeeReqVo {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "id")
    private Integer id;
}
