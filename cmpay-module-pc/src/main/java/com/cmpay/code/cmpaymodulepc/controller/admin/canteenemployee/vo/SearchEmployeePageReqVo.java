package com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-30 10:49:59
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchEmployeePageReqVo extends PageParam {

    @Schema(description = "员工编号")
    private String employeeId;

    @Schema(description = "员工姓名")
    private String employeeName;

    @Schema(description = "商户号")
    private String shopId;
}
