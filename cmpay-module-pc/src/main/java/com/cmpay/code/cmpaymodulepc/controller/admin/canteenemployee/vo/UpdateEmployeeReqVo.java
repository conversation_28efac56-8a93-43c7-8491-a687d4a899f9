package com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-30 14:08:56
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateEmployeeReqVo {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "员工姓名")
    private String employeeName;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "所属公司")
    private String organization;

}
