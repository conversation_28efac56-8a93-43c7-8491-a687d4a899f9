package com.cmpay.code.cmpaymodulepc.controller.admin.canteenmealprogrem;

import com.cmpay.code.cmpaymodulepc.controller.admin.canteenmealprogrem.vo.CreateMealProgremReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenmealprogrem.vo.MealProgremPageReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenmealprogrem.vo.UpdateMealProgremReqVo;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenmealprogrem.CanteenMealProgremDO;
import com.cmpay.code.cmpaymodulepc.service.canteenmealprogrem.CanteenMealProgremService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 食堂套餐")
@RestController
@RequestMapping("/canteen/meal-progrem")
@Validated
public class CanteenMealProgremController {

    @Resource
    private CanteenMealProgremService mealProgremService;

    @PostMapping("/page")
    @Operation(summary = "食堂收费管理——套餐管理分页")
    @PreAuthorize("@ss.hasPermission('canteen:package')")
    public CommonResult<PageResult<CanteenMealProgremDO>> searchEmployeePage(@RequestBody MealProgremPageReqVo mealProgremPageReqVo) {
        return mealProgremService.searchMealProgremPage(mealProgremPageReqVo);
    }

    @PostMapping("/create")
    @Operation(summary = "收费管理——套餐管理——添加套餐")
    @PreAuthorize("@ss.hasPermission('canteen:package')")
    public CommonResult<String> createMealProgrem(@RequestBody CreateMealProgremReqVo createReqVO) {
        return success(mealProgremService.createMealProgrem(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "食堂收费管理——套餐管理——更新套餐")
    @PreAuthorize("@ss.hasPermission('canteen:package')")
    public CommonResult<Boolean> updateMealProgrem(@RequestBody UpdateMealProgremReqVo updateReqVO) {
        mealProgremService.updateMealProgrem(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "食堂收费管理——套餐管理——删除套餐")
    @PreAuthorize("@ss.hasPermission('canteen:package')")
    public CommonResult<Boolean> deleteMealProgrem(@PathVariable("id") Integer id) {
        mealProgremService.deleteMealProgrem(id);
        return success(true);
    }
}
