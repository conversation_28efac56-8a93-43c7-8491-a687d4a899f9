package com.cmpay.code.cmpaymodulepc.controller.admin.canteenmealprogrem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-05 14:24:42
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateMealProgremReqVo {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "原始价格")
    private BigDecimal originalPrice;

    @Schema(description = "折扣后价格")
    private BigDecimal discountPrice;
}
