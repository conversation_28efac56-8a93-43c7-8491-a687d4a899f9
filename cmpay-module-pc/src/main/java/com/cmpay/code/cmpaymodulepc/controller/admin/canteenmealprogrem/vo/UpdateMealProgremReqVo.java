package com.cmpay.code.cmpaymodulepc.controller.admin.canteenmealprogrem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-05 14:41:17
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateMealProgremReqVo {

    @Schema(description = "id")
    private String id;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "原始价格")
    private BigDecimal originalPrice;

    @Schema(description = "折扣后价格")
    private BigDecimal discountPrice;

    @Schema(description = "商户号")
    private String shopId;
}
