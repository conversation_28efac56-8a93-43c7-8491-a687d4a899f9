package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders;

import com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.canteenorders.CanteenOrdersConvert;
import com.cmpay.code.cmpaymodulepc.service.canteenorders.CanteenOrdersService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@Tag(name = "管理后台 - 食堂，学校场景订单管理")
@RestController
@RequestMapping("/canteen/orders")
@Validated
public class CanteenOrdersController {

    @Resource
    private CanteenOrdersService canteenOrdersService;

    @PostMapping("/school-page")
    @Operation(summary = "学校送奶管理——订单信息分页")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:orders')")
    public CommonResult<PageResult<MilkOrdersPage>> searchMilkOrdersPage(@RequestBody SearchMilkOrdersPagePageReqVo milkOrdersPage) {
        return canteenOrdersService.searchMilkOrdersPage(milkOrdersPage);
    }

    @PostMapping("/exportSchoolExcel")
    @Operation(summary = "学校送奶管理——导出订单信息Excel")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:orders')")
    public void exportSchoolExcel(@RequestBody SearchMilkOrdersPagePageReqVo milkOrdersPage,
                            HttpServletResponse response) throws IOException {
        List<MilkOrdersPage> list = canteenOrdersService.getList(milkOrdersPage);
        // 导出 Excel
        List<MilkOrdersExcelVO> datas = CanteenOrdersConvert.INSTANCE.convertSchoolList(list);
        ExcelUtils.write(response, "milkorders.xls", "数据", MilkOrdersExcelVO.class, datas);
    }

    @PostMapping("/canteen-page")
    @Operation(summary = "食堂收费管理——订单信息分页")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:orders')")
    public CommonResult<PageResult<ShiTangPage>> searchShiTangPage(@RequestBody SearchShiTangPageReqVo shiTangPage) {
        return canteenOrdersService.searchShiTangPage(shiTangPage);
    }

    @PostMapping("/exportShiTangExcel")
    @Operation(summary = "食堂收费管理——导出订单信息Excel")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:orders')")
    public void exportShiTangExcel(@RequestBody SearchShiTangPageReqVo shiTangPage,
                                   HttpServletResponse response) throws IOException {
        List<ShiTangPage> list = canteenOrdersService.exportShiTangExcel(shiTangPage);
        // 导出 Excel
        List<ShiTangExcelVO> datas = CanteenOrdersConvert.INSTANCE.convertShiTangList(list);
        ExcelUtils.write(response, "shitangorders.xlsx", "数据", ShiTangExcelVO.class, datas);
    }
}
