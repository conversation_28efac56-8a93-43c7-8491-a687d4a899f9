package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-29 14:51:56
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MilkOrdersExcelVO {

    @ExcelProperty("订单号")
    private String orderId;

    @ExcelProperty("收费时间")
    private String payTime;

    @ExcelProperty("收费项目")
    private String milkProject;

    @ExcelProperty("学校")
    private String school;

    @ExcelProperty("年级")
    private String stGrade;

    @ExcelProperty("班级")
    private String stClass;

    @ExcelProperty("姓名")
    private String stName;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("缴费金额")
    private BigDecimal money;

    @ExcelProperty("状态")
    private String statusStr;
}
