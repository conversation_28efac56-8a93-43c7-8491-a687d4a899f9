package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-29 11:21:47
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MilkOrdersPage {

    @Schema(description = "缴费单号")
    private String orderId;

    @Schema(description = "收费时间")
    private String payTime;

    @Schema(description = "收费项目")
    private String milkProject;

    @Schema(description = "学校")
    private String school;

    @Schema(description = "所在班级")
    private String stGrade;

    @Schema(description = "所在班级")
    private String stClass;

    @Schema(description = "学生姓名")
    private String stName;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "缴费金额")
    private BigDecimal money;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "状态对应字符串")
    private String statusStr;
}
