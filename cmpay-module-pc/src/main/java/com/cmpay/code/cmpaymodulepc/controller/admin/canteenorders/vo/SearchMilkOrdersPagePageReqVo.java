package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-29 11:00:35
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchMilkOrdersPagePageReqVo extends PageParam {

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "收费项目")
    private String milkProject;

    @Schema(description = "学校")
    private String school;

    @Schema(description = "所在班级")
    private String stClass;

    @Schema(description = "学生姓名")
    private String stName;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "缴费状态，0 ：支付失败；1：支付成功")
    private String milkStatus;
}
