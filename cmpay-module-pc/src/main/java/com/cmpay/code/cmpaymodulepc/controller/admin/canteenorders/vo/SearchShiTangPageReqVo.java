package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-30 16:07:59
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchShiTangPageReqVo extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "订单号")
    private String stOrderId;

    @Schema(description = "消费者")
    private String stConsumer;

    @Schema(description = "订单类型")
    private String mealName;
}
