package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-03 11:29:31
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShiTangExcelVO {

    @ExcelProperty("订单号")
    private String stOrderId;

    @ExcelProperty("人员编号")
    private String employeeId;

    @ExcelProperty("消费者")
    private String name;

    @ExcelProperty("部门")
    private String organization;

    @ExcelProperty("类型")
    private String mealName;

    @ExcelProperty("消费金额")
    private BigDecimal money;

    @ExcelProperty("消费时间")
    private String payTime;

    @ExcelProperty("状态")
    private String statusStr;




}
