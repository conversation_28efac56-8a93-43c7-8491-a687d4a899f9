package com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-30 16:16:47
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShiTangPage {

    @Schema(description = "订单号")
    private String stOrderId;

    @Schema(description = "消费者")
    private String name;

    @Schema(description = "类型")
    private String mealName;

    @Schema(description = "消费金额")
    private BigDecimal money;

    @Schema(description = "消费时间")
    private String payTime;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "状态字符串")
    private String statusStr;

    @Schema(description = "员工编号")
    private String employeeId;

    @Schema(description = "所属公司")
    private String organization;
}
