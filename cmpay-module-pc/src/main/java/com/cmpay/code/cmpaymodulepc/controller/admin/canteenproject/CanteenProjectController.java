package com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject;

import com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject.vo.CreateProjectReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject.vo.ProjectPage;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject.vo.UpdateProjectReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject.vo.searchProjectPageReqVo;
import com.cmpay.code.cmpaymodulepc.service.canteenproject.CanteenProjectService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 送奶项目管理")
@RestController
@RequestMapping("/canteen/project")
@Validated
public class CanteenProjectController {

    @Resource
    private CanteenProjectService projectService;

    @PostMapping("/page")
    @Operation(summary = "送奶项目分页")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:delivery-type')")
    public CommonResult<PageResult<ProjectPage>> searchProjectPage(@RequestBody searchProjectPageReqVo projectPageReqVo) {
        return projectService.searchProjectPage(projectPageReqVo);
    }

    @PostMapping("/create")
    @Operation(summary = "创建送奶项目")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:delivery-type')")
    public CommonResult<String> createProject(@RequestBody CreateProjectReqVo createReqVO) {
        return success(projectService.createProject(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新送奶项目")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:delivery-type')")
    public CommonResult<Boolean> updateProject(@RequestBody UpdateProjectReqVo updateReqVO) {
        projectService.updateProject(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{projectId}")
    @Operation(summary = "删除送奶项目")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:delivery-type')")
    public CommonResult<Boolean> deleteProject(@PathVariable("projectId") Integer projectId) {
        projectService.deleteProject(projectId);
        return success(true);
    }
}
