package com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectPage {

    @Schema(description = "送奶项目id")
    private Integer projectId;

    @Schema(description = "送奶项目名称")
    private String projectName;

    @Schema(description = "价格")
    private BigDecimal price;
}
