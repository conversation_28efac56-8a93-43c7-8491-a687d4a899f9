package com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool;

import com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo.CreateSchoolReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo.SchoolPage;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo.UpdateSchoolReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo.searchSchoolPageReqVo;
import com.cmpay.code.cmpaymodulepc.service.canteenschool.CanteenSchoolService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 送奶学校管理")
@RestController
@RequestMapping("/canteen/school")
@Validated
public class CanteenSchoolController {

    @Resource
    private CanteenSchoolService schoolService;

    @PostMapping("/page")
    @Operation(summary = "送奶学校分页")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:schools')")
    public CommonResult<PageResult<SchoolPage>> searchSchoolPage(@RequestBody searchSchoolPageReqVo schoolPageReqVo) {
        return schoolService.searchSchoolPage(schoolPageReqVo);
    }

    @PostMapping("/create")
    @Operation(summary = "创建送奶学校")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:schools')")
    public CommonResult<String> createSchool(@RequestBody CreateSchoolReqVo createReqVO) {
        return success(schoolService.createSchool(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新送奶学校")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:schools')")
    public CommonResult<Boolean> updateSchool(@RequestBody UpdateSchoolReqVo updateReqVO) {
        schoolService.updateSchool(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{schoolId}")
    @Operation(summary = "删除送奶学校")
    @PreAuthorize("@ss.hasPermission('school-milk-delivery:schools')")
    public CommonResult<Boolean> deleteSchool(@PathVariable(value = "schoolId", required = true) Integer schoolId) {
        schoolService.deleteSchool(schoolId);
        return success(true);
    }
}
