package com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-27 11:09:06
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateSchoolReqVo {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "学校名称")
    private String name;

    @Schema(description = "学校地址")
    private String address;

    @Schema(description = "当前时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date insertTime;
}
