package com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-26 16:46:37
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SchoolPage {

    @Schema(description = "学校id")
    private Integer schoolId;

    @Schema(description = "学校名称")
    private String name;

    @Schema(description = "学校地址")
    private String address;
}
