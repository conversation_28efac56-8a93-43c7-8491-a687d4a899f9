package com.cmpay.code.cmpaymodulepc.controller.admin.channelmsg;

import com.cmpay.code.cmpaymodulepc.service.channelmsg.ChannelMsgService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 通道信息表HTTP请求控制器
 * Created by 创建人 on 2023-11-04 09:08:34.
 */
@Tag(name = "管理后台 - 通道信息表")
@RestController
@RequestMapping(value = "/channel/msg/")
@Validated
public class ChannelMsgController {

    @Resource
    private ChannelMsgService channelMsgService;
}

