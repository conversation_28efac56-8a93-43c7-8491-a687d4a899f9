package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders;

import com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo.SearchSubsidyDetailReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo.SearchSubsidyDetailRespVo;
import com.cmpay.code.cmpaymodulepc.service.channelsubsidymerchantorders.ChannelSubsidyMerchantOrdersService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@Tag(name = "管理后台 - 渠道补贴商户订单明细")
@RestController
@RequestMapping("/channel/subsidy-merchant-orders")
@Validated
public class ChannelSubsidyMerchantOrdersController {

    @Resource
    private ChannelSubsidyMerchantOrdersService subsidyMerchantOrdersService;

    @PostMapping("/merchant/searchSubsidyDetail")
    @Operation(summary = "商户邮政权益平台——补贴明细")
    // @PreAuthorize("@ss.hasPermission('settle-accounts:subsidy-detail:query')")
    public CommonResult<SearchSubsidyDetailRespVo> merchantSearchSubsidyDetail(@RequestBody SearchSubsidyDetailReqVo subsidyDetailReqVo) {
        return subsidyMerchantOrdersService.merchantSearchSubsidyDetail(subsidyDetailReqVo);
    }

    @PostMapping("/merchant/searchSubsidyDetailExcel")
    @Operation(summary = "商户导出政权益平台——补贴明细Excel")
    // @PreAuthorize("@ss.hasPermission('settle-accounts:subsidy-detail:export')")
    public void exportSubsidyDetailExcel(HttpServletResponse response, @RequestBody SearchSubsidyDetailReqVo subsidyDetailReqVo) {
        subsidyMerchantOrdersService.exportSubsidyDetailExcel(subsidyDetailReqVo, response);
    }
}
