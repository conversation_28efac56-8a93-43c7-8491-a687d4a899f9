package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-17 11:18:22
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchSubsidyDetailReqVo extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "商户订单号")
    private String shopOrderId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "市id")
    private String partnerId;

    @Schema(description = "省id")
    private String branchId;

    @Schema(description = "数据来源")
    private String subsidySource;

    @Schema(description = "通道")
    private String channel;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "平台")
    private Integer platform;
}
