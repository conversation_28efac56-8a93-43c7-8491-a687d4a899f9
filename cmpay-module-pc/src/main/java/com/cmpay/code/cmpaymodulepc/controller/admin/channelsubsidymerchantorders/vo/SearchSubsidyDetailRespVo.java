package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo;

import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-17 11:06:15
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchSubsidyDetailRespVo {

    @Schema(description = "分页对象")
    private PageResult<SubsidyDetailVo> pageResult;

    @Schema(description = "统计补贴明细")
    private StatisticsSubsidyDetailVo statistics;
}
