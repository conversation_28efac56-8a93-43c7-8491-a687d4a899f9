package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 统计补贴明细
 * @date 2023-06-17 11:09:55
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsSubsidyDetailVo {

    @Schema(description = "补贴笔数")
    private Integer subsidyNum;

    @Schema(description = "补贴金额")
    private Double subsidyMoney;
}
