package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 补贴明细对象
 * @date 2023-06-17 11:08:53
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubsidyDetailVo {

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "商户订单号")
    private String shopOrderId;

    @Schema(description = "补贴时间")
    private String insertTime;

    @Schema(description = "补贴金额")
    private Double subsidyMoney;

    @Schema(description = "补贴详情")
    private String content;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "状态字符串")
    private String statusStr;

    @Schema(description = "所属商户")
    private String shopName;

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "所属营业点")
    private String acceptName;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "所属区县")
    private String agentName;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "所属市")
    private String partnerName;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "通道key")
    private String channel;

    @Schema(description = "通道value")
    private String channelStr;
}
