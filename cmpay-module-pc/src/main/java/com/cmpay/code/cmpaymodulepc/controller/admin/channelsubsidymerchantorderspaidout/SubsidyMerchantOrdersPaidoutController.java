package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout;

import com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo.subsidyMerchantPaidoutDetailReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo.subsidyMerchantPaidoutDetailRespVo;
import com.cmpay.code.cmpaymodulepc.service.channelsubsidymerchantorderspaidout.SubsidyMerchantOrdersPaidoutService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@Tag(name = "管理后台 - 渠道补贴商家订单结算")
@RestController
@RequestMapping("/channel/subsidy-merchant-orders-paidout")
@Validated
public class SubsidyMerchantOrdersPaidoutController {

    @Resource
    private SubsidyMerchantOrdersPaidoutService subsidyMerchantOrdersPaidoutService;

    @PostMapping("/merchant/subsidyMerchantPaidoutDetail")
    @Operation(summary = "商户平台——结算明细")
    // @PreAuthorize("@ss.hasPermission('settle-accounts:settle-accounts-detail:query')")
    public CommonResult<subsidyMerchantPaidoutDetailRespVo> subsidyMerchantPaidoutDetail(@RequestBody subsidyMerchantPaidoutDetailReqVo paidoutDetailReqVo) {
        return subsidyMerchantOrdersPaidoutService.subsidyMerchantPaidoutDetail(paidoutDetailReqVo);
    }

    @PostMapping("/merchant/exportPaidoutDetailExcel")
    @Operation(summary = "商户导出邮政权益平台——结算明细Excel")
    // @PreAuthorize("@ss.hasPermission('settle-accounts:settle-accounts-detail:export')")
    public void exportPaidoutDetailExcel(HttpServletResponse response, @RequestBody subsidyMerchantPaidoutDetailReqVo paidoutDetailReqVo) {
        subsidyMerchantOrdersPaidoutService.exportPaidoutDetailExcel(paidoutDetailReqVo, response);
    }
}
