package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 邮政权益平台——结算明细分页
 * @date 2023-06-19 11:00:22
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaidoutDetailVo {

    @Schema(description = "结算时间")
    private String  paidoutDay;

    @Schema(description = "补贴时间")
    private String subsidyDay;

    @Schema(description = "金额")
    private Double money;

    @Schema(description = "结算状态码：1-已结算 0-未结算 2-结算失败")
    private String paidoutStatus;

    @Schema(description = "结算状态字符串")
    private String paidoutStatusStr;

    @Schema(description = "所属商户")
    private String shopName;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "所属营业点")
    private String acceptName;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "通道")
    private String channel;

    @Schema(description = "通道字符串")
    private String channelStr;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "区县营业点")
    private String agentName;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "所属市级")
    private String partnerName;
}
