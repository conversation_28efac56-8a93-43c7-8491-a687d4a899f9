package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 邮政权益平台——统计结算明细
 * @date 2023-06-19 10:59:39
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsPaidoutDetailVo {

    @Schema(description = "结算金额")
    private Double paidoutMoney;

    @Schema(description = "未结算金额")
    private Double unPaidoutMoney;
}
