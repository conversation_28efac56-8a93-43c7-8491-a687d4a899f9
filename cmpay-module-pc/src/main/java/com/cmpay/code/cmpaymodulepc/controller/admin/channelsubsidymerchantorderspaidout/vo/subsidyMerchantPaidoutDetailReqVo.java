package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 邮政权益平台——结算明细请求
 * @date 2023-06-19 9:54:31
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class subsidyMerchantPaidoutDetailReqVo extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "结算状态")
    private String paidoutStatus;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "市id")
    private String partnerId;

    @Schema(description = "省id")
    private String branchId;

    @Schema(description = "通道")
    private String channel;

    @Schema(description = "平台")
    private Integer platform;
}
