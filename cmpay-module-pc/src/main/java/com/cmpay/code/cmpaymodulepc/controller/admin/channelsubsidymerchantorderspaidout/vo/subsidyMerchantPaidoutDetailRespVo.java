package com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo;

import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 邮政权益平台——结算明细响应
 * @date 2023-06-19 10:04:06
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class subsidyMerchantPaidoutDetailRespVo {

    @Schema(description = "分页对象")
    private PageResult<PaidoutDetailVo> pageResult;

    @Schema(description = "统计补贴明细")
    private StatisticsPaidoutDetailVo statistics;
}
