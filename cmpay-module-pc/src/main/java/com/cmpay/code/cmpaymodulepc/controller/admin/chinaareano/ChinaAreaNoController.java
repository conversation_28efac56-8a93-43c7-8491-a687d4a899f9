package com.cmpay.code.cmpaymodulepc.controller.admin.chinaareano;

import com.cmpay.code.cmpaymodulepc.dal.dataobject.chinaareano.ChinaAreaNoDO;
import com.cmpay.code.cmpaymodulepc.service.chinaareano.ChinaAreaNoService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-19 11:42:36
 * @version: 1.0
 */
@Tag(name = "管理后台 - 中国地区编号")
@RestController
@RequestMapping("/china/area/no")
public class ChinaAreaNoController {

    @Resource
    private ChinaAreaNoService chinaAreaNoService;

    /**
     * 查询所有省
     * 赋值父级id
     * 查询所有市
     * 根据市级的省名称去查询省id并赋值
     * 查询所有区县
     * 根据区县的市名称去查询市id并赋值
     */
    @GetMapping("batch-add-area-partnerId")
    @Operation(summary = "中国地区编号——批量添加地市父id")
    public CommonResult<String> batchAddAreaPartnerId() {
        return CommonResult.success(chinaAreaNoService.batchAddAreaPartnerId());
    }

    @GetMapping("/search-city")
    @Operation(summary = "公海商户——获取市级行政区域")
    public CommonResult<List<ChinaAreaNoDO>> searchCity(@RequestParam(value = "branchId", required = false) String branchId, @RequestParam(value = "partnerId", required = false) String partnerId, @RequestParam("platform") String platform, @RequestParam(value = "company", required = false) String company) {
        return CommonResult.success(chinaAreaNoService.searchPcCityAll(branchId, platform, partnerId, company));
    }

    @GetMapping("/search-dist")
    @Operation(summary = "公海商户——获取县级行政区域区")
    public CommonResult<List<ChinaAreaNoDO>> searchDist(@RequestParam("partnerId") String partnerId, @RequestParam(value = "company", required = false) String company, @RequestParam(value = "cityCode", required = false) String cityCode) {
        return CommonResult.success(chinaAreaNoService.searchDistAll(partnerId, company,cityCode));
    }
}
