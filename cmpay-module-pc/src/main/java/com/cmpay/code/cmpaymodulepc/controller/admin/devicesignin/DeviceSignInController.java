package com.cmpay.code.cmpaymodulepc.controller.admin.devicesignin;

import com.cmpay.code.cmpaymodulepc.service.devicesignin.DeviceSignInService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;



@Tag(name = "管理后台 - 添加班结签到")
@RestController
@RequestMapping("/device/sign-in")
@Validated
public class DeviceSignInController {

    @Resource
    private DeviceSignInService signInService;


}
