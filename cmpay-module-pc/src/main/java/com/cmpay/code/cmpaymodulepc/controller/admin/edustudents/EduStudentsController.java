package com.cmpay.code.cmpaymodulepc.controller.admin.edustudents;

import com.cmpay.code.cmpaymodulepc.controller.admin.edustudents.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.edustudents.EduStudentsConvert;
import com.cmpay.code.cmpaymodulepc.service.edustudents.EduStudentsService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 学校缴费")
@RestController
@RequestMapping("/edu-students/")
@Validated
public class EduStudentsController {

    @Resource
    private EduStudentsService eduStudentsService;

    @PostMapping("/create")
    @Operation(summary = "添加学生信息")
//    @PreAuthorize("@ss.hasPermission('edustudents::create')")
    public CommonResult<Boolean> create(@RequestBody @Validated EduStudentsCreateReqVO eduStudentsCreateReqVO){
        eduStudentsService.insertStudents(eduStudentsCreateReqVO);
        return success(true);
    }
    @PostMapping("/page")
    @Operation(summary ="学生信息列表")
//    @PreAuthorize("@ss.hasPermission('edustudents::page')")
    public CommonResult<PageResult<EduStudentsPageRespVO>> getPage(@RequestBody @Validated EduStudentsPageReqVO eduStudentsPageReqVO){
        return success(eduStudentsService.getPage(eduStudentsPageReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "修改学生信息")
    public CommonResult<Boolean> update(@RequestBody @Validated EduStudentsUpdateVO eduStudentsUpdateVO){
        eduStudentsService.update(eduStudentsUpdateVO);
        return success(true);
    }

    @PostMapping("/export-excel")
    @Operation(summary = "导出")
    public void exportStudents(@Valid EduStudentsPageReqVO eduStudentsPageReqVO, HttpServletResponse response) throws IOException {
        List<EduStudentsPageRespVO> list = eduStudentsService.getList(eduStudentsPageReqVO);
        List<EduStudentsExcelVO> convert = EduStudentsConvert.INSTANCE.convert(list);
        ExcelUtils.write(response, "学生信息.xls", "数据", EduStudentsExcelVO.class, convert);
    }

    @PostMapping("/send-bill-mssage")
    @Operation(summary = "发送账单消息")
    public CommonResult<Boolean> sendEduBillMssage(EduStudentsSendReqVO eduStudentsSendReqVO) throws InterruptedException {
        eduStudentsService.sendEduBillMssage(eduStudentsSendReqVO.getShopId(),eduStudentsSendReqVO.getBillName());
        return success(true);
    }

}
