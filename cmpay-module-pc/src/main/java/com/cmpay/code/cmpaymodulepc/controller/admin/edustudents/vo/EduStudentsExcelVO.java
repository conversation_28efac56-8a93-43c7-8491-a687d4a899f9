package com.cmpay.code.cmpaymodulepc.controller.admin.edustudents.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class EduStudentsExcelVO {


    @ExcelProperty("学号")
    private String stuNo;

    @ExcelProperty("姓名")
    private String stuName;

    @ExcelProperty("所在班级")
    private String stuClass;

    @ExcelProperty("绑定家长")
    private String name;

    @Schema(description = "openid")
    private String openid;

    @ExcelProperty("手机号")
    private String phone;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date insertTime;
}
