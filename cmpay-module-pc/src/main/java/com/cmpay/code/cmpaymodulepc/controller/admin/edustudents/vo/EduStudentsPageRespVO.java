package com.cmpay.code.cmpaymodulepc.controller.admin.edustudents.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class EduStudentsPageRespVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "学生号")
    private String stuNo;

    @Schema(description = "学生姓名")
    private String stuName;

    @Schema(description = " 商户号")
    private String shopId;

    @Schema(description = "学生班级")
    private String stuClass;

    @Schema(description = "openid")
    private String openid;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "绑定家长")
    private String name;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date insertTime;
}
