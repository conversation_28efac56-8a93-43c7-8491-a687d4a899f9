package com.cmpay.code.cmpaymodulepc.controller.admin.edustudents.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EduStudentsSendVO {
    @Schema(description = "学号")
    private String stuNo;

    @Schema(description = "学生姓名")
    private String stuName;

    @Schema(description = "学生所在班级")
    private String stuClass;

    @Schema(description = "subOpenid")
    private String subOpenid;

    @Schema(description = "账单名称")
    private String billName;

    @Schema(description = "projects")
    private String projects;

    @Schema(description = "需缴金额")
    private Double money;

    @Schema(description = "账单id")
    private String id;
}
