package com.cmpay.code.cmpaymodulepc.controller.admin.merchant;

import com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.merchant.MerchantConvert;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.merchant.MerchantDO;
import com.cmpay.code.cmpaymodulepc.service.merchant.MerchantService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.common.util.md5.MD5Util2;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import com.cmpay.code.framework.security.core.util.SecurityFrameworkUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 商户管理")
@RestController
@RequestMapping("/merchant/")
@Validated
@Slf4j
public class MerchantController {

    @Resource
    private MerchantService merchantService;

    @PostMapping("/create")
    @Operation(summary = "创建商户")
    @PreAuthorize("@ss.hasPermission('merchant::create')")
    public CommonResult<String> create(@Valid @RequestBody MerchantCreateReqVO createReqVO) {
        return success(merchantService.create(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('merchant::delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") String id) {
        merchantService.delete(id);
        return success(true);
    }


    @GetMapping("/list")
    @Operation(summary = "获得商户列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('merchant::query')")
    public CommonResult<List<MerchantRespVO>> getList(@RequestParam("ids") Collection<String> ids) {
        List<MerchantDO> list = merchantService.getList(ids);
        return success(MerchantConvert.INSTANCE.convertList(list));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出商户 Excel")
    @PreAuthorize("@ss.hasPermission('merchant::export')")
    @OperateLog(type = EXPORT)
    public void exportExcel(@Valid MerchantExportReqVO exportReqVO,
                            HttpServletResponse response) throws IOException {
        List<MerchantDO> list = merchantService.getList(exportReqVO);
        // 导出 Excel
        List<MerchantExcelVO> datas = MerchantConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商户.xls", "数据", MerchantExcelVO.class, datas);
    }

    @GetMapping("/get-industry")
    @Operation(summary = "获取商户的行业类目")
    @Parameter(name = "shopId", description = "商户号", required = true, example = "2048")
    public CommonResult<MerchantIndustryRespVO> getIndustryByShopId(@RequestParam("shopId") String shopId) {
        return success(merchantService.getIndustryByShopId(shopId));
    }

    @PutMapping("/update-refund-pwd")
    @Operation(summary = "修改商户退款密码")
    public CommonResult<Boolean> updateRefundPassword(@RequestBody @Valid RefundUpdateReqVO refundUpdateReqVO) {
        merchantService.updateRefundPassword(refundUpdateReqVO);
        return success(true);
    }

    /**
     * 预生成商户号
     *
     * @return CommonResult 统一返回类
     */
    @GetMapping("/getShopId")
    @Operation(summary = "商户管理 - 获取商户号初始信息")
//    @PreAuthorize("@ss.hasPermission('merchant:merchant:getShopId')")
    public CommonResult<MerchantGetShopIdRespVo> getShopId() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        log.info("预生成商户号请求:{}", userId);
        CommonResult<MerchantGetShopIdRespVo> commonResult = merchantService.getShopId();
        log.info("预生成商户号响应:{},{}", userId, commonResult);
        return commonResult;
    }

    @GetMapping("/search-merchant/{shopId}")
    @Operation(summary = "查询商户是否存在")
    public CommonResult<Boolean> searchMerchant(@PathVariable("shopId") String shopId) {
        if (shopId.length() == 10) {
            shopId = MD5Util2.encode(shopId);
        }
        MerchantDO merchant = merchantService.getMerchantByShopId(shopId);
        return success(merchant != null);
    }


    /**
     * 查询商户信息根据商户名称或者长短商户号
     *
     * @param condition 条件
     * @return CommonResult  统一返回类
     */
    @GetMapping("/getMerchantInfoByCondition")
    @Operation(summary = "查询商户信息根据商户名称或者长短商户号")
    @Parameters({
            @Parameter(name = "condition", description = "查询条件，支持：商户名，长短商户号", required = true)
    })
    public CommonResult<Map<String, List<GetMerchantInfoByConditionRespVO>>> getMerchantInfoByCondition(@RequestParam(name = "condition", required = false) String condition) {
        return merchantService.getMerchantInfoByCondition(condition);
    }

    @GetMapping("/page-by-large")
    @Operation(summary = "大商户的商户列表")
    public CommonResult<PageResult<LargeMerchantRespVO>> getByAgentSuId(@Valid LargeMerchantReqVO largeMerchantReqVO) {
        return success(merchantService.getPageByAgentSuId(largeMerchantReqVO));
    }

    @GetMapping("/export-agent-sub")
    @Operation(summary = "大商户的商户列表导出")
    public void exportAgentSuId(LargeMerchantReqVO largeMerchantReqVO, HttpServletResponse response) throws IOException {
        merchantService.exportAgentSuId(largeMerchantReqVO, response);
    }

    @GetMapping("/get-by-large-list")
    @Operation(summary = "大商户的商户下拉")
    public CommonResult<List<LargeMerchantRespVO>> selectListByAgentSuId(@RequestParam String agentSubId) {
        return success(merchantService.selectListByAgentSuId(agentSubId));
    }
}
