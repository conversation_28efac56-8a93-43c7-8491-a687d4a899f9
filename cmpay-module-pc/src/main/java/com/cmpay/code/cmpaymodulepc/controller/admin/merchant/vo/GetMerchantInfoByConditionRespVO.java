package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * author suoh<PERSON><PERSON>
 * date 2024/2/22 10:51
 * version 1.0
 */
@Data
public class GetMerchantInfoByConditionRespVO {
    @Schema(description = "短商户号")
    private String trueid;
    @Schema(description = "长商户号")
    private String shopId;
    @Schema(description = "商户简称")
    private String shopNickname;
}
