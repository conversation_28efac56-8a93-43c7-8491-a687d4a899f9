package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class LargeMerchantReqVO extends PageParam {

    @Schema(description = "商户名称")
    private String shopName;

    @Schema(description = "负责人")
    private String shopKeeper;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "商户号")
    private String shopId;

    @NotNull(message = "大商户号不能为空")
    @Schema(description = "大商户号")
    private String agentSubId;
}
