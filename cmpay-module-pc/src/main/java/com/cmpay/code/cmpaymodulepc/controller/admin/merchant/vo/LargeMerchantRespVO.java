package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LargeMerchantRespVO {
    @Schema(description = "商户号")
    @ExcelProperty("商户号")
    private String shopId;
    @Schema(description = "商户名称")
    @ExcelProperty("商户名称")
    private String shopName;
    @Schema(description = "商户简称")
    @ExcelProperty("商户简称")
    private String shopNickname;
    @Schema(description = "商户负责人")
    @ExcelProperty("商户负责人")
    private String shopKeeper;
    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String phone;
    @Schema(description = "商户地址")
    @ExcelProperty("商户地址")
    private String shopAddress;
    @Schema(description = "短商户号")
    @ExcelProperty("短商户号")
    private String trueid;
    @Schema(description = "注册时间")
    @ExcelProperty("注册时间")
    private String registerTime;
    @Schema(description = "大商户号")
    @ExcelProperty("大商户号")
    private String subAgentId;
}
