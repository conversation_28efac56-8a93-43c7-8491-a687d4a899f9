package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MerchantAllIdRespVO {

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "区县id")
    private String agentName;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "网点id")
    private String acceptName;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "市级id")
    private String partnerName;

    @Schema(description = "省级id")
    private String branchOfficeId;

    @Schema(description = "省级id")
    private String branchOfficeName;
}
