package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MerchantDebitCardRespVO {

    @Schema(description = "商户名称")
    private String shopName;
    @Schema(description = "结算人")
    private String keeper;
    @Schema(description = "手机号")
    private String phone;
    @Schema(description = "省")
    private String province;
    @Schema(description = "市")
    private String city;
    @Schema(description = "区")
    private String area;
    @Schema(description = "地址")
    private String address;
    @Schema(description = "商圈")
    private String tradeArea;
    @Schema(description = "一级分类")
    private String type;
    @Schema(description = "二级分类")
    private String subClassify;
    @Schema(description = "微信支付通道")
    private String payType;
    @Schema(description = "银行卡号")
    private String card;
    @Schema(description = "结算人")
    private String cardName;
    @Schema(description = "银行")
    private String bankName;
    @Schema(description = "支行")
    private String bankAddress;
    @Schema(description = "负责人电话")
    private String shopPhone;
    @Schema(description = "商户注册时间")
    private String registerTime;
    @Schema(description = "微信费率")
    private String rate;
    @Schema(description = "支付宝费率")
    private String ratealipay;
    @Schema(description = "和融通是否优质商户")
    private String yrmHighQuality;
    @Schema(description = "结算手机号")
    private String cardPhone;
    @Schema(description = "银行联号")
    private String bankAddNo;
    @Schema(description = "结算人身份证")
    private String identity;
    @Schema(description = "支付宝支付通道")
    private String payTypeAlipay;
    @Schema(description = "idCardType")
    private String idCardType = "0105";
    @Schema(description = "cardType")
    private String cardType = "0105";
    @Schema(description = "支付通道")
    private String payTypeStr;
    @Schema(description = "生效时间")
    private String effectTime;

}
