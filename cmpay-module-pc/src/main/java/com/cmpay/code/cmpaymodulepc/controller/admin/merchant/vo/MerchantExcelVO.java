package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 商户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MerchantExcelVO {

    @ExcelProperty("商户号")
    private String trueid;

    @ExcelProperty("商户名")
    private String shopName;

    @ExcelProperty("户主")
    private String shopKeeper;

    @ExcelProperty("电话")
    private String phone;

    @ExcelProperty("商户类型")
    private String businessTypeStr;

    @ExcelProperty("结算户名")
    private String cardName;

    @ExcelProperty("结算卡号")
    private String card;

    @ExcelProperty("结算银行")
    private String bankAddress;

    @ExcelProperty("区（县）级分公司")
    private String agentName;

    @ExcelProperty("营业所")
    private String acceptName;

    @ExcelProperty("微信")
    private String wxStatusStr;

    @ExcelProperty("微信费率")
    private String rate;

    @ExcelProperty("支付宝")
    private String alipayStatusStr;

    @ExcelProperty("支付宝费率")
    private String rateAlipay;

    @ExcelProperty("开户时间")
    private String registerTime;

    @ExcelProperty("微信驳回原因")
    private String wxRejectMsg;

    @ExcelProperty("支付宝驳回原因")
    private String aliRejectMsg;

    @ExcelProperty("备注")
    private String remarks;

}
