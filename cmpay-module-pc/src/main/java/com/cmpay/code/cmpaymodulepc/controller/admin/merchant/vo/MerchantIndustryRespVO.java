package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 商户行业类目 Request VO")
@Data
@ToString(callSuper = true)
public class MerchantIndustryRespVO {

    @Schema(description = "一级分类")
    private String type;

    @Schema(description = "二级分类")
    private String subClassify;

    @Schema(description = "通道")
    private String channel;

    @Schema(description = "角色pid")
    private String pid;

}
