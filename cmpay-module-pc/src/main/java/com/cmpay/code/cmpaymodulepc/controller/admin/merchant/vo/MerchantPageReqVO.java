package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 商户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MerchantPageReqVO extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "负责人")
    private String keeper;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "通道类型")
    private String subType;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "业务员")
    private String ywyOpenid;

    @Schema(description = "商户状态")
    private String status;

    @Schema(description = "上游商户号")
    private String upperMerchant;

    private String searchType;

    private String right;

    @Schema(description = "所属营业点")
    private String acceptId;

    private String merchantAcceptId;

    private String cloudpayStatus;

    private String type;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "平台")
    private Integer platform;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "省级id")
    private String branchId;

    @Schema(description = "操作人")
    private String account;

    @Schema(description = "微信商户号")
    private String wxMerchantNo;

    @Schema(description = "支付宝商户号")
    private String aliMerchantNo;
    @Schema(description = "上游商户号集合")
    private List<String> upNoList;

    @Schema(description = "商户名商户号")
    private String shopLongId;

    @Schema(description = "负责人身份证号")
    private String keeperIdentity;

}
