package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MerchantRespVO extends MerchantBaseVO {

    @Schema(description = "商户号", required = true, example = "27985")
    private String shopId;

}
