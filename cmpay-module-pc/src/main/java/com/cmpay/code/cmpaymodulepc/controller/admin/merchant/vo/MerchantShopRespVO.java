package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ToString(callSuper = true)
public class MerchantShopRespVO {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "区县名称")
    private String agentName;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "网点名称")
    private String acceptName;

    @Schema(description = "商户名称")
    private String shopName;

    @Schema(description = "商户简称")
    private String shopNickname;

    @Schema(description = "商户类型")
    private Integer businessType;

    @Schema(description = "商户类型中文")
    private String businessTypeStr;

    @Schema(description = "支付类型")
    private String payType;

    @Schema(description = "支付宝支付类型")
    private String payTypeAlipay;

    @Schema(description = "云闪付支付类型")
    private String payTypeUnionpay;

    @Schema(description = "负责人")
    private String shopKeeper;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "微信状态")
    private String wxStatus;

    @Schema(description = "微信状态中文")
    private String wxStatusStr;

    @Schema(description = "支付宝状态")
    private String alipayStatus;

    @Schema(description = "支付宝状态中文")
    private String alipayStatusStr;

    @Schema(description = "云闪付状态")
    private String unionpayStatus;

    @Schema(description = "云闪付状态中文")
    private String unionpayStatusStr;

    @Schema(description = "数币")
    private String shubiStatus;

    @Schema(description = "数币中文")
    private String shubiStatusStr;

    @Schema(description = "数币支付类型")
    private String payTypeDcpay;

    @Schema(description = "开户时间")
    private String registerTime;

    @Schema(description = "业务员openid")
    private String ywyOpenid;

    @Schema(description = "业务员名称")
    private String name;

    @Schema(description = "认证名称")
    private String pipeName;

    @Schema(description = "认证身份证号")
    private String pipeIdcard;

    @Schema(description = "认证手机号")
    private String pipePhone;

    @Schema(description = "是否收费?")
    private String isIncharge;

    @Schema(description = "商户状态")
    private Integer status;

    @Schema(description = "商户状态中文")
    private String statusStr;

    @Schema(description = "商户所在地", required = true)
    @NotNull(message = "商户所在地不能为空")
    private String shopAddress;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "internet")
    private Integer internet;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "一级分类", example = "1")
    private String type;

    @Schema(description = "省", required = true)
    @NotNull(message = "省不能为空")
    private String province;

    @Schema(description = "市", required = true)
    @NotNull(message = "市不能为空")
    private String city;

    @Schema(description = "微信小程序对应id", example = "28689")
    private String openid;

    @Schema(description = "微邮付商户号", example = "6916")
    private String trueid;

    @Schema(description = "结算卡号")
    private String card;

    @Schema(description = "结算人", example = "王五")
    private String cardName;

    @Schema(description = "结算手机号")
    private String cardPhone;

    @Schema(description = "银行", example = " ")
    private String bankName;

    @Schema(description = "支行")
    private String bankAddress;

    @Schema(description = "银行联号")
    private String bankAddNo;

    @Schema(description = "额度")
    private Object quota;

    @Schema(description = "sub_type", example = "1")
    private String subType;

    @Schema(description = "sub_mid", example = "31682")
    private String subMid;

    @Schema(description = "sub_appid", example = "2850")
    private String subAppid;

    @Schema(description = "sub_appidse")
    private String subAppidse;

    @Schema(description = "sub_openid", example = "2304")
    private String subOpenid;

    @Schema(description = "alipay_sub_id", example = "32695")
    private String alipaySubId;

    @Schema(description = "rate_pay")
    private BigDecimal ratePay;

    @Schema(description = "微信费率")
    private BigDecimal rate;

    @Schema(description = "支付宝费率")
    private BigDecimal rateAlipay;

    @Schema(description = "数币费率")
    private BigDecimal rateDcpay;

    @Schema(description = "云闪付费率")
    private BigDecimal rateUnionpay;

    @Schema(description = "rate_quota")
    private Object rateQuota;

    @Schema(description = "结算人身份证")
    private String identity;

    @Schema(description = "wechatid", example = "3882")
    private String wechatid;

    @Schema(description = "subsidy_status", required = true, example = "1")
    @NotNull(message = "subsidy_status不能为空")
    private Integer subsidyStatus;

    @Schema(description = "subsidy", required = true)
    @NotNull(message = "subsidy不能为空")
    private BigDecimal subsidy;

    @Schema(description = "经度")
    private String lng;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "merchant_id", example = "7281")
    private String merchantId;

    @Schema(description = "sub_merchant_id", example = "10658")
    private String subMerchantId;

    @Schema(description = "商户logo地址", example = "https://www.iocoder.cn")
    private String logoUrl;

    @Schema(description = "lc_merchant_no")
    private String lcMerchantNo;

    @Schema(description = "lc_merchant_level")
    private Integer lcMerchantLevel;

    @Schema(description = "lc_terminal_id", example = "27503")
    private String lcTerminalId;

    @Schema(description = "lc_access_token")
    private String lcAccessToken;

    @Schema(description = "lc_contract_status", example = "1")
    private Integer lcContractStatus;

    @Schema(description = "micro_mch_id", example = "158")
    private String microMchId;

    @Schema(description = "二级分类")
    private String subClassify;

    @Schema(description = "区")
    private String area;

    @Schema(description = "商圈")
    private String tradeArea;

    @Schema(description = "特色、主要服务内容")
    private String feature;

    @Schema(description = "负责人电话")
    private String shopPhone;

    @Schema(description = "wft_xy_merchant_no")
    private String wftXyMerchantNo;

    @Schema(description = "wft_xy_merchant_key")
    private String wftXyMerchantKey;

    @Schema(description = "sub_channel_status", example = "2")
    private Integer subChannelStatus;

    @Schema(description = "wft_xy_merchant_password")
    private String wftXyMerchantPassword;

    @Schema(description = "description", example = "你说的对")
    private String description;

    @Schema(description = "agent_sub_openid", example = "30163")
    private String agentSubOpenid;

    @Schema(description = "sub_agent_id", example = "31847")
    private String subAgentId;

    @Schema(description = "商户所属场景")
    private String scene;

    @Schema(description = "refund_auth")
    private String refundAuth;

    @Schema(description = "alipay_store_id", example = "10676")
    private String alipayStoreId;

    @Schema(description = "reject")
    private String reject;

    @Schema(description = "agent_rate")
    private BigDecimal agentRate;

    @Schema(description = "is_member", required = true)
    @NotNull(message = "is_member不能为空")
    private Integer isMember;

    @Schema(description = "agent_rate_alipay")
    private BigDecimal agentRateAlipay;

    @Schema(description = "agent_rate_unionpay")
    private BigDecimal agentRateUnionpay;

    @Schema(description = "member_type", example = "1")
    private Integer memberType;

    @Schema(description = "auth_bank4")
    private Integer authBank4;

    @Schema(description = "xtbank_merchant_no")
    private String xtbankMerchantNo;

    @Schema(description = "xtbank_proxy_no")
    private String xtbankProxyNo;

    @Schema(description = "leshua_merchant_no")
    private String leshuaMerchantNo;

    @Schema(description = "leshua_merchant_key")
    private String leshuaMerchantKey;

    @Schema(description = "zhonghui_merchant_no")
    private String zhonghuiMerchantNo;

    @Schema(description = "refund_password")
    private String refundPassword;

    @Schema(description = "yrm_merchant_no")
    private String yrmMerchantNo;

    @Schema(description = "sxf_merchant_no")
    private String sxfMerchantNo;

    @Schema(description = "和融通是否优质商户")
    private Integer yrmHighQuality;

    @Schema(description = "和融通优质商户300以内返佣，单位千分之")
    private BigDecimal yrmHighQualityAgentRate;

    @Schema(description = "随行付微信商户号", example = "8640")
    private String sxfMerchantMchId;

    @Schema(description = "随行付开户编码")
    private String sxfMerchantTaskCode;

    @Schema(description = "yrm_org_id", example = "14860")
    private String yrmOrgId;

    @Schema(description = "yrm_org_key")
    private String yrmOrgKey;

    @Schema(description = "随行付代理商号", example = "21326")
    private String sxfOrgId;

    @Schema(description = "随行付代理商私钥")
    private String sxfOrgPriviteKey;

    @Schema(description = "sb_merchant_no")
    private String sbMerchantNo;

    @Schema(description = "和融通结算子通道，在使用快银秘钥发起支付，但结算不为快银的商户，如沈小平的户，亿企富部分户")
    private String yrmPaidoutChannelSub;

    @Schema(description = "fft_merchant_no")
    private String fftMerchantNo;

    @Schema(description = "fft_terminal_id", example = "24847")
    private String fftTerminalId;

    @Schema(description = "wsy_client_id", example = "12214")
    private String wsyClientId;

    @Schema(description = "jl_merchant_no")
    private String jlMerchantNo;

    @Schema(description = "mch_id", example = "29551")
    private String mchId;

    @Schema(description = "cp_mch_id", example = "8567")
    private String cpMchId;

    @Schema(description = "cp_merchant_no")
    private String cpMerchantNo;

    @Schema(description = "cp_merchant_auth_key")
    private String cpMerchantAuthKey;

    @Schema(description = "cp_merchant_device_no")
    private String cpMerchantDeviceNo;

    @Schema(description = "cp_merchant_device_id", example = "28740")
    private String cpMerchantDeviceId;

    @Schema(description = "cp_merchant_cashier")
    private String cpMerchantCashier;

    @Schema(description = "商品订单前缀")
    private String cpMerchantOrderPrefix;

    @Schema(description = "管理员身份")
    private String keeperIdentity;

    @Schema(description = "商业号")
    private String chMerchantNo;

    @Schema(description = "0:不允许；1：允许，默认不允许", required = true)
    @NotNull(message = "0:不允许；1：允许，默认不允许不能为空")
    private Integer isUpdateMer;

    @Schema(description = "修改密码时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime upPwdTime;

    @Schema(description = "0:不允许；1：允许，默认不允许")
    private Integer isAllowWangjin;

    @Schema(description = "ximid", example = "31439")
    private String xmid;

    @Schema(description = "是否邮政换码商户：0：否；1：换码")
    private Integer isYzSwap;

    @Schema(description = "邮政商户换码时间", required = true)
    private String yzSwapTime;

    @Schema(description = "商户原始开户时间", required = true)
    @NotNull(message = "商户原始开户时间不能为空")
    private String orgRegisterTime;

    @Schema(description = "渠道id，市级id", example = "18921")
    private String partnerId;

    @Schema(description = "市名称")
    private String partnerName;

    @Schema(description = "分公司id", example = "3141")
    private Integer branchId;

    @Schema(description = "账户类型")
    private Integer accountType;

    @Schema(description = "账户类型中文")
    private String accountTypeStr;

    @Schema(description = "支付宝通道中文")
    private String aliPayStr;

    @Schema(description = "微信通道中文")
    private String wxPayStr;

    @Schema(description = "云闪付通道中文")
    private String unionPayStr;

    @Schema(description = "数币通道中文")
    private String dcPayStr;

    @Schema(description = "支付宝认证状态")
    private Integer aliPayAuthStatus;

    @Schema(description = "微信认证状态")
    private Integer wxPayAuthStatus;

    @Schema(description = "支付宝认证状态中文")
    private String aliPayAuthStatusStr;

    @Schema(description = "支付宝认证返回状态")
    private String aliPayAuthMsg;

    @Schema(description = "微信认证状态中文")
    private String wxPayAuthStatusStr;

    @Schema(description = "微信超阈值费率")
    private BigDecimal wxThresholdTate;

    @Schema(description = "支付宝超阈值费率")
    private BigDecimal aliThresholdRate;

    @Schema(description = "云闪付超阈值费率")
    private BigDecimal unionThresholdRate;

    @Schema(description = "阶梯类型")
    private String ladderType;

    @Schema(description = "交易阈值")
    private Integer endAmt;

    @Schema(description = "补贴阈值")
    private Integer subsides;

    @Schema(description = "开户政策")
    private String policy;

    @Schema(description = "业务员xmid")
    private String ywyXmid;

    @Schema(description = "微信驳回原因")
    private String wxRejectMsg;

    @Schema(description = "支付宝驳回原因")
    private String aliRejectMsg;

    @Schema(description = "商户经营名称，只用于显示，不上送上游")
    private String shopBusinessName;


}
