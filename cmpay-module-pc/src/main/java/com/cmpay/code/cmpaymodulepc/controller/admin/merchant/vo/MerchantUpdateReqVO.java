package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商户更新 Request VO")
@Data
@ToString(callSuper = true)
public class MerchantUpdateReqVO {

    @Schema(description = "xmid", required = true, example = "27985")
    @NotNull(message = "xmid不能为空")
    private String xmid;

    @Schema(description = "是否认证")
    private Integer isOrNotCertified;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "平台id")
    private String platform;
}
