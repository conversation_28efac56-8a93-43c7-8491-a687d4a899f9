package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Data
public class RefundUpdateReqVO {

    @Schema(description = "商户号")
    @NotNull(message = "商户号不能为空")
    String shopId;
    @Schema(description = "新密码")
    @NotNull(message = "新密码不能为空")
    @Length(min = 8, max = 16, message = "新密码长度为 8-16 位")
    String newPwd;
    @Schema(description = "旧密码")
    @NotNull(message = "旧密码不能为空")
    String oldPwd;
}
