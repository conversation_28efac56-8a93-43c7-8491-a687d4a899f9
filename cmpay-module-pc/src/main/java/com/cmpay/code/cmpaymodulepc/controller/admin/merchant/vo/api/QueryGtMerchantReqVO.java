package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: 应用查询国通请求VO
 * @Date 2024/12/18 10:05
 * @version: 1.0
 */
@Data
public class QueryGtMerchantReqVO {

    @Schema(description = "商户标识")
    private String shopId;

    @Schema(description = "应用id")
    @NotBlank(message = "应用id不能为空")
    private String programId;

    @Schema(description = "国通商户号")
    private String gtMerchantNo;

    @Schema(description = "签名")
    private String sign;
}
