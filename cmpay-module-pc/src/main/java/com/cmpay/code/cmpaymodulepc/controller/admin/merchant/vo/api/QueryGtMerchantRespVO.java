package com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 应用查询国通返回VO
 * @Date 2024/12/17 9:20
 * @version: 1.0
 */
@Data
public class QueryGtMerchantRespVO {

    @Schema(description = "商户标识")
    private String shopId;

    @Schema(description = "商户号")
    private String trueId;

    @Schema(description = "商户名称")
    private String shopName;

    @Schema(description = "当前通道")
    private String channel;

    @Schema(description = "微信商户号")
    private String gtWxMerchantNo;

    @Schema(description = "支付宝商户号")
    private String gtAliMerchantNo;

    @Schema(description = "国通商户号")
    private String gtMerchantNo;

    @Schema(description = "微信认证状态")
    private String wxAuthorizeState;

    @Schema(description = "微信认证状态描述")
    private String wxAuthorizeReject;

    @Schema(description = "支付宝认证状态")
    private String aliAuthorizeState;

    @Schema(description = "支付宝认证状态描述")
    private String aliAuthorizeReject;
}
