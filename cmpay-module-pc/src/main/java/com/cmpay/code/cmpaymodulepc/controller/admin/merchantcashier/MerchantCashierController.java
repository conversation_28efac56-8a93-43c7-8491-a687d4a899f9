package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier;

import com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.MerchantUpdateReqVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo.*;
import com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo.PersonInChargeSimpleRespVO;
import com.cmpay.code.cmpaymodulepc.service.merchantcashier.MerchantCashierService;
import com.cmpay.code.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.common.util.number.NumberUtils;
import com.cmpay.code.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 邮付小助手团队成员用户")
@RestController
@RequestMapping("/merchantcashier/merchant-cashier")
@Validated
public class MerchantCashierController {

    @Resource
    private MerchantCashierService merchantCashierService;


    /*
            /merchantcashier/merchant-cashier/create
            /merchantcashier/merchant-cashier/delete
            /merchantcashier/merchant-cashier/update-role
     */
    @GetMapping("/page")
    @Operation(summary = "获得团队成员分页")
//    @PreAuthorize("@ss.hasPermission('merchantcashier:merchant-cashier:query')")
    public CommonResult<PageResult<ShopTeamRespVO>> getShopTeamPage(@Valid ShopTeamPageReqVO shopTeamPageReqVO) {
        PageResult<ShopTeamRespVO> pageResult = merchantCashierService.getShopTeamPage(shopTeamPageReqVO);
        return success(pageResult);
    }


    @PostMapping("/create")
    @Operation(summary = "创建团队成员用户")
//    @PreAuthorize("@ss.hasPermission('merchantcashier:merchant-cashier:create')")
    public CommonResult<String> createMerchantCashier(@Valid @RequestBody MerchantCashierCreateReqVO createReqVO) {
        return success(GlobalErrorCodeConstants.SUCCESS.getCode(),"成功",merchantCashierService.createMerchantCashier(createReqVO));

    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除团队用户")
//    @PreAuthorize("@ss.hasPermission('merchantcashier:merchant-cashier:delete')")
    @Parameters({
            @Parameter(name = "xmid", description = "xmid", required = true, example = "2048"),
            @Parameter(name = "shopId", description = "商户号", required = true, example = "2048"),
            @Parameter(name = "isOrNotCertified", description = "是否认证", required = true, example = "1")
    })
    public CommonResult<Boolean> delete(@RequestParam("xmid") String xmid, @RequestParam("shopId") String shopId,
                                        @RequestParam("isOrNotCertified") Integer isOrNotCertified
    ) {
        merchantCashierService.deleteMerchantCashier(xmid, shopId,isOrNotCertified);
        return success(true);
    }

    @PutMapping("/update-pwd")
    @Operation(summary = "修改团队用户密码")
//    @PreAuthorize("@ss.hasPermission('merchantcashier:merchant-cashier:update')")
    @Parameter(name = "xmid", description = "xmid", required = true, example = "2048")
    public CommonResult<Boolean> updatePwd(@RequestBody MerchantUpdateReqVO merchantUpdateReqVO) {
        merchantCashierService.updatePwd(merchantUpdateReqVO);
        return success(true);
    }

    @PostMapping("/update-role")
    @Operation(summary = "重置角色")
    public CommonResult<Boolean> updateIsBoss(@RequestBody MerchantUpdateReqVO merchantUpdateReqVO) {
        merchantCashierService.updateIsBoss(merchantUpdateReqVO.getXmid(),merchantUpdateReqVO.getShopId(),merchantUpdateReqVO.getIsOrNotCertified(),merchantUpdateReqVO.getPlatform());
        return success(true);
    }

    @GetMapping("get-qrc")
    @Operation(summary = "生成二维码")
    @Parameters({
            @Parameter(name = "name", description = "姓名", required = true, example = "张三"),
            @Parameter(name = "phone", description = "手机号", required = true, example = "15744587865"),
            @Parameter(name = "isBoss", description = "角色", required = true, example = "0"),
            @Parameter(name = "shopId", description = "商户号", required = true, example = "2048"),
            @Parameter(name = "shopName", description = "商户名", required = true, example = "XX商户"),
            @Parameter(name = "xmid", description = "xmid", required = true, example = "2048")
    })
    public CommonResult<String> generateJoinTeamQRC(String name, String phone, String isBoss, String shopId,
                                                    String shopName, String xmid) {
        return success(GlobalErrorCodeConstants.SUCCESS.getCode(),"成功",merchantCashierService.generateJoinTeamQRC(name, phone, isBoss, shopId, shopName, xmid));
    }

    @PostMapping("/initCashier")
    @Operation(summary = "商户系统——初始化收银员")
    public CommonResult<Set<InitCashierRespVo>> initCashier(@RequestBody InitCashierReqVo initCashier) {
        return merchantCashierService.initCashier(initCashier);
    }

    @PutMapping("/update")
    @Operation(summary = "修改")
    public CommonResult<Boolean> update(@RequestBody @Valid MerchantCashierUpdateReqVO updateReqVO) {
        merchantCashierService.update(updateReqVO);
        return success(true);
    }

    //@PostConstruct
    @GetMapping("/initUsers")
    @Operation(summary = "初始化商户用户数据")
    @PermitAll
    //@OperateLog(type = OperateTypeEnum.OTHER)
    public String initUsers() {
        return merchantCashierService.initUsers();
    }

    //@PostConstruct
    @PostMapping("/initMerchantUpdateXmid")
    @Operation(summary = "处理商户xmid问题")
    @PermitAll
    //@OperateLog(type = OperateTypeEnum.OTHER)
    public String initMerchantUpdateXmid(@RequestBody InitOneUsersReqVo initOneUsersReqVo) {
        return merchantCashierService.initMerchantUpdateXmid(initOneUsersReqVo);
    }

    @GetMapping("/initOneUsers/{phone}")
    @Operation(summary = "初始化单个商户")
    @PermitAll
    //@OperateLog(type = OperateTypeEnum.OTHER)
    public String initOneUsers(@PathVariable("phone") String phone) {
        return merchantCashierService.initOneUsers(phone);
    }


    @GetMapping("/send-code")
    @PermitAll
    @Operation(summary = "发送验证码")
    public CommonResult<Boolean> sendsms(String phone) throws Exception {
        boolean flag = NumberUtils.validatePhoneNumber(phone);
        if (!flag) {
            String decodedPhone = URLDecoder.decode(phone, StandardCharsets.UTF_8.name());
            phone = merchantCashierService.decryptRsa(decodedPhone);
        }
        merchantCashierService.sendsms(phone);
        return success(true);
    }



    @Operation(summary = "校验验证码")
    @GetMapping("/check-code")
    @PermitAll
    public CommonResult<Boolean> checkCode(@RequestParam("phone") String phone,@RequestParam("code") String code) throws Exception {
        boolean flag = NumberUtils.validatePhoneNumber(phone);
        if (!flag) {
            String decodedPhone = URLDecoder.decode(phone, StandardCharsets.UTF_8.name());
            phone = merchantCashierService.decryptRsa(decodedPhone);
        }
        merchantCashierService.checkCode(phone,code);
        return success(true);
    }

    @PostMapping("/update-user-pwd")
    @Operation(summary = "个人中心修改密码")
    public CommonResult<Boolean> updateUserPwd(@RequestBody @Valid UserProfileUpdatePasswordReqVO userProfileUpdatePasswordReqVO){
        merchantCashierService.updateUserPwd(userProfileUpdatePasswordReqVO);
        return success(true);
    }

    @GetMapping("/simple-list")
    @Operation(summary = "门店店长下拉")
    public CommonResult<List<PersonInChargeSimpleRespVO>> getByshopIdAndIsBoss(@RequestParam("shopId")String shopId){
        return success(merchantCashierService.getByshopIdAndIsBoss(shopId));
    }

    @GetMapping("/insertMerchantUser/{phone}")
    @Operation(summary = "添加单个商户用户")
    @PermitAll
    public CommonResult<String> insertMerchantUser(@PathVariable("phone")String phone){
        return success(merchantCashierService.insertMerchantUser(phone));
    }

}
