package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 初始化收银员
 * @date 2023-05-08 14:54:09
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitCashierReqVo {

    @Schema(description = "商户id", required = true)
    private String shopId;

    @Schema(description = "分店")
    private String device;

    @Schema(description = "当前人身份", required = true)
    private String isBoss;

    @Schema(description = "xmid", required = true)
    private String xmid;
}
