package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-05-08 14:57:10
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitCashierRespVo {

    @Schema(description = "收银员名称")
    private String cashier;

    @Schema(description = "xmid")
    private String xmid;

    @Schema(description = "openid")
    private String openid;
}
