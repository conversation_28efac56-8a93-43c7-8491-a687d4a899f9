package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-04 17:02:16
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitOneUsersReqVo {

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "密码")
    private String password;
}
