package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 邮付小助手团队成员用户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MerchantCashierBaseVO {

    @Schema(description = "商户号", required = true, example = "24020")
    @NotNull(message = "商户号不能为空")
    private String shopId;

    @Schema(description = "0无身份；1负责人；2店长；3收银员；4超管；", required = true, example = "21165")
    @NotNull(message = "0无身份；1负责人；2店长；3收银员；4超管；不能为空")
    private String openid;

    @Schema(description = "密码")
    private String pw;

    @Schema(description = "微信号对应小程序id")
    private Integer isOrNotCertified;

    @Schema(description = "插入时间", required = true)
    @NotNull(message = "插入时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime insertTime;

    @Schema(description = "1:老板", required = true)
    @NotNull(message = "1:老板不能为空")
    private Integer isBoss;

    @Schema(description = "对应用户表的id", example = "24807")
    private String xmid;

}
