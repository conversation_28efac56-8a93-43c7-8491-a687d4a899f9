package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 邮付小助手团队成员用户创建 Request VO")
@Data
@ToString(callSuper = true)
public class MerchantCashierCreateReqVO {
    @Schema(description = "姓名")
    private String name;
    // @Mobile
    @Schema(description = "手机号")
    private String phone;
    @Schema(description = "角色")
    private Integer isBoss;
    @Schema(description = "收银台")
    private List<String> cashier;
    @Schema(description = "分店")
    private List<Long> branchStore;
    @Schema(description = "商户id")
    private String shopId;

    @Schema(description = "商户名称")
    private String shopName;



}
