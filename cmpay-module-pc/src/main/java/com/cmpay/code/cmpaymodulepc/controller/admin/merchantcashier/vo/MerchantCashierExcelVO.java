package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邮付小助手团队成员用户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MerchantCashierExcelVO {

    @ExcelProperty("主键id")
    private Long id;

    @ExcelProperty("商户号		")
    private String shopId;

    @ExcelProperty("0无身份；1负责人；2店长；3收银员；4超管；")
    private String openid;

    @ExcelProperty("密码")
    private String pw;

    @ExcelProperty("微信号对应小程序id")
    private Integer isOrNotCertified;

    @ExcelProperty("插入时间")
    private LocalDateTime insertTime;

    @ExcelProperty("1:老板")
    private Integer isBoss;

    @ExcelProperty("对应用户表的id")
    private String xmid;

}
