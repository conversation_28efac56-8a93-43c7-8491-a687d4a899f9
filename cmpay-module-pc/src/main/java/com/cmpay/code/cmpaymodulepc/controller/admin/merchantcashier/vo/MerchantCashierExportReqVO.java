package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 邮付小助手团队成员用户 Excel 导出 Request VO，参数和 MerchantCashierPageReqVO 是一致的")
@Data
public class MerchantCashierExportReqVO {

    @Schema(description = "商户号		", example = "24020")
    private String shopId;

    @Schema(description = "0无身份；1负责人；2店长；3收银员；4超管；", example = "21165")
    private String openid;

    @Schema(description = "密码")
    private String pw;

    @Schema(description = "微信号对应小程序id")
    private Integer isOrNotCertified;

    @Schema(description = "插入时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] insertTime;

    @Schema(description = "1:老板")
    private Integer isBoss;

    @Schema(description = "对应用户表的id", example = "24807")
    private String xmid;

}
