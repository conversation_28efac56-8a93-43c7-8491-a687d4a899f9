package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MerchantCashierHandlerVO {
    @Schema(description = "商户号")
    private String shopId;
    @Schema(description = "商户号")
    private String trueId;
    @Schema(description = "商户名")
    private String shopName;
    @Schema(description = "身份")
    private String isBoss;
    @Schema(description = "身份str")
    private String isBossStr;
    @Schema(description = "商户状态str")
    private String statusStr;
    @Schema(description = "商户状态")
    private String status;
}
