package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 邮付小助手团队成员用户更新 Request VO")
@Data
@ToString(callSuper = true)
public class MerchantCashierUpdateReqVO {

    @Schema(description = "主键id", required = true, example = "15102")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "xmid")
    private String xmid;

    @Schema(description = "权限")
    private Integer isBoss;

    @Schema(description = "是否认证")
    private Integer isOrNotCertified;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "收银台")
    private List<String> cashier;

    @Schema(description = "分店")
    private List<Long> branchStore;

    @Schema(description = "平台号")
    private String platform;

}
