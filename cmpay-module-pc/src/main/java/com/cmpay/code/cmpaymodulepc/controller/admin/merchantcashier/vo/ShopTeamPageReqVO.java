package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 商户团队成员用户分页 Request VO")
@Data
@ToString(callSuper = true)
public class ShopTeamPageReqVO extends PageParam {
    @Schema(description = "商户id")
    private String shopId;
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "平台")
    private Integer platform;

}
