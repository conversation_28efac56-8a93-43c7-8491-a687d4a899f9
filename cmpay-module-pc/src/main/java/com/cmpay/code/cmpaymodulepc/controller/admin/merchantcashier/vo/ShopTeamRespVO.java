package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 商户团队成员用户分页 Respone VO")
@Data
@ToString(callSuper = true)
public class ShopTeamRespVO {

    @Schema(description = "id")
    private String id;
    @Schema(description = "头像url")
    private String headImgUrl;
    @Schema(description = "创建时间")
    private String insertTime;
    @Schema(description = "角色id")
    private Integer isBoss;
    @Schema(description = "角色名")
    private String isBossName;
    @Schema(description = "是否微信认证 1-已认证，0-未认证")
    private Integer isOrNotCertified;
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "是否语音播报 1-是，0-否")
    private Integer isOpend;
    @Schema(description = "手机号")
    private String phone;
    @Schema(description = "xmid")
    private String xmid;
    @Schema(description = "收银台")
    private List<String> shortKey;
    @Schema(description = "店长")
    private List<String> branchStore;





}
