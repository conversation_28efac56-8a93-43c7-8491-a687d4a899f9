package com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class UpdatePwdCodeReqVO {
    @NotNull(message = "手机号不能为空！")
    @Schema(description = "手机号")
    private String phone;
    @NotNull(message = "验证码不能为空！")
    @Schema(description = "验证码")
    private String code;
    @NotNull(message = "密码不能为空！")
    @Schema(description = "新密码")
    @Length(min = 8, max = 16, message = "长度为 8-16 位")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).+$", message = "密码格式必须包含数字大写字母和小写字母")
    private String newPwd;
    @NotNull(message = "确认密码不能为空！")
    @Schema(description = "确认密码")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).+$", message = "密码格式必须包含数字大写字母和小写字母")
    private String confirmPwd;

    @Schema(description = "平台")
    private String platform;
}
