package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders;

import com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo.*;
import com.cmpay.code.cmpaymodulepc.service.merchantdutyorders.MerchantDutyOrdersService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "管理后台 - 商户班结信息")
@RestController
@RequestMapping("/merchant/duty-orders")
@Validated
public class MerchantDutyOrdersController {

    @Resource
    private MerchantDutyOrdersService merchantDutyOrdersService;

//    @PermitAll
    @PostMapping("/searchDutyMessage")
    @Operation(summary = "商户班结信息——查询班结信息")
    //@PreAuthorize("@ss.hasPermission('class-conclusion:query')")
    public CommonResult<PageResult<SearchDutyMessageRespVo>> searchDutyMessage(@RequestBody SearchDutyMessageReqVo dutyMessage) {
        return merchantDutyOrdersService.searchDutyMessage(dutyMessage);
    }

//    @PermitAll
    @PostMapping("/searchDutyDetails")
    @Operation(summary = "商户班结信息——查询班结信息详情")
    public CommonResult<SearchDutyDetailsRespVo> searchDutyDetails(@RequestBody @Valid SearchDutyDetailsReqVo dutyDetails) {
        return merchantDutyOrdersService.searchDutyDetails(dutyDetails);
    }

//    @PermitAll
    @PostMapping("/insert")
    @Operation(summary = "商户班结信息——添加班结")
    //@PreAuthorize("@ss.hasPermission('class-conclusion:add')")
    public CommonResult<String> insertDuty(@RequestBody InsertDutyReqVo insertDuty) {
        return merchantDutyOrdersService.insertDuty(insertDuty);
    }

    @PostMapping("/add")
    @Operation(summary = "商户班结-添加")
    public CommonResult<String> addDuty(@RequestBody  AddDutyReqVo addDuty) {
        return merchantDutyOrdersService.addDuty(addDuty);
    }

    @PostMapping("/searchDuty")
    @Operation(summary = "商户班结-详情")
    public CommonResult<SearchDutyReqVo> searchDuty(@RequestBody  AddDutyReqVo addDuty) {
        return merchantDutyOrdersService.searchDuty(addDuty);
    }

    @PostMapping("/queryNotTraded")
    @Operation(summary = "商户班结-是否交易")
    public CommonResult<QueryNotVo> queryNotTraded(@RequestBody  AddDutyReqVo addDuty) {
        return merchantDutyOrdersService.queryNotTraded(addDuty);
    }

    @PostMapping("/queryDutyDevices")
    @Operation(summary = "商户班结-查询门店收银台")
    public CommonResult<DutyDevicesAndShortUrlRespVO> getDutyDevicesAndShortUrl(@RequestBody DutyDevicesAndShortUrlRepVO dutyDevicesAndShortUrlRepVO){
        return CommonResult.success(merchantDutyOrdersService.getDutyDevicesAndShortUrl(dutyDevicesAndShortUrlRepVO));
    }
}
