package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AddDutyReqVo {
    @Schema(description = "默认")
    private String source;
    @Schema(description = "商户号")
    private String shopId;
    @Schema(description = "用户ID")
    private String xmid;
    @Schema(description = "登录账号")
    private Long phone;
    @Schema(description = "身份")
    private String isBoss;
    @Schema(description = "记录ID")
    private String dutyId;
    @Schema(description = "分店ID")
    private List<String> deviceIds;
    @Schema(description = "终端")
    private List<String> shortKeys;
}
