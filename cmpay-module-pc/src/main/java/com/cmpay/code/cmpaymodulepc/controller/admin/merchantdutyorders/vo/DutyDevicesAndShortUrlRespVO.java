package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.InitDevicesRespVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.InitShortUrlRespVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DutyDevicesAndShortUrlRespVO {
    private List<InitDevicesRespVo> initDevicesRespVos;

    private List<InitShortUrlRespVo> initShortUrlRespVos;
}
