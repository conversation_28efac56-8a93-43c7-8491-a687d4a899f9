package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.InitDevicesRespVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InsertDutyReqVo {

    @Schema(description = "商户id", required = true)
    private String shopId;

    @Schema(description = "分店名称集合")
    private List<InitDevicesRespVo> deviceList;

    @Schema(description = "分店")
    private String device;

    @Schema(description = "分店Id")
    private String deviceId;

    @Schema(description = "分店Id")
    private String isBoos;

    @Schema(description = "终端key集合")
    private List<String> shortKeyList;

    @Schema(description = "终端")
    private String shortKey;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
}
