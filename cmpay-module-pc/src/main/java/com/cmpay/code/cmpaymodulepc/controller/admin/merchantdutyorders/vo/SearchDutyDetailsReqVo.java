package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchDutyDetailsReqVo {

    @Schema(description = "班结id", required = true)
    @NotNull(message = "班结id不能为空")
    private String dutyId;

    @Schema(description = "商户id", required = true)
    @NotNull(message = "商户id不能为空")
    private String shopId;
}
