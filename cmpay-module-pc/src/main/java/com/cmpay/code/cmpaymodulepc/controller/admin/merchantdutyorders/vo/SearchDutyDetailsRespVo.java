package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchDutyDetailsRespVo {

    @Schema(description = "微信总订单")
    private int wxNum = 0;

    @Schema(description = "微信总金额")
    private Double wxMoney = 0.0;

    @Schema(description = "微信退款订单")
    private int wxRefundNum=0;

    @Schema(description = "微信退款金额")
    private Double wxRefundMoney = 0.0;

    @Schema(description = "支付宝总订单")
    private int aliNum = 0;

    @Schema(description = "支付宝总金额")
    private Double aliMoney = 0.0;

    @Schema(description = "支付宝退款订单")
    private int aliRefundNum = 0;

    @Schema(description = "支付宝退款金额")
    private Double aliRefundMoney = 0.0;

    @Schema(description = "云闪付总订单")
    private int uniNum = 0;

    @Schema(description = "云闪付总金额")
    private Double uniMoney = 0.0;

    @Schema(description = "云闪付退款订单")
    private int uniRefundNum = 0;

    @Schema(description = "云闪付退款金额")
    private Double uniRefundMoney = 0.0;

    @Schema(description = "数币总订单")
    private int dcpayNum = 0;

    @Schema(description = "数币总金额")
    private Double dcpayMoney = 0.0;

    @Schema(description = "数币退款订单")
    private int dcpayRefundNum = 0;

    @Schema(description = "数币退款金额")
    private Double dcpayRefundMoney = 0.0;

    @Schema(description = "会员总订单")
    private int memNum = 0;

    @Schema(description = "会员总金额")
    private Double memMoney = 0.0;

    @Schema(description = "其他总订单")
    private int otherNum = 0;

    @Schema(description = "其他总金额")
    private Double otherMoney = 0.0;

    @Schema(description = "总订单量")
    private int totalNum = 0;

    @Schema(description = "总金额")
    private Double totalMoney = 0.0;

    @Schema(description = "退款订单量")
    private int totalRefundNum = 0;

    @Schema(description = "退款金额")
    private Double totalRefundMoney = 0.0;

    @Schema(description = "银行卡笔数")
    private int bankNum=0;

    @Schema(description = "银行卡金额")
    private Double bankMoney=0.0;

    @Schema(description = "银行卡退款笔数")
    private int bankRefundNum=0;

    @Schema(description = "银行卡退款金额")
    private Double bankRefundMoney=0.0;
}
