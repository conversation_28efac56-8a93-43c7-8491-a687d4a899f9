package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-27 15:05:46
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchDutyMessageReqVo extends PageParam {

    @Schema(description = "当前身份", required = true)
    private String isBoss;

    @Schema(description = "用户id", required = true)
    private String xmid;

    @Schema(description = "商户id", required = true)
    private String shopId;

    @Schema(description = "终端")
    private String shortKey;

    @Schema(description = "分店")
    private String device;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "班结类型")
    private String dutyType;
}
