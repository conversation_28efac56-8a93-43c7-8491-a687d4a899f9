package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-27 15:13:09
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchDutyMessageRespVo {

    @Schema(description = "班结号")
    private String dutyId;

    @Schema(description = "终端号")
    private String shortKey;

    @Schema(description = "分店")
    private String device;

    @Schema(description = "总订单量")
    private int totalNum;

    @Schema(description = "总金额")
    private Double totalMoney;

    @Schema(description = "退款订单量")
    private int totalRefundNum;

    @Schema(description = "退款金额")
    private Double totalRefundMoney;

    @Schema(description = "身份")
    private String isBoss;

    @Schema(description = "身份str")
    private String dutyType;

    @Schema(description = "用户id")
    private String xmid;

    @Schema(description = "班级人姓名")
    private String name;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "终端名称")
    private String shortKeyName;

    @Schema(description = "结束时间")
    private String endTime;
}
