package com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo;

import lombok.Data;

@Data
public class SearchDutyReqVo {
   private String shortKeyName;
   private String uniMoney;
   private String fastNum;
   private String deviceId;
   private String memMoney;
   private String uniNum;
   private String wxRefundNum;
   private String fastRefundMoney;
   private String dcpayNum;
   private String aliMoney;
   private String shortKey;
   private String totalRefundNum;
   private String startTime;
   private String fastRefundNum;
   private String wxRefundMoney;
   private String aliNum;
   private String wxNum;
   private String fastMoney;
   private String wxMoney;
   private String dcpayRefundNum;
   private String dcpayMoney;
   private String totalMoney;
   private String uniRefundNum;
   private String uniRefundMoney;
   private String aliRefundMoney;
   private String memNum;
   private String money;
   private String totalNum;
   private String totalRefundMoney;
   private String dcpayRefundMoney;
   private String endTime;
   private String aliRefundNum;
   private String device;
}
