package com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend;

import com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend.vo.ProfitShareOpenStatusReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend.vo.ProfitShareOpenStatusRespVo;
import com.cmpay.code.cmpaymodulepc.service.merchantextend.MerchantExtendService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "管理后台 - 商户扩展")
@RestController
@RequestMapping("/merchant/agent-merchant-extend")
@Validated
public class MerchantExtendController {

    @Resource
    private MerchantExtendService merchantExtendService;

    @PostMapping("/page")
    @Operation(summary = "分账管理——分账商户管理——分页查询")
    public CommonResult<PageResult<ProfitShareOpenStatusRespVo>> searchProfitShareOpenStatus(@RequestBody ProfitShareOpenStatusReqVo profitShareOpenStatusReqVo) {
        return merchantExtendService.searchProfitShareOpenStatus(profitShareOpenStatusReqVo);
    }

    @GetMapping("/saveMerchantProfitShare")
    @Operation(summary = "分账管理——分账商户管理——开通分账")
    public CommonResult<String> saveMerchantProfitShare(@RequestParam("shopId") String shopId,@RequestParam("status") String status) {
        return merchantExtendService.saveMerchantProfitShare(shopId, status);
    }

    @GetMapping("/update-merchant-wx-alipay-Status")
    @Operation(summary = "问题处理——修改星移计划")
    public CommonResult<String> updateMerchantWxAlipayStatus(@RequestParam("shopId") String shopId,@RequestParam("status") String status) {
        return CommonResult.success(merchantExtendService.updateMerchantWxAlipayStatus(shopId, status));
    }


}
