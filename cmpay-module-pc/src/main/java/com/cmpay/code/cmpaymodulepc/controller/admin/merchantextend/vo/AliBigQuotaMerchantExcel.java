package com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2024/5/24 9:54
 * @version: 1.0
 */
@Data
@ToString
public class AliBigQuotaMerchantExcel {

    @Schema(description = "政策名称")
    private String title;

    @Schema(description = "长商户号")
    private String shopId;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "支付宝认证状态")
    private String aliAuthorizeState;

    @Schema(description = "支付宝商户号")
    private String merchantOther4;

    @Schema(description = "通道")
    private String channel;
}
