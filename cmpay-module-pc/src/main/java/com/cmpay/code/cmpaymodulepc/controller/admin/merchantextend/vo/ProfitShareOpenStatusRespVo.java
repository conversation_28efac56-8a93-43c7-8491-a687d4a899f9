package com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-10-24 17:04:50
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfitShareOpenStatusRespVo {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "商户名")
    private String shopNickname;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "开通状态")
    private String status;

    @Schema(description = "开通状态字符串")
    private String statusStr;
}
