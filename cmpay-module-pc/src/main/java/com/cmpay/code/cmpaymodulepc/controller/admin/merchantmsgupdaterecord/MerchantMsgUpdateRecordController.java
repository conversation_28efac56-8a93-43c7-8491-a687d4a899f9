package com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord;

import com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo.*;
import com.cmpay.code.cmpaymodulepc.service.merchantmsgupdaterecord.MerchantMsgUpdateRecordService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 费率修改记录")
@RestController
@RequestMapping("/merchant/msg/update-record")
@Validated
public class MerchantMsgUpdateRecordController {

    @Resource
    private MerchantMsgUpdateRecordService msgUpdateRecordService;

    @PostMapping("/upload")
    @Operation(summary = "修改结算卡——上传图片")
    @OperateLog(enable = false)
    //@PreAuthorize("@ss.hasPermission('class-conclusion:query')")
    //修改结算卡——上传图片
    public CommonResult<String> uploadFile(UploadFileReqVo uploadFileReqVo) {
        return msgUpdateRecordService.uploadFile(uploadFileReqVo);
    }
    @Operation(summary = "结算卡详情")
    @GetMapping("/get")
    @OperateLog(enable = false)
    public CommonResult<UpdateCardAllVO> getMerchantUpdateCardMsg(String shopId){

        return success(msgUpdateRecordService.getMerchantUpdateCardMsg(shopId));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateMerchantCardTm(@RequestBody MerchantUpdateCardUpdateVO merchantUpdateCardUpdateVO){
        msgUpdateRecordService.updateMerchantCardTm(merchantUpdateCardUpdateVO);
        return success(true);
    }


    @PostMapping("/searchSettlementBank")
    @Operation(summary = "修改结算卡——搜索结算银行")
    //@PreAuthorize("@ss.hasPermission('class-conclusion:query')")
    public CommonResult<List<String>> searchSettlementBank(@RequestBody SettlementBankReqVo settlementBankReqVo) {
        return msgUpdateRecordService.searchSettlementBank(settlementBankReqVo);
    }

    @PostMapping("/searchSuggestBank")
    @Operation(summary = "修改结算卡——搜索开户银行")
    //@PreAuthorize("@ss.hasPermission('class-conclusion:query')")
    public CommonResult<List<SuggestBankMessageRqspVo>> searchSuggestBank(@RequestBody SuggestBankMessageReqVo suggestBankMessageReqVo) {
        return msgUpdateRecordService.searchSuggestBank(suggestBankMessageReqVo);
    }
}
