package com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MerchantUpdateCardRecordChannelVO {
    @Schema(description = "修改状态")
    private String updateStatus;
    @Schema(description = "修改是否成功信息")
    private String updateErrorMsg;
    @Schema(description = "修改时间")
    private String updateTime;
    @Schema(description = "结算卡图片")
    private String cardImage;
    @Schema(description = "订单id")
    private String orderId;
    @Schema(description = "银行卡号")
    private String currentCard;
    @Schema(description = "结算人")
    private String cardName;
    @Schema(description = "结算银行")
    private String bankName;
    @Schema(description = "银行联号")
    private String bankAddNo;
    @Schema(description = "开户行名称")
    private String branchName;
    @Schema(description = "身份证号")
    private String identity;
    @Schema(description = "结算人手机号")
    private String cardPhone;
    @Schema(description = "省")
    private String province;
    @Schema(description = "市")
    private String city;
    @Schema(description = "结算卡类型")
    private String cardType;
    @Schema(description = "idCardType")
    private String idCardType;
    @Schema(description = "生效时间")
    private String effectTime;
    @Schema(description = "注册时间")
    private String registerTime;

}
