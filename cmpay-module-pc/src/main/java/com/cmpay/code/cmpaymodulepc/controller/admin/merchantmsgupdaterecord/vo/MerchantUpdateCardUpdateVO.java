package com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MerchantUpdateCardUpdateVO {
    @NotNull(message = "商户号不能为空")
    @Schema(description = "商户号")
    private String shopId;
    @Schema(description = "结算银行")
    private String bankName;
    @Schema(description = "开户行名称")
    private String branchName;
    @NotNull(message = "银行联号不能为空")
    @Schema(description = "银行联号")
    private String bankAddNo;
    @Schema(description = "结算人手机号")
    private String cardPhone;
    @NotNull(message = "银行卡号不能为空")
    @Schema(description = "银行卡号")
    private String card;
    @Schema(description = "微信支付通道")
    private String payType;
    @NotNull(message = "银行卡照片不能为空")
    @Schema(description = "图片路径")
    private String cardImage;
    @Schema(description = "结算人")
    private String cardName;
    @Schema(description = "身份证号")
    private String identity;
}
