package com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-07 15:34:00
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SuggestBankMessageReqVo {

    @Schema(description = "结算银行")
    private String settlementBankName;

    @Schema(description = "开户银行")
    private String suggestBankName;
}
