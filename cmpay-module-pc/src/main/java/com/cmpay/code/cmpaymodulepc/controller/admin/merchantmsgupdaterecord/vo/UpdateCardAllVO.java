package com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo;

import com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.MerchantDebitCardRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UpdateCardAllVO {

    @Schema(description = "修改的信息")
    private MerchantUpdateCardRecordChannelVO merchantUpdateCardRecordChannelVO;
    @Schema(description = "原信息")
    private MerchantDebitCardRespVO merchantDebitCardRespVO;
}
