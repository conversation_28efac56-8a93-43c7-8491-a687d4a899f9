package com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-07-06 11:49:58
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UploadFileReqVo {

    @Schema(description = "文件")
    private MultipartFile file;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "区分路径")
    private String bucket;

    @Schema(description = "区分类型")
    private String type;
}
