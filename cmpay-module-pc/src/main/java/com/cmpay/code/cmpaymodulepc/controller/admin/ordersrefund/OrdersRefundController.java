package com.cmpay.code.cmpaymodulepc.controller.admin.ordersrefund;

import com.cmpay.code.cmpaymodulepc.service.ordersrefund.OrdersRefundService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Tag(name = "管理后台 - 退款订单")
@RestController
@RequestMapping("/ordersrefund/orders-refund")
@Validated
public class OrdersRefundController {

    @Resource
    private OrdersRefundService ordersRefundService;



}
