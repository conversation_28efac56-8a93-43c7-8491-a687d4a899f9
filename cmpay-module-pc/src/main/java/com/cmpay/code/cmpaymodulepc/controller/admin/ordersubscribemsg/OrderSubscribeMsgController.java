package com.cmpay.code.cmpaymodulepc.controller.admin.ordersubscribemsg;

import com.cmpay.code.cmpaymodulepc.controller.admin.ordersubscribemsg.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.ordersubscribemsg.OrderSubscribeMsgConvert;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.ordersubscribemsg.OrderSubscribeMsgDO;
import com.cmpay.code.cmpaymodulepc.service.ordersubscribemsg.OrderSubscribeMsgService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 商户支付绑定播报人信息")
@RestController
@RequestMapping("/ordersubscribemsg/order-subscribe-msg")
@Validated
public class OrderSubscribeMsgController {

    @Resource
    private OrderSubscribeMsgService orderSubscribeMsgService;

    @PostMapping("/create")
    @Operation(summary = "创建商户支付绑定播报人信息")
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:create')")
    public CommonResult<Integer> createOrderSubscribeMsg(@Valid @RequestBody OrderSubscribeMsgCreateReqVO createReqVO) {
        return success(orderSubscribeMsgService.createOrderSubscribeMsg(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商户支付绑定播报人信息")
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:update')")
    public CommonResult<Boolean> updateOrderSubscribeMsg(@Valid @RequestBody OrderSubscribeMsgUpdateReqVO updateReqVO) {
        orderSubscribeMsgService.updateOrderSubscribeMsg(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商户支付绑定播报人信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:delete')")
    public CommonResult<Boolean> deleteOrderSubscribeMsg(@RequestParam("id") Integer id) {
        orderSubscribeMsgService.deleteOrderSubscribeMsg(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商户支付绑定播报人信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:query')")
    public CommonResult<OrderSubscribeMsgRespVO> getOrderSubscribeMsg(@RequestParam("id") Integer id) {
        OrderSubscribeMsgDO orderSubscribeMsg = orderSubscribeMsgService.getOrderSubscribeMsg(id);
        return success(OrderSubscribeMsgConvert.INSTANCE.convert(orderSubscribeMsg));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商户支付绑定播报人信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:query')")
    public CommonResult<List<OrderSubscribeMsgRespVO>> getOrderSubscribeMsgList(@RequestParam("ids") Collection<Integer> ids) {
        List<OrderSubscribeMsgDO> list = orderSubscribeMsgService.getOrderSubscribeMsgList(ids);
        return success(OrderSubscribeMsgConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商户支付绑定播报人信息分页")
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:query')")
    public CommonResult<PageResult<OrderSubscribeMsgRespVO>> getOrderSubscribeMsgPage(@Valid OrderSubscribeMsgPageReqVO pageVO) {
        PageResult<OrderSubscribeMsgDO> pageResult = orderSubscribeMsgService.getOrderSubscribeMsgPage(pageVO);
        return success(OrderSubscribeMsgConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商户支付绑定播报人信息 Excel")
    @PreAuthorize("@ss.hasPermission('ordersubscribemsg:order-subscribe-msg:export')")
    @OperateLog(type = EXPORT)
    public void exportOrderSubscribeMsgExcel(@Valid OrderSubscribeMsgExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<OrderSubscribeMsgDO> list = orderSubscribeMsgService.getOrderSubscribeMsgList(exportReqVO);
        // 导出 Excel
        List<OrderSubscribeMsgExcelVO> datas = OrderSubscribeMsgConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商户支付绑定播报人信息.xls", "数据", OrderSubscribeMsgExcelVO.class, datas);
    }

}
