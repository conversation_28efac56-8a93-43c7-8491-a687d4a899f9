package com.cmpay.code.cmpaymodulepc.controller.admin.ordersubscribemsg.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 商户支付绑定播报人信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class OrderSubscribeMsgBaseVO {

    @Schema(description = "商户id", required = true, example = "14751")
    @NotNull(message = "商户id不能为空")
    private String shopId;

    @Schema(description = "用户id", required = true, example = "3188")
    @NotNull(message = "用户id不能为空")
    private String xmid;

    @Schema(description = "1：老板；2：店长；3：店员，角色", required = true)
    @NotNull(message = "1：老板；2：店长；3：店员，角色不能为空")
    private Integer isBoss;

    @Schema(description = "小程序appid", required = true, example = "21717")
    @NotNull(message = "小程序appid不能为空")
    private String appid;

    @Schema(description = "同一个微信在不同小程序下的id", required = true, example = "3716")
    @NotNull(message = "同一个微信在不同小程序下的id不能为空")
    private String openid;

    @Schema(description = "0：关闭；1：开启，是否语音播报", required = true)
    @NotNull(message = "0：关闭；1：开启，是否语音播报不能为空")
    private Integer isOpend;

    @Schema(description = "0：未订阅；1：已订阅，是否认证", required = true)
    @NotNull(message = "0：未订阅；1：已订阅，是否认证不能为空")
    private Integer isSubscribe;

    @Schema(description = "模板id", example = "30016")
    private String templateId;

    @Schema(description = "创建时间", required = true)
    @NotNull(message = "创建时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime insertTime;

    @Schema(description = "用户名", example = "李四")
    private String nickname;

    @Schema(description = "头像地址", example = "https://www.iocoder.cn")
    private String headimgurl;

}
