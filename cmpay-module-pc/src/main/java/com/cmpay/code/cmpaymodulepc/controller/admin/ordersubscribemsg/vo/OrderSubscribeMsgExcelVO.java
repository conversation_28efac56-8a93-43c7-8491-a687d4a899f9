package com.cmpay.code.cmpaymodulepc.controller.admin.ordersubscribemsg.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商户支付绑定播报人信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class OrderSubscribeMsgExcelVO {

    @ExcelProperty("id")
    private Integer id;

    @ExcelProperty("商户id")
    private String shopId;

    @ExcelProperty("用户id")
    private String xmid;

    @ExcelProperty("1：老板；2：店长；3：店员，角色")
    private Integer isBoss;

    @ExcelProperty("小程序appid")
    private String appid;

    @ExcelProperty("同一个微信在不同小程序下的id")
    private String openid;

    @ExcelProperty("0：关闭；1：开启，是否语音播报")
    private Integer isOpend;

    @ExcelProperty("0：未订阅；1：已订阅，是否认证")
    private Integer isSubscribe;

    @ExcelProperty("模板id")
    private String templateId;

    @ExcelProperty("创建时间")
    private LocalDateTime insertTime;

    @ExcelProperty("用户名")
    private String nickname;

    @ExcelProperty("头像地址")
    private String headimgurl;

}
