package com.cmpay.code.cmpaymodulepc.controller.admin.ordersubscribemsg.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商户支付绑定播报人信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderSubscribeMsgPageReqVO extends PageParam {

    @Schema(description = "商户id", example = "14751")
    private String shopId;

    @Schema(description = "用户id", example = "3188")
    private String xmid;

    @Schema(description = "1：老板；2：店长；3：店员，角色")
    private Integer isBoss;

    @Schema(description = "小程序appid", example = "21717")
    private String appid;

    @Schema(description = "同一个微信在不同小程序下的id", example = "3716")
    private String openid;

    @Schema(description = "0：关闭；1：开启，是否语音播报")
    private Integer isOpend;

    @Schema(description = "0：未订阅；1：已订阅，是否认证")
    private Integer isSubscribe;

    @Schema(description = "模板id", example = "30016")
    private String templateId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] insertTime;

    @Schema(description = "用户名", example = "李四")
    private String nickname;

    @Schema(description = "头像地址", example = "https://www.iocoder.cn")
    private String headimgurl;

}
