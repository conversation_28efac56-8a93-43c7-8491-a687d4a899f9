package com.cmpay.code.cmpaymodulepc.controller.admin.ordersubscribemsg.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商户支付绑定播报人信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderSubscribeMsgUpdateReqVO extends OrderSubscribeMsgBaseVO {

    @Schema(description = "id", required = true, example = "23496")
    @NotNull(message = "id不能为空")
    private Integer id;

}
