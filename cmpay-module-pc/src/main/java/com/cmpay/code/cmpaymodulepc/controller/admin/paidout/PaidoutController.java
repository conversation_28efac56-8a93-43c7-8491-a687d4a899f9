package com.cmpay.code.cmpaymodulepc.controller.admin.paidout;

import com.cmpay.code.cmpaymodulepc.controller.admin.paidout.vo.SettlementDetailVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.paidout.vo.SettlementReqVo;
import com.cmpay.code.cmpaymodulepc.service.paidout.PaidoutService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@Tag(name = "管理后台 - 结算")
@RestController
@RequestMapping("/paidout/")
@Validated
public class PaidoutController {

    @Resource
    private PaidoutService paidoutService;

//    @PermitAll
    @PostMapping("/settlement")
    @Operation(summary = "商户交易信息——查询结算记录")
    //@PreAuthorize("@ss.hasPermission('charge:pay-record:query')")
    public CommonResult<PageResult<SettlementDetailVo>> settlement(@RequestBody SettlementReqVo settlementReqVo) {
        return paidoutService.settlement(settlementReqVo);
    }

    //    @PermitAll
    @PostMapping("/exportPaidoutExcel")
    @Operation(summary = "商户交易信息——导出结算记录Excel")
    //@PreAuthorize("@ss.hasPermission('charge:pay-record:export')")
    public void exportPaidoutExcel(@RequestBody SettlementReqVo settlementReqVo, HttpServletResponse response) {
        paidoutService.getPaidoutList(settlementReqVo, response);
    }

}
