package com.cmpay.code.cmpaymodulepc.controller.admin.paidout.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 结算明细
 * @date 2023-04-25 10:46:55
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettlementDetailVo {

    @Schema(description = "结算时间")
    private String paymentTime;

    @Schema(description = "消费日期")
    private String StartTime;

    @JsonIgnore
    @Schema(description = "结算方式")
    private String payType;

    @Schema(description = "结算方式")
    private String payTypeStr;

    @JsonIgnore
    @Schema(description = "结算状态")
    private String paymentStatus;

    @Schema(description = "结算状态")
    private String paymentStatusStr;

    @Schema(description = "手续费合计")
    private Double PaidOutCommission = 0.0;

    @Schema(description = "结算金额合计")
    private Double PaidOutMoney = 0.0;

    @Schema(description = "微信手续费")
    private Double wxCommission = 0.0;

    @Schema(description = "微信金额")
    private Double wxPaidOut = 0.0;

    @Schema(description = "支付宝手续费")
    private Double aliCommission = 0.0;

    @Schema(description = "支付宝金额")
    private Double aliPaidOut = 0.0;

    @Schema(description = "云闪付手续费")
    private Double unionPayCommission = 0.0;

    @Schema(description = "云闪付金额")
    private Double unionPayPaidOut = 0.0;

    @Schema(description = "数币手续费")
    private Double dcpayCommission = 0.0;

    @Schema(description = "数币金额")
    private Double dcpayPaidOut = 0.0;

    @Schema(description = "补贴金额")
    private Double subsidyPaidOut = 0.0;

    @Schema(description = "补贴手续费")
    private Double subsidyCommission = 0.0;

    @Schema(description = "银行转账金额")
    private Double fastPaidOut = 0.0;

    @Schema(description = "银行转账手续费")
    private Double fastCommission = 0.0;

    @Schema(description = "银行卡手续费")
    private Double bankCommission = 0.0;

    @Schema(description = "银行卡金额")
    private Double bankPaidOut = 0.0;

    @JsonIgnore
    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "备注信息")
    private String paymentFailMsg;

    @Schema(description = "结束日期")
    private String endTime;

    @Schema(description = "通道")
    private String channel;
}
