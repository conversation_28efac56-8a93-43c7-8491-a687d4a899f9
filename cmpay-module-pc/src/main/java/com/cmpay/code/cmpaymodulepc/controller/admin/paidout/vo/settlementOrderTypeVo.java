package com.cmpay.code.cmpaymodulepc.controller.admin.paidout.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-25 16:42:45
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class settlementOrderTypeVo {

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "金额")
    private Double paidOutMoney;

    @Schema(description = "手续费")
    private Double commission;

    @Schema(description = "结算时间")
    private String paymentTime;

    @Schema(description = "结算通道")
    private String payType;
}
