package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.channel.ChannelConvert;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO;
import com.cmpay.code.cmpaymodulepc.service.partnerchannel.PartnerChannelService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 渠道信息")
@RestController
@RequestMapping("/partner/channel")
@Validated
public class PartnerChannelController {

    @Resource
    private PartnerChannelService partnerChannelService;

    /**
     * 初始化市级公司
     *
     * @param param 接收省级id
     * @return
     */
    @PostMapping("/initPartnerChannel")
    @Operation(summary = "初始化市级公司")
    public CommonResult<List<InitMenuListRespVo>> initPartnerChannel(@RequestBody InitChannelReqVo initChannelReqVo) {
        return partnerChannelService.initPartnerChannel(initChannelReqVo);
    }


    @GetMapping("/simple-list")
    @Operation(summary = "获得渠道信息下拉")
//    @PreAuthorize("@ss.hasPermission('partner:channel:query')")
    @PermitAll
    public CommonResult<List<ChannelSimpleRespVO>> getChannelSimpleList() {
        List<PartnerChannelDO> channelList = partnerChannelService.getChannelList();
        return success(ChannelConvert.INSTANCE.convertSimple(channelList));
    }

    @PostMapping("/getBranchAndPartnerId")
    @Operation(summary = "查询区县/网点归属省市")
    // @PermitAll
    public CommonResult<CompanyAffiliationRespVo> getBranchAndPartnerId(@RequestBody CompanyAffiliationReqVo companyAffiliationVo) {
        CompanyAffiliationRespVo companyAffiliation = partnerChannelService.getCompanyAffiliation(companyAffiliationVo);
        return success(companyAffiliation);
    }

    @GetMapping("/page")
    @Operation(summary = "市级分公司列表")
    public CommonResult<PageResult<ChannelRespVO>> getPage(@Valid ChannelPageReqVO reqVO) {
        return success(partnerChannelService.getPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "市级分公司添加")
    public CommonResult<JSONObject> create(@RequestBody ChannelCreateReqVO channelCreateReqVO){
        return success(partnerChannelService.create(channelCreateReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "修改市级分公司")
    public CommonResult<JSONObject> update(@RequestBody ChannelCreateReqVO channelUpdateReqVO)  {
        return success(partnerChannelService.update(channelUpdateReqVO));
    }

    @PostMapping("/update-company")
    @Operation(summary = "修改市级分公司")
    public CommonResult updateCom(@RequestBody ChannelCompayCreateVo channelCompayCreateVo)  {
        return partnerChannelService.updateCom(channelCompayCreateVo);
    }

    @PostMapping("/create-company")
    @Operation(summary = "创建市级分公司")
    public CommonResult createCom(@RequestBody ChannelCompayCreateVo channelCompayCreateVo)  {
        return partnerChannelService.createPartnerChannel(channelCompayCreateVo);
    }

    /**
     * 大渠道地区管理查询
     */
    @GetMapping("/getPartnerAreaManage")
    @Operation(summary = "大渠道地区管理查询")
    // @PermitAll
    public CommonResult<PageResult<GetPartnerAreaManageRespVO>> getPartnerAreaManage(@Valid GetPartnerAreaManageReqVO getPartnerAreaManageReqVO) {
        PageResult<GetPartnerAreaManageRespVO> partnerAreaManage = partnerChannelService.getPartnerAreaManage(getPartnerAreaManageReqVO);
        return success(partnerAreaManage);
    }

    @GetMapping("/getPartnerChannel")
    @Operation(summary = "市通道详细")
    public CommonResult<ChannelReqVo> getPartnerChannel(@RequestParam String partnerId){
        return success(partnerChannelService.selectByPartnerId(partnerId));
    }

    @GetMapping("/getRate")
    @Operation(summary = "市通道保底费率")
    public CommonResult<RateChannelVo> getRateChannel(@RequestParam String partnerId){
        return success(partnerChannelService.getRateByPartnerId(partnerId));
    }

}
