package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Title: ChannelRespDTO
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.dto
 * @Date 2025/3/21 16:27
 * @description: 市级公司响应对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelRespDTO {

    /**
     * 大渠道号
     */
    private String partnerId;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 负责人姓名
     */
    private String keeper;

    /**
     * 和融通保底
     */
    private BigDecimal yirongmaRate;

    /**
     * 易融码业务员号
     */
    private String yirongmaUserId;

    /**
     * 随行付保底
     */
    private BigDecimal suixingfuRate;

    /**
     * 随行付业务员号
     */
    private String suixingfuUserId;

    /**
     * 乐刷保底
     */
    private BigDecimal leshuaRate;

    /**
     * 审核状态：0：渠道自审；1：平台帮审
     */
    private Integer auditType;

    /**
     * 佣金结算户名
     */
    private String cardName;

    /**
     * 佣金结算卡号
     */
    private String cardNo;

    /**
     * 佣金结算卡开户行
     */
    private String bankAddress;

    /**
     * 添加时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date insertTime;

    private String keeperphone;

    private String phone;
    /**
     * 易融码300以下费率
     */
    @TableField(value = "rate_yirongma_300down")
    private BigDecimal rateYirongma300down;
    /**
     * 易融码300以上费率
     */
    @TableField(value = "rate_yirongma_300up")
    private BigDecimal rateYirongma300up;

    private BigDecimal rateFufeitong;

    private BigDecimal rateShoubei;

    private String address;

    /**
     * 佣金发票类型
     */
    private String invoiceType;

    private String taxpayerNum;

    /**
     * 佣金发票内容
     */
    private String invoiceProgrem;

    /**
     * 结算方式：1，快银结算；0，都可以。
     */
    private Integer isKyaccount;

    /**
     * 佣金发票公司名称
     */
    private String invoiceName;

    /**
     * 佣金发票电话
     */
    private String invoicePhone;

    /**
     * 佣金发票银行
     */
    private String invoiceBank;

    /**
     * 佣金发票法人
     */
    private String invoiceAccount;

    /**
     * 佣金发票银行地址
     */
    private String invoiceBanknum;

    /**
     * 是否SAAS
     */
    private Integer partnerAgent;

    /**
     * SAAS公众号appid
     */
    private String merchantAppid;

    /**
     * SAAS公众号秘钥
     */
    private String merchantAppidse;

    private String merchantAuditTempId;

    private String merchantPayoverTempId;

    private String merchantPaidoutTempId;

    private String activityPerstoreBuyTempId;

    private String brand;

    private String brandLogo;

    private String gzhImg;

    private BigDecimal shoubeiRate;

    private String yrmOrgId;

    private String yrmOrgKey;

    /**
     * 0:禁止（代理商的受理商）；1；允许。
     */
    private Integer isAccept;

    private Integer isIndividualAccount;

    private String sxfOrgId;

    private String sxfOrgKey;

    private String sxfSweepAppid;

    private String sxfSweepAppidKey;

    /**
     * 0:渠道权限不显示；1显示渠道权限
     */
    private Integer isPayoffAuthority;

    private BigDecimal rateWxMicro;

    private String wxMicroNumber;

    private BigDecimal rateJialian;

    /**
     * 0:否；1：是。
     */
    private Integer isWxmicro;

    private BigDecimal rateCloudpay;

    private String yirongmaBrand;

    /**
     * 微信买单保底50以下费率
     */
    @TableField(value = "rate_cloudpay_50down")
    private BigDecimal rateCloudpay50down;

    /**
     * 传化机构号
     */
    private String chOrgId;

    /**
     * 传化机构秘钥
     */
    private String chOrgKey;

    private BigDecimal rateChuanhua;

    private BigDecimal rateHuifu;

    /**
     * 乐刷机构号
     */
    private String lsOrgId;

    /**
     * 乐刷机构秘钥
     */
    private String lsOrgKey;

    /**
     * 乐刷机构公众号
     */
    private String lsSweepAppid;

    /**
     * 乐刷机构公众号秘钥
     */
    private String lsSweepAppidKey;

    private Integer isCloudpayMerchant;

    /**
     * 汇付机构号
     */
    private String hfOrgId;

    /**
     * 汇付机构秘钥路径
     */
    private String hfOrgKeyPath;

    /**
     * 汇付机构秘钥密码
     */
    private String hfOrgKeyPassword;

    private String hfOrgOptellerid;

    private String hfSweepAppid;

    private String hfSweepAppidKey;

    private BigDecimal rateDuolabao;

    private BigDecimal rateSub;

    private BigDecimal rateSubalipay;

    private BigDecimal rateXinhui;

    private BigDecimal rateLiandong;

    /**
     * 是否允许审核政策；0:否；1：是。
     */
    private Integer isAdmitPolicy;

    private String hkOrgId;

    private String wxmaidanSubscribeAppid;

    private String hkOrgKey;

    private String hkOrgAccessid;

    private String hkPassKey;

    private String hkSweepAppid;

    private String hkSweepAppidKey;

    private String hkWxChannelNo;

    private String hkAliChannelNo;

    private BigDecimal rateRuiyinxin;

    private BigDecimal rateHaike;

    private BigDecimal rateCcb;

    private String ryxOrgId;

    private String ryxPrivateKeyPath;

    private String ryxPublicKeyPath;

    private String ryxCooperator;

    /**
     * 分公司/省公司id
     */
    private Integer branchOfficeId;

    private String ryxSweepAppid;

    private String ryxSweepAppidKey;

    private String ryxWxChannelId;

    private String ryxAccessId;

    private String ryxSigninPrivateKeyPath;

    private String ryxSigninPublicKeyPath;

    private BigDecimal rateSubunionpay;

    private String customerUid;

    private String modelUid;

    private String ryxAliChannelId;

    private String ryxPaidoutType;

    /**
     * 是否邮政
     */
    private Integer isYouzheng;

    private String wftChannel;

    private String wftOrgId;

    private String wftOrgKey;

    private String wftWxChannelNo;

    /**
     * 徽商支付宝渠道号
     */
    @TableField(value = "wft_ali_channel_no1")
    private String wftAliChannelNo;

    private String wftAppid;

    private String wftAppidKey;

    private BigDecimal rateWft;

    /**
     * 是否允许重置商户通道
     */
    private Integer isCanResetMerchant;

    private String merchantPaidoutFailTempId;

    private String merchantRiskTempId;

    private BigDecimal rateSjb;

    private BigDecimal rateWftHs;

    private BigDecimal rateFubei;

    private BigDecimal ratePufabank;

    private BigDecimal rateSxfTq;

    private BigDecimal ratePabank;

    /**
     * 是否
     */
    private Integer isDirect;

    private BigDecimal rateTonglian;

    private String fubeiOrgId;

    private String fubeiSignKey;

    private String fubeiAppid;

    private String fubeiAppidKey;

    private String sxfWxChannelNo;

    private String sxfAliChannelNo;

    private BigDecimal rateZjnx;

    private BigDecimal rateLakala;

    private BigDecimal rateWangkebao;

    /**
     * 是否显示商户投诉；0:否；1：是
     */
    private Integer isMerchantRisk;

    /**
     * 是否显示分店管理；0:否；1：是
     */
    private Integer isMerchantDevice;

    private BigDecimal rateLakalaMis;

    private BigDecimal rateZhifu;

    private String zhifuOrgId;

    private String zhifuOrgKey;

    /**
     * 是否向上游报备；1：是；0：是
     */
    private Integer isBaobeiUp;

    private String zhifuWxMch;

    private String zhifuAliMch;

    private String zhifuAppid;

    private String zhifuAppKey;

    /**
     * 是否允许萤火虫插件；0:否；1：是
     */
    private Integer isYhcOut;

    /**
     * 外插模板id
     */
    private String outsideModelUid;

    private String wkbOrgNo;

    private String wkbSignKey;

    private String wkbWxChannelId;

    private String wkbAliChannelId;

    private String wkbGatewayUrl;

    private String wkbAppid;

    private String wkbAppidSecert;

    private String xinhuiOrgNo;

    private String xinhuiSignKey;

    private String xinhuiWxChannelId;

    private String xinhuiAliChannelId;

    private String xinhuiGatewayUrl;

    private String xinhuiAppid;

    private String xinhuiAppidSecert;

    /**
     * 是否限制地域；0:否；1;是。
     */
    private Integer isLimitArea;

    private BigDecimal rateXiandai;

    private String xiandaiOrgId;

    private String xiandaiSignKey;

    private String xiandaiPublicKey;

    private String xiandaiPwd;

    private String xiandaiAppid;

    private String xiandaiAppidSecret;

    private String xiandaiWxMchid;

    private String xiandaiAliMchid;

    private String xiandaiMerOrgId;

    private String xiandaiCreateUserId;

    /**
     * 是否允许确认开通；0：否；1：是
     */
    private Integer isCanConfirmMerchant;

    private String sjbOrgId;

    private String sjbChannelMchId;

    private String sjbAliChannelMchId;

    private String sjbSignKey;

    /**
     * 插件V5的模板id
     */
    private String yhcV5ModelUid;

    /**
     * 是否允许确认开通；0:否；1;是。
     */
    private Integer isConfirmMerchantQs;

    /**
     * 邮政机构id
     */
    private String yzOrgId;

    private String yzOrgAccount;

    /**
     * 是否显示商户补贴，0:否；1;是。
     */
    private Integer isShowRedpackage;

    /**
     * 是否参与统计；1：参与；0：不参与
     */
    private Integer isStatistics;

    private Date updateTime;

    /**
     * 是否允许政策审核：1允许；0不允许
     */
    private Integer isPolicyUpdate;

    private BigDecimal rateDianxin;

    /**
     * 邮政数币通道费率
     */
    private BigDecimal rateYouzheng;

    private BigDecimal rateYzPay;

    /**
     * 瑞银信（H）机构号
     */
    private String huluOrgId;

    private BigDecimal rateYlsw;

    /**
     * 招商银行保底费率
     */
    private BigDecimal rateCmb;

    /**
     * 瑞银信（H）平台公钥
     */
    private String huluPlatformPublic;

    /**
     * 瑞银信（H）服务商私钥
     */
    private String huluPrivate;

    /**
     * 瑞银信（H）微信渠道号
     */
    private String huluWxChannelId;

    /**
     * 瑞银信（H）支付宝PID
     */
    private String huluAliChannelId;

    /**
     * 瑞银信（H）公众号appid
     */
    private String huluAppid;

    private String huluAppidse;

    private BigDecimal rateHulu;

    /**
     * 葫芦提现次数
     */
    @TableField(value = "hulu_nd1_num")
    private Integer huluNd1Num;

    @TableField(value = "hulu_nd1_cap_commission")
    private Integer huluNd1CapCommission;

    /**
     * 葫芦提现规则；单费率和阶梯模式，参看文档，默认0费率，地市补
     */
    @TableField(value = "hulu_nd1_rule")
    private String huluNd1Rule;

    /**
     * 葫芦提现单笔限额，0为不限额
     */
    @TableField(value = "hulu_nd1_quota")
    private Integer huluNd1Quota;

    /**
     *瑞银信(H)活动号
     */
    @TableField(value = "hulu_nd1_activity_id")
    private String huluNd1ActivityId;

    /**
     * 城市默认坐标
     */
    private String lnglat;

    /**
     * 市级机构排序，自定义
     */
    private Integer sortId;

    /**
     * 易票联机构号
     */
    private String yplOrgId;

    /**
     * 易票联平台公钥
     */
    private String yplPlatformPublic;

    /**
     * 商户私钥（机构共用一套）
     */
    private String yplPrivate;

    /**
     * 商户私钥密码
     */
    private String yplPrivatePwd;

    /**
     * 签名序列号
     */
    private String yplSignNo;

    private BigDecimal rateYpl;

    // 是否支持备用通道；0不支持；1支持；
    private Integer isStandbyChannel;

    //客户经理可以设置的最大补贴金额
    private BigDecimal ywyMaxSubsidyMoney;

    //是否支持走访任务功能；0不支持；1支持
    private String isVisitTask;

    //目标商户标志，0关闭，1开启
    private String isTargetMer;

    //账户补贴标志，0关闭，1开启
    private String isAccountSubsidy;

    //pc账户补贴标志，0关闭，1开启
    private String pcAccountSubsidy;
}
