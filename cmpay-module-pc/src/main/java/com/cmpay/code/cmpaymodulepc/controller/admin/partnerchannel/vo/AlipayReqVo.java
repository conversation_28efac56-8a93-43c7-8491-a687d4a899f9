package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AlipayReqVo {
    @Schema(description = "支付宝通道")
    private String channel="subalipay";
    @Schema(description = "支付宝机构号", example = "1053")
    private String alipayOrgNo;
    @Schema(description = "支付宝保底费率")
    private BigDecimal alipayRate;
    @Schema(description = "支付宝结算周期", example = "1")
    private String alipayPaidoutType;
}
