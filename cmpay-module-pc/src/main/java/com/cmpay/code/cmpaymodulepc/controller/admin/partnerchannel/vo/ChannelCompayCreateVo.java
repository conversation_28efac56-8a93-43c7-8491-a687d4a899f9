package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class ChannelCompayCreateVo {
    @Schema(description = "公司")
    private String company;
    @Schema(description = "负责人姓名")
    private String keeper;
    @Schema(description = "负责人电话")
    private String keeperphone;
    @Schema(description = "电话")
    private String phone;
    @Schema(description = "所属分公司", example = "16061")
    private Integer branchOfficeId;
    @Schema(description = "渠道名称", example = "渠道名称")
    private String partnerId;

    @Schema(description = "审核方式")
    private Integer auditType;

    @Schema(description = "是否允许营业点")
    private Integer isAccept;

    @Schema(description = "是否允许渠道权限")
    private Integer isPayoffAuthority;

    @Schema(description = "是否允许开户政策管理")
    private Integer isAdmitPolicy;

    @Schema(description = "是否邮政渠道")
    private Integer isYouzheng;

    @Schema(description = "是否允许重置通道")
    private Integer isCanResetMerchant;

    @Schema(description = "是否允许确认开通")
    private Integer isCanConfirmMerchant;

    @Schema(description = "是否显示商户投诉")
    private Integer isMerchantRisk;

    @Schema(description = "是否显示商户补贴")
    private Integer isShowRedpackage;

    @Schema(description = "是否允许：政策审核1允许；0不允许")
    private Integer isPolicyUpdate;

    @Schema(description = "外插是否显示云mis")
    private Integer isYhcOut;

    @Schema(description = "内插模板id", example = "10811")
    private String modelUid;

    @Schema(description = "外插模板id", example = "21488")
    private String outsideModelUid;

    @Schema(description = "内插v5模板id", example = "3063")
    private String yhcV5ModelUid;

    @Schema(description = "瑞银信三方")
    private RyxChannelReqVo ryxChannelReqVo;

    @Schema(description = "瑞银信(H)")
    private HuluReqVo huluReqVo;

    @Schema(description = "随行付")
    private SxfChannelReqVo sxfChannelReqVo;

    @Schema(description = "银联")
    private YlswReqVo ylswReqVo;

    @Schema(description = "国通")
    private GuoTongReqVo guoTongReqVo;

    @Schema(description ="抖音" )
    private DyChannelReqVo dyChannelReqVo;

    @Schema(description = "微企付")
    private WqfReqVp wqfReqVp;

    @Schema(description = "数币直连")
    private YzReqVo yzReqVo;

    @Schema(description = "拉卡拉v3")
    private LaklaV3ReqVo laklaV3ReqVo;

    @Schema(description = "易生")
    private YiShengResqVo yiShengResqVo;

    @Schema(description = "是否支持备用通道；0不支持；1支持；")
    private Integer isStandbyChannel;

    @Schema(description = "客户经理可以设置的最大补贴金额")
    private BigDecimal ywyMaxSubsidyMoney;

    @Schema(description = "是否支持走访任务功能；0不支持；1支持")
    private String isVisitTask;

    @Schema(description = "目标商户标志，0关闭，1开启")
    private String isTargetMer;

    @Schema(description = "账户补贴标志，0关闭，1开启")
    private String isAccountSubsidy;

    @Schema(description = "是否需要数据脱敏:0需要;1:不需要")
    private String isDataDesc;

    @Schema(description = "pc账户补贴标志，0关闭，1开启")
    private String pcAccountSubsidy;
}
