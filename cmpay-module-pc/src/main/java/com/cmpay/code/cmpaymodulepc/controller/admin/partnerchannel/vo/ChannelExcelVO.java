package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 市级分公司渠道 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ChannelExcelVO {

    @ExcelProperty("大渠道号")
    private String partnerId;

    @ExcelProperty("所属公司")
    private String company;

    @ExcelProperty("负责人姓名")
    private String keeper;

    @ExcelProperty("和融通保底")
    private BigDecimal yirongmaRate;

    @ExcelProperty("易融码业务员号")
    private String yirongmaUserId;

    @ExcelProperty("随行付保底")
    private BigDecimal suixingfuRate;

    @ExcelProperty("随行付业务员号")
    private String suixingfuUserId;

    @ExcelProperty("乐刷保底")
    private BigDecimal leshuaRate;

    @ExcelProperty("审核状态：0：渠道自审；1：平台帮审")
    private Integer auditType;

    @ExcelProperty("佣金结算户名")
    private String cardName;

    @ExcelProperty("佣金结算卡号")
    private String cardNo;

    @ExcelProperty("佣金结算卡开户行")
    private String bankAddress;

    @ExcelProperty("添加时间")
    private LocalDateTime insertTime;

    @ExcelProperty("负责人电话")
    private String keeperphone;

    @ExcelProperty("客服电话")
    private String phone;

    @ExcelProperty("易融码300以下费率")
    private BigDecimal rateYirongma300down;

    @ExcelProperty("易融码300以上费率")
    private BigDecimal rateYirongma300up;

    @ExcelProperty("付费通保底费率")
    private BigDecimal rateFufeitong;

    @ExcelProperty("收呗保底费率")
    private BigDecimal rateShoubei;

    @ExcelProperty("详细地址")
    private String address;

    @ExcelProperty("佣金发票类型")
    private String invoiceType;

    @ExcelProperty("纳税人识别号")
    private String taxpayerNum;

    @ExcelProperty("佣金发票内容")
    private String invoiceProgrem;

    @ExcelProperty("结算方式：1，快银结算；0，都可以。")
    private Integer isKyaccount;

    @ExcelProperty("佣金发票公司名称")
    private String invoiceName;

    @ExcelProperty("佣金发票电话")
    private String invoicePhone;

    @ExcelProperty("佣金发票银行")
    private String invoiceBank;

    @ExcelProperty("佣金发票法人")
    private String invoiceAccount;

    @ExcelProperty("佣金发票银行地址")
    private String invoiceBanknum;

    @ExcelProperty("是否SAAS")
    private Integer partnerAgent;

    @ExcelProperty("SAAS公众号appid")
    private String merchantAppid;

    @ExcelProperty("SAAS公众号秘钥")
    private String merchantAppidse;

    @ExcelProperty("SAAS的审核模板id")
    private String merchantAuditTempId;

    @ExcelProperty("SAAS的支付模板id")
    private String merchantPayoverTempId;

    @ExcelProperty("SAAS的结算模板id")
    private String merchantPaidoutTempId;

    @ExcelProperty("SAAS的结算失败模板id")
    private String merchantPaidoutFailTempId;

    @ExcelProperty("SAAS的风控模板id")
    private String merchantRiskTempId;

    @ExcelProperty("SAAS的活动推送模板id")
    private String activityPerstoreBuyTempId;

    @ExcelProperty("SAAS的品牌名称")
    private String brand;

    @ExcelProperty("SAAS的品牌logo")
    private String brandLogo;

    @ExcelProperty("SAAS的公众号关注图片url")
    private String gzhImg;

    @ExcelProperty("收呗保底费率")
    private BigDecimal shoubeiRate;

    @ExcelProperty("和融通机构号")
    private String yrmOrgId;

    @ExcelProperty("和融通机构秘钥")
    private String yrmOrgKey;

    @ExcelProperty("0:禁止（代理商的受理商）；1；允许。")
    private Integer isAccept;

    @ExcelProperty("个人开户： 0，禁止；1，允许")
    private Integer isIndividualAccount;

    @ExcelProperty("随行付机构号")
    private String sxfOrgId;

    @ExcelProperty("随行付机构秘钥")
    private String sxfOrgKey;

    @ExcelProperty("随行付公众号appid")
    private String sxfSweepAppid;

    @ExcelProperty("随行付公众号秘钥")
    private String sxfSweepAppidKey;

    @ExcelProperty("随行付微信渠道号")
    private String sxfWxChannelNo;

    @ExcelProperty("随行付支付宝渠道号")
    private String sxfAliChannelNo;

    @ExcelProperty("付呗机构号")
    private String fubeiOrgId;

    @ExcelProperty("付呗秘钥")
    private String fubeiSignKey;

    @ExcelProperty("付呗appid")
    private String fubeiAppid;

    @ExcelProperty("付呗appidKey")
    private String fubeiAppidKey;

    @ExcelProperty("0:渠道权限不显示；1显示渠道权限")
    private Integer isPayoffAuthority;

    @ExcelProperty("微信小程序保底费率")
    private BigDecimal rateWxMicro;

    @ExcelProperty("微信小程序业务员号")
    private String wxMicroNumber;

    @ExcelProperty("嘉联通道的保底费率")
    private BigDecimal rateJialian;

    @ExcelProperty("0:否；1：是。")
    private Integer isWxmicro;

    @ExcelProperty("微信买单保底费率（腾讯云）")
    private BigDecimal rateCloudpay;

    @ExcelProperty("和融通品牌")
    private String yirongmaBrand;

    @ExcelProperty("微信买单保底50以下费率")
    private BigDecimal rateCloudpay50down;

    @ExcelProperty("传化机构号")
    private String chOrgId;

    @ExcelProperty("传化机构秘钥")
    private String chOrgKey;

    @ExcelProperty("传化保底费率")
    private BigDecimal rateChuanhua;

    @ExcelProperty("汇付保底费率")
    private BigDecimal rateHuifu;

    @ExcelProperty("乐刷机构号")
    private String lsOrgId;

    @ExcelProperty("乐刷机构秘钥")
    private String lsOrgKey;

    @ExcelProperty("乐刷机构公众号")
    private String lsSweepAppid;

    @ExcelProperty("乐刷机构公众号秘钥")
    private String lsSweepAppidKey;

    @ExcelProperty("是否允许代理商可查看微信买单商户，0：否；1是。")
    private Integer isCloudpayMerchant;

    @ExcelProperty("汇付机构号")
    private String hfOrgId;

    @ExcelProperty("汇付机构秘钥路径")
    private String hfOrgKeyPath;

    @ExcelProperty("汇付机构秘钥密码")
    private String hfOrgKeyPassword;

    @ExcelProperty("汇付代理商操作员")
    private String hfOrgOptellerid;

    @ExcelProperty("汇付公众号appid")
    private String hfSweepAppid;

    @ExcelProperty("汇付公众号appid秘钥")
    private String hfSweepAppidKey;

    @ExcelProperty("哆啦宝保底费率")
    private BigDecimal rateDuolabao;

    @ExcelProperty("微信直连保底费率")
    private BigDecimal rateSub;

    @ExcelProperty("支付宝直连保底费率")
    private BigDecimal rateSubalipay;

    @ExcelProperty("信汇保底费率")
    private BigDecimal rateXinhui;

    @ExcelProperty("联动保底费率")
    private BigDecimal rateLiandong;

    @ExcelProperty("微信买单关注appid")
    private String wxmaidanSubscribeAppid;

    @ExcelProperty("是否允许审核政策；0:否；1：是。")
    private Integer isAdmitPolicy;

    @ExcelProperty("海科服务商编号")
    private String hkOrgId;

    @ExcelProperty("海科accesskey")
    private String hkOrgKey;

    @ExcelProperty("海科accessid")
    private String hkOrgAccessid;

    @ExcelProperty("海科传输秘钥")
    private String hkPassKey;

    @ExcelProperty("海科配置公众号appid")
    private String hkSweepAppid;

    @ExcelProperty("海科配置公众号秘钥")
    private String hkSweepAppidKey;

    @ExcelProperty("海科微信渠道号")
    private String hkWxChannelNo;

    @ExcelProperty("海科支付宝渠道号")
    private String hkAliChannelNo;

    @ExcelProperty("瑞银信保底费率")
    private BigDecimal rateRuiyinxin;

    @ExcelProperty("海科保底费率")
    private BigDecimal rateHaike;

    @ExcelProperty("建行保底费率")
    private BigDecimal rateCcb;

    @ExcelProperty("瑞银信机构号")
    private String ryxOrgId;

    @ExcelProperty("瑞银信私钥路径")
    private String ryxPrivateKeyPath;

    @ExcelProperty("瑞银信公钥路径")
    private String ryxPublicKeyPath;

    @ExcelProperty("瑞银信cooperator")
    private String ryxCooperator;

    @ExcelProperty("分公司/省公司id")
    private Integer branchOfficeId;

    @ExcelProperty("瑞银信公众号appid")
    private String ryxSweepAppid;

    @ExcelProperty("瑞银信公众号appid秘钥")
    private String ryxSweepAppidKey;

    @ExcelProperty("瑞银信微信渠道号")
    private String ryxWxChannelId;

    @ExcelProperty("瑞银信accessid：")
    private String ryxAccessId;

    @ExcelProperty("瑞银信进件私钥")
    private String ryxSigninPrivateKeyPath;

    @ExcelProperty("瑞银信进件公钥")
    private String ryxSigninPublicKeyPath;

    @ExcelProperty("云闪付直连保底费率")
    private BigDecimal rateSubunionpay;

    @ExcelProperty("外插模板id")
    private String customerUid;

    @ExcelProperty("内插模板id")
    private String modelUid;

    @ExcelProperty("瑞银信支付宝PID")
    private String ryxAliChannelId;

    @ExcelProperty("瑞银信结算周期")
    private String ryxPaidoutType;

    @ExcelProperty("徽商渠道号")
    private String wftChannel;

    @ExcelProperty("徽商机构号")
    private String wftOrgId;

    @ExcelProperty("徽商密钥")
    private String wftOrgKey;

    @ExcelProperty("徽商微信渠道号")
    private String wftWxChannelNo;

    @ExcelProperty("徽商支付宝渠道号")
    private String wftAliChannelNo;

    @ExcelProperty("徽商appid")
    private String wftAppid;

    @ExcelProperty("徽商appid密钥")
    private String wftAppidKey;

    @ExcelProperty("是否邮政")
    private Integer isYouzheng;

    @ExcelProperty("是否允许确认开通；0：否；1：是")
    private Integer isCanConfirmMerchant;

    @ExcelProperty("徽商保底费率")
    private BigDecimal rateWft;

    @ExcelProperty("是否允许重置商户通道")
    private Integer isCanResetMerchant;

    @ExcelProperty("四九八保底费率")
    private BigDecimal rateSjb;

    @ExcelProperty("徽商银行通道保底费率")
    private BigDecimal rateWftHs;

    @ExcelProperty("付呗保底费率")
    private BigDecimal rateFubei;

    @ExcelProperty("浦发银行通道保底费率")
    private BigDecimal ratePufabank;

    @ExcelProperty("随行付通道保底费率")
    private BigDecimal rateSxfTq;

    @ExcelProperty("平安银行通道保底费率")
    private BigDecimal ratePabank;

    @ExcelProperty("是否")
    private Integer isDirect;

    @ExcelProperty("通联三方通道保底费率")
    private BigDecimal rateTonglian;

    @ExcelProperty("浙江农信通道保底费率")
    private BigDecimal rateZjnx;

    @ExcelProperty("拉卡拉保底费率")
    private BigDecimal rateLakala;

    @ExcelProperty("信汇S保底费率")
    private BigDecimal rateWangkebao;

    @ExcelProperty("是否显示商户投诉；0:否；1：是")
    private Integer isMerchantRisk;

    @ExcelProperty("是否显示分店管理；0:否；1：是")
    private Integer isMerchantDevice;

    @ExcelProperty("拉卡拉云MIS保底费率,单位千分之一")
    private BigDecimal rateLakalaMis;

    @ExcelProperty("智付三方保底费率")
    private BigDecimal rateZhifu;

    @ExcelProperty("智付平台机构号")
    private String zhifuOrgId;

    @ExcelProperty("智付签约秘钥")
    private String zhifuOrgKey;

    @ExcelProperty("是否向上游报备；1：是；0：是")
    private Integer isBaobeiUp;

    @ExcelProperty("智付微信渠道号")
    private String zhifuWxMch;

    @ExcelProperty("智付支付宝渠道号")
    private String zhifuAliMch;

    @ExcelProperty("智付appid")
    private String zhifuAppid;

    @ExcelProperty("智付app开发者秘钥")
    private String zhifuAppKey;

    @ExcelProperty("是否允许萤火虫插件；0:否；1：是")
    private Integer isYhcOut;

    @ExcelProperty("外插模板id")
    private String outsideModelUid;

    @ExcelProperty("信汇S服务商号")
    private String wkbOrgNo;

    @ExcelProperty("信汇S服务商秘钥")
    private String wkbSignKey;

    @ExcelProperty("信汇S微信渠道号")
    private String wkbWxChannelId;

    @ExcelProperty("信汇S支付宝渠道号")
    private String wkbAliChannelId;

    @ExcelProperty("信汇S进件地址")
    private String wkbGatewayUrl;

    @ExcelProperty("信汇SAppid")
    private String wkbAppid;

    @ExcelProperty("信汇SAppid秘钥")
    private String wkbAppidSecert;

    @ExcelProperty("信汇服务商号")
    private String xinhuiOrgNo;

    @ExcelProperty("信汇服务商秘钥")
    private String xinhuiSignKey;

    @ExcelProperty("信汇微信渠道号")
    private String xinhuiWxChannelId;

    @ExcelProperty("信汇支付宝渠道号")
    private String xinhuiAliChannelId;

    @ExcelProperty("信汇进件地址")
    private String xinhuiGatewayUrl;

    @ExcelProperty("信汇Appid")
    private String xinhuiAppid;

    @ExcelProperty("信汇Appid秘钥")
    private String xinhuiAppidSecert;

    @ExcelProperty("是否限制地域；0:否；1;是。")
    private Integer isLimitArea;

    @ExcelProperty("现代金控保底费率")
    private BigDecimal rateXiandai;

    @ExcelProperty("现代机构号")
    private String xiandaiOrgId;

    @ExcelProperty("现代秘钥路径")
    private String xiandaiSignKey;

    @ExcelProperty("现代解密秘钥路径")
    private String xiandaiPublicKey;

    @ExcelProperty("现代秘钥密码")
    private String xiandaiPwd;

    @ExcelProperty("现代Appid")
    private String xiandaiAppid;

    @ExcelProperty("现代Appid秘钥")
    private String xiandaiAppidSecret;

    @ExcelProperty("微信mch_id")
    private String xiandaiWxMchid;

    @ExcelProperty("支付宝mch_id")
    private String xiandaiAliMchid;

    @ExcelProperty("现代秘钥商户编号")
    private String xiandaiMerOrgId;

    @ExcelProperty("现代创建人id")
    private String xiandaiCreateUserId;

    @ExcelProperty("四九八服务商号")
    private String sjbOrgId;

    @ExcelProperty("四九八微信渠道号")
    private String sjbChannelMchId;

    @ExcelProperty("四九八支付宝渠道号")
    private String sjbAliChannelMchId;

    @ExcelProperty("四九八进件秘钥")
    private String sjbSignKey;

    @ExcelProperty("插件V5的模板id")
    private String yhcV5ModelUid;

    @ExcelProperty("是否允许确认开通；0:否；1;是。")
    private Integer isConfirmMerchantQs;

    @ExcelProperty("邮政机构id")
    private String yzOrgId;

    @ExcelProperty("邮政机构账号")
    private String yzOrgAccount;

    @ExcelProperty("是否显示商户补贴栏目，0:否；1;是。")
    private Integer isShowRedpackage;

    @ExcelProperty("是否参与统计；1：参与；0：不参与")
    private Integer isStatistics;

    @ExcelProperty("是否允许政策审核：1允许；0不允许")
    private Integer isPolicyUpdate;

    @ExcelProperty("电信好码保底费率")
    private BigDecimal rateDianxin;

    @ExcelProperty("邮政数币通道费率")
    private BigDecimal rateYouzheng;

    @ExcelProperty("数币直联通道费率")
    private BigDecimal rateYzPay;

    @ExcelProperty("葫芦机构号")
    private String huluOrgId;

    @ExcelProperty("瑞银信（H）平台公钥")
    private String huluPlatformPublic;

    @ExcelProperty("瑞银信（H）服务商私钥")
    private String huluPrivate;

    @ExcelProperty("葫芦微信渠道号")
    private String huluWxChannelId;

    @ExcelProperty("葫芦支付宝渠道号")
    private String huluAliChannelId;

    @ExcelProperty("葫芦appid")
    private String huluAppid;

    @ExcelProperty("葫芦密钥")
    private String huluAppidse;

    @ExcelProperty("葫芦保底费率")
    private BigDecimal rateHulu;

}
