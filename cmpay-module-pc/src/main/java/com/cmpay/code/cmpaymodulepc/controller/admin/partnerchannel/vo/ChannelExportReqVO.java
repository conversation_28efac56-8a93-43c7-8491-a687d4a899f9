package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;





@Schema(description = "管理后台 - 市级分公司渠道 Excel 导出 Request VO，参数和 ChannelPageReqVO 是一致的")
@Data
public class ChannelExportReqVO {

    @Schema(description = "公司")
    private String company;

    @Schema(description = "负责人姓名")
    private String keeper;

    @Schema(description = "和融通保底")
    private BigDecimal yirongmaRate;

    @Schema(description = "和融通业务员号", example = "19235")
    private String yirongmaUserId;

    @Schema(description = "随行付保底")
    private BigDecimal suixingfuRate;

    @Schema(description = "随行付业务员号", example = "7748")
    private String suixingfuUserId;

    @Schema(description = "乐刷保底费率")
    private BigDecimal leshuaRate;

    @Schema(description = "审核方式", example = "1")
    private Integer auditType;

    @Schema(description = "户主姓名", example = "王五")
    private String cardName;

    @Schema(description = "银行卡号")
    private String cardNo;

    @Schema(description = "开户银行")
    private String bankAddress;

    @Schema(description = "添加时间", required = true)
    @NotNull(message = "添加时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date insertTime;

    @Schema(description = "负责人电话")
    private String keeperphone;

    @Schema(description = "客服电话")
    private String phone;

    @Schema(description = "和融通普通300以下保底费率")
    private BigDecimal rateYirongma300down;

    @Schema(description = "和融通普通300以上保底费率")
    private BigDecimal rateYirongma300up;

    @Schema(description = "付费通直联保底费率")
    private BigDecimal rateFufeitong;

    @Schema(description = "付费通三方保底费率")
    private BigDecimal rateShoubei;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "开票类型", example = "1")
    private String invoiceType;

    @Schema(description = "纳税人识别号")
    private String taxpayerNum;

    @Schema(description = "开票项目")
    private String invoiceProgrem;

    @Schema(description = "是否结算", example = "22169")
    private Integer isKyaccount;

    @Schema(description = "开票名称", example = "李四")
    private String invoiceName;

    @Schema(description = "电话")
    private String invoicePhone;

    @Schema(description = "开户行")
    private String invoiceBank;

    @Schema(description = "账号", example = "29668")
    private String invoiceAccount;

    @Schema(description = "银行联号")
    private String invoiceBanknum;

    @Schema(description = "是否SAAS商户")
    private Integer partnerAgent;

    @Schema(description = "合作商appid", example = "7032")
    private String merchantAppid;

    @Schema(description = "合作商秘钥")
    private String merchantAppidse;

    @Schema(description = "合作商审核模板ID", example = "26859")
    private String merchantAuditTempId;

    @Schema(description = "合作商支付模板ID", example = "10048")
    private String merchantPayoverTempId;

    @Schema(description = "合作商结算模板ID", example = "5256")
    private String merchantPaidoutTempId;

    @Schema(description = "合作商结算失败模板ID", example = "25100")
    private String merchantPaidoutFailTempId;

    @Schema(description = "合作商风控模板ID", example = "13658")
    private String merchantRiskTempId;

    @Schema(description = "预存卡购买模板ID", example = "32135")
    private String activityPerstoreBuyTempId;

    @Schema(description = "合作商品牌名")
    private String brand;

    @Schema(description = "合作商品牌Logo")
    private String brandLogo;

    @Schema(description = "公众号关注图片")
    private String gzhImg;

    @Schema(description = "收呗保底费率")
    private BigDecimal shoubeiRate;

    @Schema(description = "和融通机构号", example = "28628")
    private String yrmOrgId;

    @Schema(description = "和融通机构秘钥")
    private String yrmOrgKey;

    @Schema(description = "是否允许受理商")
    private Integer isAccept;

    @Schema(description = "是否允许个人开户", example = "27638")
    private Integer isIndividualAccount;

    @Schema(description = "随行付机构号", example = "12903")
    private String sxfOrgId;

    @Schema(description = "随行付机构秘钥")
    private String sxfOrgKey;

    @Schema(description = "随行付公众号appid", example = "24532")
    private String sxfSweepAppid;

    @Schema(description = "随行付公众号appid秘钥")
    private String sxfSweepAppidKey;

    @Schema(description = "随行付微信渠道号")
    private String sxfWxChannelNo;

    @Schema(description = "随行付支付宝PID")
    private String sxfAliChannelNo;

    @Schema(description = "付呗服务商编号", example = "24110")
    private String fubeiOrgId;

    @Schema(description = "付呗秘钥")
    private String fubeiSignKey;

    @Schema(description = "付呗配置公众号appid", example = "1036")
    private String fubeiAppid;

    @Schema(description = "付呗配置公众号秘钥")
    private String fubeiAppidKey;

    @Schema(description = "是否允许渠道权限")
    private Integer isPayoffAuthority;

    @Schema(description = "微信小程序保底费率")
    private BigDecimal rateWxMicro;

    @Schema(description = "微信小程序业务员号")
    private String wxMicroNumber;

    @Schema(description = "嘉联通道的保底费率")
    private BigDecimal rateJialian;

    @Schema(description = "是否允许小微开户")
    private Integer isWxmicro;

    @Schema(description = "微信买单保底费率")
    private BigDecimal rateCloudpay;

    @Schema(description = "和融通品牌")
    private String yirongmaBrand;

    @Schema(description = "微信买单50以下返佣费率")
    private BigDecimal rateCloudpay50down;

    @Schema(description = "传化机构号", example = "1572")
    private String chOrgId;

    @Schema(description = "传化机构秘钥")
    private String chOrgKey;

    @Schema(description = "传化三方通道保底费率")
    private BigDecimal rateChuanhua;

    @Schema(description = "汇付三方通道保底费率")
    private BigDecimal rateHuifu;

    @Schema(description = "乐刷机构号", example = "3346")
    private String lsOrgId;

    @Schema(description = "乐刷机构秘钥")
    private String lsOrgKey;

    @Schema(description = "乐刷公众号appid", example = "27752")
    private String lsSweepAppid;

    @Schema(description = "乐刷公众号appid秘钥")
    private String lsSweepAppidKey;

    @Schema(description = "是否允许显示微信买单商户")
    private Integer isCloudpayMerchant;

    @Schema(description = "汇付机构号", example = "1053")
    private String hfOrgId;

    @Schema(description = "汇付秘钥路径")
    private String hfOrgKeyPath;

    @Schema(description = "汇付秘钥密码")
    private String hfOrgKeyPassword;

    @Schema(description = "汇付代理商操作员", example = "28843")
    private String hfOrgOptellerid;

    @Schema(description = "汇付公众号appid", example = "21427")
    private String hfSweepAppid;

    @Schema(description = "汇付公众号appid秘钥")
    private String hfSweepAppidKey;

    @Schema(description = "哆啦宝三方通道保底费率")
    private BigDecimal rateDuolabao;

    @Schema(description = "微信直连保底费率")
    private BigDecimal rateSub;

    @Schema(description = "支付宝直连保底费率")
    private BigDecimal rateSubalipay;

    @Schema(description = "信汇三方通道保底费率")
    private BigDecimal rateXinhui;

    @Schema(description = "联动优势三方通道保底费率")
    private BigDecimal rateLiandong;

    @Schema(description = "微信买单关注appid", example = "14594")
    private String wxmaidanSubscribeAppid;

    @Schema(description = "是否允许开户政策管理")
    private Integer isAdmitPolicy;

    @Schema(description = "海科服务商编号", example = "12954")
    private String hkOrgId;

    @Schema(description = "海科accesskey")
    private String hkOrgKey;

    @Schema(description = "海科accessid", example = "29352")
    private String hkOrgAccessid;

    @Schema(description = "海科传输秘钥")
    private String hkPassKey;

    @Schema(description = "海科配置公众号appid", example = "29737")
    private String hkSweepAppid;

    @Schema(description = "海科配置公众号秘钥")
    private String hkSweepAppidKey;

    @Schema(description = "海科微信渠道号")
    private String hkWxChannelNo;

    @Schema(description = "海科支付宝渠道号")
    private String hkAliChannelNo;

    @Schema(description = "瑞银信三方保底费率")
    private BigDecimal rateRuiyinxin;

    @Schema(description = "海科三方保底费率")
    private BigDecimal rateHaike;

    @Schema(description = "建行保底费率")
    private BigDecimal rateCcb;

    @Schema(description = "瑞银信机构号", example = "26130")
    private String ryxOrgId;

    @Schema(description = "瑞银信私钥路径")
    private String ryxPrivateKeyPath;

    @Schema(description = "瑞银信公钥路径")
    private String ryxPublicKeyPath;

    @Schema(description = "瑞银信cooperator")
    private String ryxCooperator;

    @Schema(description = "所属分公司", example = "16061")
    private Integer branchOfficeId;

    @Schema(description = "瑞银信公众号appid", example = "30668")
    private String ryxSweepAppid;

    @Schema(description = "瑞银信公众号appid秘钥")
    private String ryxSweepAppidKey;

    @Schema(description = "瑞银信微信渠道号", example = "5529")
    private String ryxWxChannelId;

    @Schema(description = "瑞银信accessid：", example = "19527")
    private String ryxAccessId;

    @Schema(description = "瑞银信进件私钥")
    private String ryxSigninPrivateKeyPath;

    @Schema(description = "瑞银信进件公钥")
    private String ryxSigninPublicKeyPath;

    @Schema(description = "云闪付直连保底费率")
    private BigDecimal rateSubunionpay;

    @Schema(description = "客户id", example = "11832")
    private String customerUid;

    @Schema(description = "内插模板id", example = "10811")
    private String modelUid;

    @Schema(description = "瑞银信支付宝PID", example = "31424")
    private String ryxAliChannelId;

    @Schema(description = "瑞银信结算周期", example = "1")
    private String ryxPaidoutType;

    @Schema(description = "徽商渠道号")
    private String wftChannel;

    @Schema(description = "徽商机构号", example = "17803")
    private String wftOrgId;

    @Schema(description = "徽商密钥")
    private String wftOrgKey;

    @Schema(description = "徽商微信渠道号")
    private String wftWxChannelNo;

    @Schema(description = "徽商支付宝渠道号")
    private String wftAliChannelNo;

    @Schema(description = "徽商appid", example = "14878")
    private String wftAppid;

    @Schema(description = "徽商appid密钥")
    private String wftAppidKey;

    @Schema(description = "是否邮政渠道")
    private Integer isYouzheng;

    @Schema(description = "是否允许确认开通")
    private Integer isCanConfirmMerchant;

    @Schema(description = "徽商保底费率")
    private BigDecimal rateWft;

    @Schema(description = "是否允许重置通道")
    private Integer isCanResetMerchant;

    @Schema(description = "四九八通道保底费率")
    private BigDecimal rateSjb;

    @Schema(description = "徽商银行通道保底费率")
    private BigDecimal rateWftHs;

    @Schema(description = "付呗保底费率")
    private BigDecimal rateFubei;

    @Schema(description = "浦发银行通道保底费率")
    private BigDecimal ratePufabank;

    @Schema(description = "随行付通道保底费率")
    private BigDecimal rateSxfTq;

    @Schema(description = "平安银行通道保底费率")
    private BigDecimal ratePabank;

    @Schema(description = "是否")
    private Integer isDirect;

    @Schema(description = "通联三方通道保底费率")
    private BigDecimal rateTonglian;

    @Schema(description = "浙江农信通道保底费率")
    private BigDecimal rateZjnx;

    @Schema(description = "拉卡拉保底费率")
    private BigDecimal rateLakala;

    @Schema(description = "信汇S保底费率")
    private BigDecimal rateWangkebao;

    @Schema(description = "是否显示商户投诉")
    private Integer isMerchantRisk;

    @Schema(description = "是否显示商户分店")
    private Integer isMerchantDevice;

    @Schema(description = "拉卡拉云MIS保底费率,单位千分之一")
    private BigDecimal rateLakalaMis;

    @Schema(description = "智付三方保底费率")
    private BigDecimal rateZhifu;

    @Schema(description = "智付平台机构号", example = "16090")
    private String zhifuOrgId;

    @Schema(description = "智付签约秘钥")
    private String zhifuOrgKey;

    @Schema(description = "是否向上游报备；1：是；0：是")
    private Integer isBaobeiUp;

    @Schema(description = "智付微信渠道号")
    private String zhifuWxMch;

    @Schema(description = "智付支付宝渠道号")
    private String zhifuAliMch;

    @Schema(description = "智付appid", example = "3390")
    private String zhifuAppid;

    @Schema(description = "智付app开发者秘钥")
    private String zhifuAppKey;

    @Schema(description = "外插是否显示云mis")
    private Integer isYhcOut;

    @Schema(description = "外插模板id", example = "21488")
    private String outsideModelUid;

    @Schema(description = "信汇S服务商号")
    private String wkbOrgNo;

    @Schema(description = "信汇S服务商秘钥")
    private String wkbSignKey;

    @Schema(description = "信汇S微信渠道号", example = "22151")
    private String wkbWxChannelId;

    @Schema(description = "信汇S支付宝渠道号", example = "20590")
    private String wkbAliChannelId;

    @Schema(description = "信汇S进件地址", example = "https://www.iocoder.cn")
    private String wkbGatewayUrl;

    @Schema(description = "信汇SAppid", example = "20074")
    private String wkbAppid;

    @Schema(description = "信汇SAppid秘钥")
    private String wkbAppidSecert;

    @Schema(description = "信汇服务商号")
    private String xinhuiOrgNo;

    @Schema(description = "信汇服务商秘钥")
    private String xinhuiSignKey;

    @Schema(description = "信汇微信渠道号", example = "29376")
    private String xinhuiWxChannelId;

    @Schema(description = "信汇支付宝渠道号", example = "6592")
    private String xinhuiAliChannelId;

    @Schema(description = "信汇进件地址", example = "https://www.iocoder.cn")
    private String xinhuiGatewayUrl;

    @Schema(description = "信汇Appid", example = "30797")
    private String xinhuiAppid;

    @Schema(description = "信汇Appid秘钥")
    private String xinhuiAppidSecert;

    @Schema(description = "是否限制开户区域")
    private Integer isLimitArea;

    @Schema(description = "现代金控保底费率")
    private BigDecimal rateXiandai;

    @Schema(description = "现代机构号", example = "25205")
    private String xiandaiOrgId;

    @Schema(description = "现代秘钥路径")
    private String xiandaiSignKey;

    @Schema(description = "现代解密秘钥路径")
    private String xiandaiPublicKey;

    @Schema(description = "现代秘钥密码")
    private String xiandaiPwd;

    @Schema(description = "现代Appid", example = "19616")
    private String xiandaiAppid;

    @Schema(description = "现代Appid秘钥")
    private String xiandaiAppidSecret;

    @Schema(description = "微信mch_id", example = "16885")
    private String xiandaiWxMchid;

    @Schema(description = "支付宝mch_id", example = "29079")
    private String xiandaiAliMchid;

    @Schema(description = "现代秘钥商户编号", example = "32116")
    private String xiandaiMerOrgId;

    @Schema(description = "现代创建人id", example = "29301")
    private String xiandaiCreateUserId;

    @Schema(description = "四九八服务商号", example = "8135")
    private String sjbOrgId;

    @Schema(description = "四九八微信渠道号", example = "3077")
    private String sjbChannelMchId;

    @Schema(description = "四九八支付宝渠道号", example = "29613")
    private String sjbAliChannelMchId;

    @Schema(description = "四九八进件秘钥")
    private String sjbSignKey;

    @Schema(description = "插件V5的模板id", example = "3063")
    private String yhcV5ModelUid;

    @Schema(description = "是否允许商户上传轻收单")
    private Integer isConfirmMerchantQs;

    @Schema(description = "邮政机构id", example = "31146")
    private String yzOrgId;

    @Schema(description = "邮政机构账号", example = "18493")
    private String yzOrgAccount;

    @Schema(description = "是否显示商户补贴")
    private Integer isShowRedpackage;

    @Schema(description = "是否参与统计；1：参与；0：不参与")
    private Integer isStatistics;

    @Schema(description = "是否允许政策审核：1允许；0不允许")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Integer isPolicyUpdate;

    @Schema(description = "电信好码保底费率")
    private BigDecimal rateDianxin;

    @Schema(description = "数币三方间联通道费率")
    private BigDecimal rateYouzheng;

    @Schema(description = "数币直联通道费率")
    private BigDecimal rateYzPay;

    @Schema(description = "瑞银信(H)机构号", example = "12832")
    private String huluOrgId;

    @Schema(description = "瑞银信(H)平台公钥")
    private String huluPlatformPublic;

    @Schema(description = "瑞银信(H)服务商私钥")
    private String huluPrivate;

    @Schema(description = "瑞银信(H)微信渠道号", example = "24159")
    private String huluWxChannelId;

    @Schema(description = "瑞银信(H)支付宝PID", example = "17679")
    private String huluAliChannelId;

    @Schema(description = "瑞银信(H)公众号appid", example = "32137")
    private String huluAppid;

    @Schema(description = "瑞银信(H)公众号appid密钥")
    private String huluAppidse;

    @Schema(description = "瑞银信(H)通道保底费率")
    private BigDecimal rateHulu;

}
