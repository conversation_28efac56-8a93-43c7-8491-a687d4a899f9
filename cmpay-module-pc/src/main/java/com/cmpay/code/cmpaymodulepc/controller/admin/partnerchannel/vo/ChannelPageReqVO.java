package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 市级分公司渠道分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelPageReqVO extends PageParam {

    @Schema(description = "所属公司")
    private String company;

    @Schema(description = "负责人姓名")
    private String keeper;

    @Schema(description = "客服电话")
    private String keeperphone;

    @Schema(description = "大渠道号")
    private String partnerId;

    @Schema(description = "省id")
    private String branchId;

    @Schema(description = "邮政机构号")
    private String yzOrgId;

}
