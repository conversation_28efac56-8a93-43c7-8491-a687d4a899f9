package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelReqVo {
    @Schema(description = "市ID")
    private String partnerId;
    @Schema(description = "公司")
    private String company;

    @Schema(description = "负责人姓名")
    private String keeper;

    @Schema(description = "负责人电话")
    private String keeperphone;

    @Schema(description = "客服电话")
    private String phone;

    @Schema(description = "所属分公司", example = "16061")
    private Integer branchOfficeId;

    @Schema(description = "审核方式", example = "1")
    private Integer auditType;

    @Schema(description = "是否允许受理商")
    private Integer isAccept;

    @Schema(description = "是否允许渠道权限")
    private Integer isPayoffAuthority;

    @Schema(description = "是否允许开户政策管理")
    private Integer isAdmitPolicy;

    @Schema(description = "是否邮政渠道")
    private Integer isYouzheng;

    @Schema(description = "是否允许重置通道")
    private Integer isCanResetMerchant;

    @Schema(description = "是否允许确认开通")
    private Integer isCanConfirmMerchant;

    @Schema(description = "是否显示商户投诉")
    private Integer isMerchantRisk;

    @Schema(description = "是否显示商户分店")
    private Integer isMerchantDevice;

    @Schema(description = "是否显示商户补贴")
    private Integer isShowRedpackage;

    @Schema(description = "外插是否显示云mis")
    private Integer isYhcOut;

    @Schema(description = "内插模板id", example = "10811")
    private String modelUid;

    @Schema(description = "外插模板id", example = "21488")
    private String outsideModelUid;

    @Schema(description = "是否SAAS商户")
    private Integer partnerAgent;

    @Schema(description = "内插v5模板id", example = "3063")
    private String yhcV5ModelUid;

    @Schema(description = "是否允许：政策审核1允许；0不允许")
    private Integer isPolicyUpdate;

    @Schema(description = "瑞银信三方")
    private RyxChannelReqVo ryxChannelReqVo;

    @Schema(description = "抖音")
    private DyChannelReqVo dyChannelReqVo;

    @Schema(description = "微企付")
    private WqfReqVp wqfReqVp;

    @Schema(description = "国通")
    private GuoTongReqVo guoTongReqVo;

    @Schema(description = "银联")
    private YlswReqVo ylswReqVo;

    @Schema(description = "瑞银信（H）")
    private HuluReqVo huluReqVo;

    @Schema(description = "随行付")
    private SxfChannelReqVo sxfChannelReqVo;

    @Schema(description = "微信直连")
    private SubChannelReqVo subChannelReqVo;

    @Schema(description = "支付宝直联")
    private AlipayReqVo alipayReqVo;

    @Schema(description = "四九八通道")
    private SjbChannelReqVo sjbChannelReqVo;

    @Schema(description = "易票联")
    private YplChannelReqVo yplChannelReqVo;

    @Schema(description = "乐刷")
    private LesuaChannelReqVo lesuaChannelReqVo;

    @Schema(description = "和融通")
    private YirongmaChannelReqVo yirongmaChannelReqVo;

    @Schema(description = "付费通")
    private FufeitongChannel fufeitongChannel;

    @Schema(description = "嘉联")
    private JialianChannelReqVo jialianChannelReqVo;

    @Schema(description = "微信买单")
    private CloudpayChannelReqVo cloudpayChannelReqVo;

    @Schema(description = "传化三方")
    private ChuanhuaChannelReqVo chuanhuaChannelReqVo;

    @Schema(description = "哆啦宝三方")
    private DuolabaoChannelReqVo duolabaoChannelReqVo;

    @Schema(description = "信汇")
    private XinhuiChannelReqVo xinhuiChannelReqVo;

    @Schema(description = "联动优势三方通道")
    private LiandongChannelReqVo liandongChannelReqVo;

    @Schema(description = "徽商")
    private WftChannelReqVo wftChannelReqVo;

    @Schema(description = "浦发银行")
    private PufabankChannelReqVo pufabankChannelReqVo;

    @Schema(description = "平安银行")
    private PabankChannelReqVo pabankChannelReqVo;

    @Schema(description = "通联")
    private TongLianReqVo tongLianReqVo;

    @Schema(description = "浙江农信")
    private ZjnxReqVo zjnxReqVo;

    @Schema(description = "汇付")
    private HuifuReqVo huifuReqVo;

    @Schema(description = "海科")
    private HaikeReqVo haikeReqVo;

    @Schema(description = "付呗")
    private FubeiReqVo fubeiReqVo;

    @Schema(description = "建行")
    private CcbReqVo ccbReqVo;

    @Schema(description = "拉卡拉")
    private LakalaReqVo lakalaReqVo;

    @Schema(description = "拉卡拉mis")
    private LakalaMisReqVo lakalaMisReqVo;

    @Schema(description = "智付")
    private ZhifuReqVo zhifuReqVo;

    @Schema(description = "信汇s")
    private WangkebaoChannelReqVo wangkebaoChannelReqVo;

    @Schema(description = "现代")
    private XiandaiReqVo xiandaiReqVo;

    @Schema(description = "电信好码")
    private DianxinReqVo dianxinReqVo;

    @Schema(description = "数币")
    private YzReqVo yzReqVo;

    @Schema(description = "是否支持切换备用通道；0不支持；1支持")
    private Integer isStandbyChannel;

    @Schema(description = "是否需要数据脱敏:0需要;1:不需要")
    private String isDataDesc;

    @Schema(description = "拉卡拉通道")
    private LaklaV3ReqVo laklaV3ReqVo;

    @Schema(description = "易生通道")
    private YiShengResqVo yiShengResqVo;
}
