package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 市级分公司渠道 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelRespVO extends ChannelBaseVO {

    @Schema(description = "大渠道号", required = true, example = "1939")
    private String partnerId;

    @Schema(description = "是否支持备用通道", required = true, example = "1")
    private Integer isStandbyChannel;

    @Schema(description = "是否支持走访任务功能；0不支持；1支持")
    private String isVisitTask;

    @Schema(description = "目标商户标志，0关闭，1开启")
    private String isTargetMer;

    @Schema(description = "账户补贴标志，0关闭，1开启")
    private String isAccountSubsidy;

    @Schema(description = "pc账户补贴标志，0关闭，1开启")
    private String pcAccountSubsidy;

}
