package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ChannelShopDcpayVO {
    @Schema(description = "数币费率")
    private BigDecimal dcpayRate;

    @Schema(description = "结算通道")
    private String channel;

    @Schema(description = "结算通道字符串")
    private String payTypeStr;

    @Schema(description = "数币商户号")
    private String dcpayMerchantNo;

    @Schema(description = "上游商户号")
    private String upMerchantNo;

    @Schema(description = "状态中文")
    private String statusStr;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "通道信息")
    private String rejectMsg;

    @Schema(description = "通道真实商户号")
    private String channelMerchantNo;

}
