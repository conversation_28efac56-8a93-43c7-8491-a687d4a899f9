package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ChannelShopRespAllVO {

    @Schema(description = "支付宝通道信息")
    private ChannelShopAlipayVO channelShopAlipayVO;

    @Schema(description = "微信通道信息")
    private ChannelShopWxpayVO channelShopWxpayVO;

    @Schema(description = "云闪付通道信息")
    private ChannelShopUnionpayVO channelShopUnionpayVO;

    @Schema(description = "数币通道信息")
    public ChannelShopDcpayVO channelShopDcpayVO;

}
