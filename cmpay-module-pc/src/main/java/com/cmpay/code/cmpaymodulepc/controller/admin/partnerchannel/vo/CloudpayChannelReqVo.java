package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CloudpayChannelReqVo {
    @Schema(description = "微信买单通道")
    private String channel="cloundpay";
    @Schema(description = "微信买单保底费率")
    private BigDecimal rateCloudpay;
    @Schema(description = "微信买单50以下返佣费率")
    private BigDecimal rateCloudpay50down;
}
