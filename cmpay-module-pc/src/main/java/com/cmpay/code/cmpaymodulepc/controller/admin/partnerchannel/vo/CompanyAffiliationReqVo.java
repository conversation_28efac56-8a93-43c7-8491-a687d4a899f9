package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-08-03 10:31:31
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyAffiliationReqVo {

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "省id")
    private String branchId;
}
