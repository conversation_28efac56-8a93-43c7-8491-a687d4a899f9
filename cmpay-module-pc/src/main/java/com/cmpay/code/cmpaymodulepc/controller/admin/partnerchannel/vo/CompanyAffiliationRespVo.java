package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-08-03 10:34:02
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyAffiliationRespVo {

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "营业点名称")
    private String acceptCompany;

    @Schema(description = "营业点机构号")
    private String acceptYzOrgId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "区县名称")
    private String agentCompany;

    @Schema(description = "区县机构号")
    private String agentYzOrgId;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "市级名称")
    private String partnerCompany;

    @Schema(description = "市级机构号")
    private String partnerYzOrgId;

    @Schema(description = "省id")
    private String branchId;

    @Schema(description = "省名称")
    private String branchCompany;

    @Schema(description = "省级机构号")
    private String branchYzOrgId;
}
