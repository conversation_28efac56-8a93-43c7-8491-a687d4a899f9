package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DyChannelReqVo {
    @Schema(description = "抖音通道")
    private String channel="douyin";
    @Schema(description = "抖音机构号", example = "1053")
    private String dyOrgNo;
    @Schema(description = "抖音保底费率")
    private BigDecimal dyRate;
    @Schema(description = "抖音结算周期", example = "1")
    private String dyPaidoutType;
}
