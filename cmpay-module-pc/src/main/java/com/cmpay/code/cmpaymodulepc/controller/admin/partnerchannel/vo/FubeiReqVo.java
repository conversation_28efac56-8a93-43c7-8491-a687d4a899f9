package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FubeiReqVo {
    @Schema(description = "通道")
    private String channel = "fubei";

    @Schema(description = "付呗服务商编号", example = "24110")
    private String fubeiOrgId;

    @Schema(description = "付呗秘钥")
    private String fubeiSignKey;

    @Schema(description = "付呗配置公众号appid", example = "1036")
    private String fubeiAppid;

    @Schema(description = "付呗配置公众号秘钥")
    private String fubeiAppidKey;

    @Schema(description = "付呗保底费率")
    private BigDecimal rateFubei;

}
