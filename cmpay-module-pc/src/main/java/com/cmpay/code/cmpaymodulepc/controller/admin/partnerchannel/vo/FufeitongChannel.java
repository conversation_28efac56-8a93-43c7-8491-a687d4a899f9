package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FufeitongChannel {
    @Schema(description = "付费通通道")
    private String channel="fufeitong";

    @Schema(description = "付费通直联保底费率")
    private BigDecimal rateFufeitong;

    @Schema(description = "付费通三方保底费率")
    private BigDecimal rateShoubei;
}
