package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * author su<PERSON><PERSON><PERSON>
 * date 2024/2/21 15:16
 * version 1.0
 * 大渠道地区管理请求类
 */
@Data
public class GetPartnerAreaManageReqVO extends PageParam {
    @Schema(description = "市id")
    private String partnerId;

    @Schema(description = "手机号")
    private String keeperphone;

    @Schema(description = "负责人")
    private String keeper;

    @Schema(description = "公司名")
    private String company;
}
