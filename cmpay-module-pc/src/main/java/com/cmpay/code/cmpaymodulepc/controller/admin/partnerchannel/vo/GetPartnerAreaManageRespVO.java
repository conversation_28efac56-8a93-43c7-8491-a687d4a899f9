package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * author suoh<PERSON><PERSON>
 * date 2024/2/21 15:20
 * version 1.0
 * 大渠道地区管理返回类
 */
@Data
public class GetPartnerAreaManageRespVO {
    @Schema(description = "编号")
    private String id;

    @Schema(description = "合作商号")
    private String partnerId;

    @Schema(description = "负责人")
    private String keeper;

    @Schema(description = "公司")
    private String company;

    @Schema(description = "手机号")
    private String keeperphone;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

}
