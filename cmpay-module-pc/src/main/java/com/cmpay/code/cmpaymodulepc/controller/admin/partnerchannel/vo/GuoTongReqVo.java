package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GuoTongReqVo {
    @Schema(description = "国通通道")
    private String channel="guotong";
    @Schema(description = "国通机构号", example = "1053")
    private String guotongOrgNo;
    @Schema(description = "国通公钥")
    private String guotongUpPublicKey;
    @Schema(description = "国通机构appid", example = "1572")
    private String guotongAppid;
    @Schema(description = "国通机构appid密钥", example = "1572")
    private String guotongAppidse;
    @Schema(description = "国通国通机构appid、瑞银信accessid", example = "1572")
    private String guotongPartnerOther2;
    @Schema(description = "国通提现费率")
    private String guotongTxRate;
    @Schema(description = "国通保底费率")
    private BigDecimal guotongRate;
    @Schema(description = "国通结算周期", example = "1")
    private String guotongPaidoutType;
    @Schema(description = "国通微信渠道号")
    private String guotongWxChannelNo;
    @Schema(description = "国通支付宝PID")
    private String guotongAliChannelNo;
}
