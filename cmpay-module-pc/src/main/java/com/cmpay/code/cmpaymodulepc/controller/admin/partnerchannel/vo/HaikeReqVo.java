package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HaikeReqVo {
    @Schema(description = "海科通道")
    private String channel="haike";
    @Schema(description = "海科服务商编号", example = "12954")
    private String hkOrgId;

    @Schema(description = "海科accesskey")
    private String hkOrgKey;

    @Schema(description = "海科accessid", example = "29352")
    private String hkOrgAccessid;

    @Schema(description = "海科传输秘钥")
    private String hkPassKey;

    @Schema(description = "海科配置公众号appid", example = "29737")
    private String hkSweepAppid;

    @Schema(description = "海科配置公众号秘钥")
    private String hkSweepAppidKey;

    @Schema(description = "海科微信渠道号")
    private String hkWxChannelNo;

    @Schema(description = "海科支付宝渠道号")
    private String hkAliChannelNo;

    @Schema(description = "海科三方保底费率")
    private BigDecimal rateHaike;

    @Schema(description = "海科结算周期", example = "1")
    private String haikePaidoutType;
}
