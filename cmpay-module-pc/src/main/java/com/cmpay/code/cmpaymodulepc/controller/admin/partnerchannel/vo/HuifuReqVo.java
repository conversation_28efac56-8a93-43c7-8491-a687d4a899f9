package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HuifuReqVo {
    @Schema(description = "汇付")
    private String channel="huifu";

    @Schema(description = "汇付三方通道保底费率")
    private BigDecimal rateHuifu;

    @Schema(description = "汇付机构号", example = "1053")
    private String hfOrgId;

    @Schema(description = "汇付秘钥路径")
    private String hfOrgKeyPath;

    @Schema(description = "汇付秘钥密码")
    private String hfOrgKeyPassword;

    @Schema(description = "汇付代理商操作员", example = "28843")
    private String hfOrgOptellerid;

    @Schema(description = "汇付公众号appid", example = "21427")
    private String hfSweepAppid;

    @Schema(description = "汇付公众号appid秘钥")
    private String hfSweepAppidKey;
}
