package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HuluReqVo {
    @Schema(description = "瑞银信(H)通道")
    private String channel="hulu";

    @Schema(description = "瑞银信(H)机构号", example = "12832")
    private String huluOrgId;

    @Schema(description = "瑞银信(H)平台公钥")
    private String huluPlatformPublic;

    @Schema(description = "瑞银信(H)服务商私钥")
    private String huluPrivate;

    @Schema(description = "瑞银信(H)微信渠道号", example = "24159")
    private String huluWxChannelId;

    @Schema(description = "瑞银信(H)支付宝PID", example = "17679")
    private String huluAliChannelId;

    @Schema(description = "瑞银信(H)公众号appid", example = "32137")
    private String huluAppid;

    @Schema(description = "瑞银信(H)公众号appid密钥")
    private String huluAppidse;

    @Schema(description = "瑞银信(H)费率")
    private BigDecimal rateHulu;

    @Schema(description = "瑞银信(H)活动号")
    private String huluNd1ActivityIid;

    @Schema(description = "瑞银信(H)结算周期")
    private String huluPaidoutType;
}
