package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-08-23 11:49:33
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitChannelReqVo {

    @Schema(description = "省公司")
    private String branchId;

    @Schema(description = "市公司名称")
    private String company;

    @Schema(description = "是否报表：1-是；0-否；2-是否收支结算汇总表省外")
    private String isReport;

    @Schema(description = "除报表外不需要传；是否广东省：1-是；0-否")
    private String isGd;
}
