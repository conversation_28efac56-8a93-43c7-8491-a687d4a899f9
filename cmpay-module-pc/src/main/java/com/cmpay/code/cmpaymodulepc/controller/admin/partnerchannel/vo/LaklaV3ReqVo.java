package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LaklaV3ReqVo {
    //拉卡拉appid
    @Schema(description = "拉卡拉appid")
    private String LakalaV3OrgNo;
    //结算方式
    @Schema(description = "结算方式")
    private String LakalaV3PaidoutType;
    //保底费率
    @Schema(description = "保底费率")
    private BigDecimal rateLakalaV3;
    @Schema(description = "拉卡拉通道")
    private String channel="lakala_v3";
}
