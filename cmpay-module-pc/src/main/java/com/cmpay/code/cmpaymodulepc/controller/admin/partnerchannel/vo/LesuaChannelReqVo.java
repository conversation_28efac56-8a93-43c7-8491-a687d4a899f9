package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LesuaChannelReqVo {
    @Schema(description = "乐刷通道")
    private String channel="leshua";

    @Schema(description = "乐刷保底费率")
    private BigDecimal leshuaRate;

    @Schema(description = "乐刷机构号", example = "3346")
    private String lsOrgId;

    @Schema(description = "乐刷机构秘钥")
    private String lsOrgKey;

    @Schema(description = "乐刷公众号appid", example = "27752")
    private String lsSweepAppid;

    @Schema(description = "乐刷公众号appid秘钥")
    private String lsSweepAppidKey;
}
