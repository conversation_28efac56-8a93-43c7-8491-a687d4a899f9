package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RateChannelVo {
    @Schema(description = "乐刷保底费率")
    private BigDecimal leshuaRate;

    @Schema(description = "和融通普通300以下保底费率")
    private BigDecimal rateYirongma300down;

    @Schema(description = "和融通普通300以上保底费率")
    private BigDecimal rateYirongma300up;

    @Schema(description = "付费通直联保底费率")
    private BigDecimal rateFufeitong;

    @Schema(description = "付费通三方保底费率")
    private BigDecimal rateShoubei;

    @Schema(description = "随行付保底(旧)")
    private BigDecimal suixingfuRate;

    @Schema(description = "微信直联保底费率")
    private BigDecimal rateSub;

    @Schema(description = "支付宝直联保底费率")
    private BigDecimal rateSubalipay;

    @Schema(description = "嘉联通道的保底费率")
    private BigDecimal rateJialian;

    @Schema(description = "微信买单保底费率")
    private BigDecimal rateCloudpay;

    @Schema(description = "微信买单50以下返佣费率")
    private BigDecimal rateCloudpay50down;

    @Schema(description = "传化三方通道保底费率")
    private BigDecimal rateChuanhua;

    @Schema(description = "汇付三方通道保底费率")
    private BigDecimal rateHuifu;

    @Schema(description = "哆啦宝三方通道保底费率")
    private BigDecimal rateDuolabao;

    @Schema(description = "信汇三方通道保底费率")
    private BigDecimal rateXinhui;

    @Schema(description = "联动优势三方通道保底费率")
    private BigDecimal rateLiandong;

    @Schema(description = "瑞银信三方保底费率")
    private BigDecimal rateRuiyinxin;

    @Schema(description = "海科三方保底费率")
    private BigDecimal rateHaike;

    @Schema(description = "建行保底费率")
    private BigDecimal rateCcb;

    @Schema(description = "四九八通道保底费率")
    private BigDecimal rateSjb;

    @Schema(description = "徽商银行通道保底费率")
    private BigDecimal rateWftHs;

    @Schema(description = "付呗保底费率")
    private BigDecimal rateFubei;

    @Schema(description = "浦发银行通道保底费率")
    private BigDecimal ratePufabank;

    @Schema(description = "随行付通道保底费率")
    private BigDecimal rateSxfTq;

    @Schema(description = "平安银行通道保底费率")
    private BigDecimal ratePabank;

    @Schema(description = "通联三方通道保底费率")
    private BigDecimal rateTonglian;

    @Schema(description = "浙江农信通道保底费率")
    private BigDecimal rateZjnx;

    @Schema(description = "拉卡拉保底费率")
    private BigDecimal rateLakala;

    @Schema(description = "拉卡拉云MIS保底费率,单位千分之一")
    private BigDecimal rateLakalaMis;

    @Schema(description = "信汇S保底费率")
    private BigDecimal rateWangkebao;

    @Schema(description = "智付三方保底费率")
    private BigDecimal rateZhifu;

    @Schema(description = "现代金控保底费率")
    private BigDecimal rateXiandai;

    @Schema(description = "电信好码保底费率")
    private BigDecimal rateDianxin;

    @Schema(description = "数币三方间联通道费率")
    private BigDecimal rateYouzheng;

    @Schema(description = "数币直联通道费率")
    private BigDecimal rateYzPay;

    @Schema(description = "国通保底费率")
    private BigDecimal gtRate;

    @Schema(description = "银联保底费率")
    private BigDecimal ylswRate;

    @Schema(description = "瑞银信(H)费率")
    private BigDecimal rateHulu;

    @Schema(description = "抖音保底费率")
    private BigDecimal dyRate;

    @Schema(description = "微企付保底费率")
    private BigDecimal wqfRate;

    @Schema(description = "易生保底费率")
    private BigDecimal yiShengRate;

    @Schema(description = "lakala保底费率")
    private BigDecimal lakalaV3Rate;

}
