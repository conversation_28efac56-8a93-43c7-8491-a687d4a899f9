package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RyxChannelReqVo {
    @Schema(description = "瑞银信三方通道")
    private String channel="ruiyinxin";

    @Schema(description = "瑞银信三方保底费率")
    private BigDecimal rateRuiyinxin;

    @Schema(description = "瑞银信机构号", example = "26130")
    private String ryxOrgId;

    @Schema(description = "瑞银信私钥路径")
    private String ryxPrivateKeyPath;

    @Schema(description = "瑞银信公钥路径")
    private String ryxPublicKeyPath;

    @Schema(description = "瑞银信cooperator")
    private String ryxCooperator;

    @Schema(description = "瑞银信公众号appid", example = "30668")
    private String ryxSweepAppid;

    @Schema(description = "瑞银信公众号appid秘钥")
    private String ryxSweepAppidKey;

    @Schema(description = "瑞银信微信渠道号", example = "5529")
    private String ryxWxChannelId;

    @Schema(description = "瑞银信accessid：", example = "19527")
    private String ryxAccessId;

    @Schema(description = "瑞银信进件私钥")
    private String ryxSigninPrivateKeyPath;

    @Schema(description = "瑞银信进件公钥")
    private String ryxSigninPublicKeyPath;

    @Schema(description = "瑞银信支付宝PID", example = "31424")
    private String ryxAliChannelId;

    @Schema(description = "瑞银信结算周期", example = "1")
    private String ryxPaidoutType;
}
