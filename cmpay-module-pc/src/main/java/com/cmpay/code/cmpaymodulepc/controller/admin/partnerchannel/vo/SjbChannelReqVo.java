package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SjbChannelReqVo {
    @Schema(description = "四八九通道")
    private String channel="sjb";

    @Schema(description = "四九八服务商号", example = "8135")
    private String sjbOrgId;

    @Schema(description = "四九八微信渠道号", example = "3077")
    private String sjbChannelMchId;

    @Schema(description = "四九八支付宝渠道号", example = "29613")
    private String sjbAliChannelMchId;

    @Schema(description = "四九八进件秘钥")
    private String sjbSignKey;
}
