package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SxfChannelReqVo {
    @Schema(description = "随行付通道")
    private String channel="sxf_tq";

    @Schema(description = "随行付保底")
    private BigDecimal suixingfuRate;

    @Schema(description = "随行付机构秘钥")
    private String sxfOrgKey;

    @Schema(description = "随行付公众号appid", example = "24532")
    private String sxfSweepAppid;

    @Schema(description = "随行付公众号appid秘钥")
    private String sxfSweepAppidKey;

    @Schema(description = "随行付微信渠道号")
    private String sxfWxChannelNo;

    @Schema(description = "随行付支付宝PID")
    private String sxfAliChannelNo;

    @Schema(description = "随行付通道保底费率")
    private BigDecimal rateSxfTq;

    @Schema(description = "随行付机构号", example = "12903")
    private String sxfOrgId;
    @Schema(description = "随行付结算周期", example = "12903")
    private String sxfPaidoutType;
}
