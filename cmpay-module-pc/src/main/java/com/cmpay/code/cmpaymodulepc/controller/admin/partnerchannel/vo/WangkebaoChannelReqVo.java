package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WangkebaoChannelReqVo {
    @Schema(description = "信汇S通道")
    private String channel="wangkebao";

    @Schema(description = "信汇S保底费率")
    private BigDecimal rateWangkebao;

    @Schema(description = "信汇S服务商号")
    private String wkbOrgNo;

    @Schema(description = "信汇S服务商秘钥")
    private String wkbSignKey;

    @Schema(description = "信汇S微信渠道号", example = "22151")
    private String wkbWxChannelId;

    @Schema(description = "信汇S支付宝渠道号", example = "20590")
    private String wkbAliChannelId;

    @Schema(description = "信汇S进件地址", example = "https://www.iocoder.cn")
    private String wkbGatewayUrl;

    @Schema(description = "信汇SAppid", example = "20074")
    private String wkbAppid;

    @Schema(description = "信汇SAppid秘钥")
    private String wkbAppidSecert;
}
