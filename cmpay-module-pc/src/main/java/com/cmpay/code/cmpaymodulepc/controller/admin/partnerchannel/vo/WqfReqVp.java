package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WqfReqVp {
    @Schema(description = "微企付通道")
    private String channel="weiqifu";
    @Schema(description = "微企付机构号", example = "1053")
    private String wqfOrgNo;
    @Schema(description = "微企付保底费率")
    private BigDecimal wqfRate;
    @Schema(description = "微企付结算周期", example = "1")
    private String wqfPaidoutType;
}
