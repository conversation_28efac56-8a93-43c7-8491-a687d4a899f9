package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class XiandaiReqVo {
    @Schema(description = "通道")
    private String channel = "xiandai";

    @Schema(description = "现代金控保底费率")
    private BigDecimal rateXiandai;

    @Schema(description = "现代机构号", example = "25205")
    private String xiandaiOrgId;

    @Schema(description = "现代秘钥路径")
    private String xiandaiSignKey;

    @Schema(description = "现代解密秘钥路径")
    private String xiandaiPublicKey;

    @Schema(description = "现代秘钥密码")
    private String xiandaiPwd;

    @Schema(description = "现代Appid", example = "19616")
    private String xiandaiAppid;

    @Schema(description = "现代Appid秘钥")
    private String xiandaiAppidSecret;

    @Schema(description = "现代秘钥商户编号", example = "32116")
    private String xiandaiMerOrgId;

    @Schema(description = "现代创建人id", example = "29301")
    private String xiandaiCreateUserId;
}
