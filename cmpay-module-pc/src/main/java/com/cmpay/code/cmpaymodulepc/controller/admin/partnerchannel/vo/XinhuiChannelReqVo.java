package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class XinhuiChannelReqVo {
    @Schema(description = "通道")
    private String channel="xinhui";
    @Schema(description = "信汇保底")
    private BigDecimal rateXinhui;
    @Schema(description = "信汇机构号")
    private String xinhuiOrgNo;
    @Schema(description = "信汇服务商秘钥")
    private String xinhuiSignKey;
    @Schema(description = "信汇微信渠道号")
    private String xinhuiWxChannelId;
    @Schema(description = "信汇支付宝PID")
    private String xinhuiAliChannelId;
    @Schema(description = "信汇进件地址")
    private String xinhuiGatewayUrl;
    @Schema(description = "信汇appid")
    private String xinhuiAppid;
    @Schema(description = "信汇appid秘钥")
    private String xinhuiAppidSecert;

}
