package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @Date 2025/7/31 14:58
 * @version: 1.0
 */
@Data
public class YiShengResqVo {
    // 易生机构号
    @Schema(description = "易生机构号")
    private String yiShengOrgNo;
    //结算方式
    @Schema(description = "结算方式")
    private String yiShengPaidoutType;
    //保底费率
    @Schema(description = "保底费率")
    private BigDecimal rateYiSheng;
    //易生通道
    @Schema(description = "易生通道")
    private String channel="yisheng";
}
