package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class YirongmaChannelReqVo {
    @Schema(description = "和融通通道")
    private String channel="yirongma";

    @Schema(description = "和融通保底")
    private BigDecimal yirongmaRate;

    @Schema(description = "和融通业务员号", example = "19235")
    private String yirongmaUserId;

    @Schema(description = "和融通普通300以下保底费率")
    private BigDecimal rateYirongma300down;

    @Schema(description = "和融通普通300以上保底费率")
    private BigDecimal rateYirongma300up;

    @Schema(description = "和融通机构号", example = "28628")
    private String yrmOrgId;

    @Schema(description = "和融通机构秘钥")
    private String yrmOrgKey;

    @Schema(description = "和融通品牌")
    private String yirongmaBrand;
}
