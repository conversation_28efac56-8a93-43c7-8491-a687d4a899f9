package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class YlswReqVo {
    @Schema(description = "银联通道")
    private String channel="ylsw";
    @Schema(description = "银联机构号", example = "1053")
    private String ylswOrgNo;
    @Schema(description = "银联公钥")
    private String ylswUpPublicKey;
    @Schema(description = "银联私钥")
    private String ylswPrivateKey;
    @Schema(description = "银联机构appid", example = "1572")
    private String ylswAppid;
    @Schema(description = "银联机构appid密钥", example = "1572")
    private String ylswAppidse;
    @Schema(description = "银联提现费率")
    private String ylswTxRate;
    @Schema(description = "银联保底费率")
    private BigDecimal ylswRate;
    @Schema(description = "银联结算周期", example = "1")
    private String ylswPaidoutType;
    @Schema(description = "银联微信渠道号")
    private String ylswWxChannelNo;
    @Schema(description = "银联支付宝PID")
    private String ylswAliChannelNo;
}

