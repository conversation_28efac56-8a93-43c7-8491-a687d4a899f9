package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class YplChannelReqVo {
    @Schema(description = "易票联通道")
    private String channel="ypl";

    @Schema(description = "易票联机构号")
    private String yplOrgId;

    @Schema(description = "易票联平台公钥")
    private String yplPlatformPublic;

    @Schema(description = "易票联商户私钥（机构共用一套）")
    private String yplPrivate;

    @Schema(description = "易票联商户私钥密码")
    private String yplPrivatePwd;

    @Schema(description = "易票联商户私钥密码")
    private String yplSignNo;

    @Schema(description = "易票联保底费率")
    private BigDecimal rateYpl;
}
