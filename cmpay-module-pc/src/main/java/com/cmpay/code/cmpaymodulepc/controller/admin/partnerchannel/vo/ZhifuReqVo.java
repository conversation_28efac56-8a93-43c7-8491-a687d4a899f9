package com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZhifuReqVo {
    @Schema(description = "通道")
    private String channel = "zhifu";

    @Schema(description = "智付三方保底费率")
    private BigDecimal rateZhifu;

    @Schema(description = "智付平台机构号", example = "16090")
    private String zhifuOrgId;

    @Schema(description = "智付签约秘钥")
    private String zhifuOrgKey;

    @Schema(description = "智付微信渠道号")
    private String zhifuWxMch;

    @Schema(description = "智付支付宝渠道号")
    private String zhifuAliMch;

    @Schema(description = "智付appid", example = "3390")
    private String zhifuAppid;

    @Schema(description = "智付app开发者秘钥")
    private String zhifuAppKey;
}
