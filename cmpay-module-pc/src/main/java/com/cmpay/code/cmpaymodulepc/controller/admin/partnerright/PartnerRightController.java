package com.cmpay.code.cmpaymodulepc.controller.admin.partnerright;

import com.cmpay.code.cmpaymodulepc.controller.admin.partnerright.vo.DistRespVO;
import com.cmpay.code.cmpaymodulepc.service.partnerright.PartnerRightService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "管理后台 - 渠道权限信息")
@RestController
@RequestMapping("/partner/right")
@Validated
public class PartnerRightController {

    @Resource
    private PartnerRightService partnerRightService;

    @GetMapping("/get-dist-by-partnerId")
    @Operation(summary = "商户公共池——获取客户经理所在市的所有区县")
    public CommonResult<List<DistRespVO>> getDistByPartnerId(@RequestParam("partnerId") String partnerId) {
        return CommonResult.success(partnerRightService.getDistByPartnerId(partnerId));
    }
}
