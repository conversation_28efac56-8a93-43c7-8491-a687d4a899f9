package com.cmpay.code.cmpaymodulepc.controller.admin.partnerright.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024-01-31 9:54:19
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistRespVO {

    @Schema(description = "地市名称")
    private String dist;

    @Schema(description = "区域编码")
    private String areaCode;

    @Schema(description = "唯一id")
    private String id;

    @Schema(description = "市级")
    private String city;
}
