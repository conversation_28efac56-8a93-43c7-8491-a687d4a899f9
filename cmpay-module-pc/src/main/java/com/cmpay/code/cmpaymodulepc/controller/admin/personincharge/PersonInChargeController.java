package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge;

import com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.personincharge.PersonInChargeConvert;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.personincharge.PersonInChargeDO;
import com.cmpay.code.cmpaymodulepc.service.personincharge.PersonInChargeService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 门店店长")
@RestController
@RequestMapping("/personincharge/person-in-charge")
@Validated
public class PersonInChargeController {

    @Resource
    private PersonInChargeService personInChargeService;

    @PostMapping("/create")
    @Operation(summary = "创建门店店长")
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:create')")
    public CommonResult<Integer> createPersonInCharge(@Valid @RequestBody PersonInChargeCreateReqVO createReqVO) {
        return success(personInChargeService.createPersonInCharge(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店店长")
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:update')")
    public CommonResult<Boolean> updatePersonInCharge(@Valid @RequestBody PersonInChargeUpdateReqVO updateReqVO) {
        personInChargeService.updatePersonInCharge(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店店长")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:delete')")
    public CommonResult<Boolean> deletePersonInCharge(@RequestParam("id") Integer id) {
        personInChargeService.deletePersonInCharge(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店店长")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:query')")
    public CommonResult<PersonInChargeRespVO> getPersonInCharge(@RequestParam("id") Integer id) {
        PersonInChargeDO personInCharge = personInChargeService.getPersonInCharge(id);
        return success(PersonInChargeConvert.INSTANCE.convert(personInCharge));
    }

    @GetMapping("/list")
    @Operation(summary = "获得门店店长列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:query')")
    public CommonResult<List<PersonInChargeRespVO>> getPersonInChargeList(@RequestParam("ids") Collection<Integer> ids) {
        List<PersonInChargeDO> list = personInChargeService.getPersonInChargeList(ids);
        return success(PersonInChargeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店店长分页")
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:query')")
    public CommonResult<PageResult<PersonInChargeRespVO>> getPersonInChargePage(@Valid PersonInChargePageReqVO pageVO) {
        PageResult<PersonInChargeDO> pageResult = personInChargeService.getPersonInChargePage(pageVO);
        return success(PersonInChargeConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出门店店长 Excel")
    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:export')")
    @OperateLog(type = EXPORT)
    public void exportPersonInChargeExcel(@Valid PersonInChargeExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PersonInChargeDO> list = personInChargeService.getPersonInChargeList(exportReqVO);
        // 导出 Excel
        List<PersonInChargeExcelVO> datas = PersonInChargeConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "门店店长.xls", "数据", PersonInChargeExcelVO.class, datas);
    }

//    @GetMapping("/simple-list")
//    @Operation(summary = "获得门店店长下拉")
////    @PreAuthorize("@ss.hasPermission('personincharge:person-in-charge:query')")
//    public CommonResult<List<PersonInChargeSimpleRespVO>> getSimpleList(@RequestParam("shopId") String shopId){
//        return success(personInChargeService.getSimpleList(shopId));
//    }

}
