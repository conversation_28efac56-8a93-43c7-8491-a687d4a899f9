package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 门店店长 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PersonInChargeBaseVO {

    @Schema(description = "用户id", example = "30452")
    private String xmid;

    @Schema(description = "1:正常；2：停用", example = "2")
    private Integer status;

    @Schema(description = "商户号", example = "1596")
    private String shopId;

    @Schema(description = "门店id", example = "3440")
    private Long deviceId;

}
