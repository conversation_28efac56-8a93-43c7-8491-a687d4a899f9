package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 门店店长 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PersonInChargeExcelVO {

    @ExcelProperty("主键id")
    private Integer id;

    @ExcelProperty("用户id")
    private String xmid;

    @ExcelProperty("1:正常；2：停用")
    private Integer status;

    @ExcelProperty("商户号")
    private String shopId;

    @ExcelProperty("门店id")
    private Long deviceId;

}
