package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 门店店长 Excel 导出 Request VO，参数和 PersonInChargePageReqVO 是一致的")
@Data
public class PersonInChargeExportReqVO {

    @Schema(description = "用户id", example = "30452")
    private String xmid;

    @Schema(description = "1:正常；2：停用", example = "2")
    private Integer status;

    @Schema(description = "商户号", example = "1596")
    private String shopId;

    @Schema(description = "门店id", example = "3440")
    private Long deviceId;

}
