package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 门店店长分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonInChargePageReqVO extends PageParam {

    @Schema(description = "用户id", example = "30452")
    private String xmid;

    @Schema(description = "1:正常；2：停用", example = "2")
    private Integer status;

    @Schema(description = "商户号", example = "1596")
    private String shopId;

    @Schema(description = "门店id", example = "3440")
    private Long deviceId;

}
