package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 门店店长 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonInChargeRespVO extends PersonInChargeBaseVO {

    @Schema(description = "主键id", required = true, example = "4132")
    private Integer id;

}
