package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 门店店长下拉 Resopnse VO")
@Data
@ToString(callSuper = true)
public class PersonInChargeSimpleRespVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "xmid")
    private String xmid;

    @Schema(description = "门店id")
    private String deviceId;

}
