package com.cmpay.code.cmpaymodulepc.controller.admin.personincharge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 门店店长更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonInChargeUpdateReqVO extends PersonInChargeBaseVO {

    @Schema(description = "主键id", required = true, example = "4132")
    @NotNull(message = "主键id不能为空")
    private Integer id;

}
