package com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders;

import com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo.ProfitShareOrdersVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo.StatisticsProfitShareOrdersRespVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo.searchProfitShareOrdersReqVo;
import com.cmpay.code.cmpaymodulepc.service.profitshareorders.ProfitShareOrdersService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@Tag(name = "管理后台 - 分账订单")
@RestController
@RequestMapping("/profit/share-orders")
@Validated
public class ProfitShareOrdersController {

    @Resource
    private ProfitShareOrdersService profitShareOrdersService;

    @PostMapping("/searchProfitShareOrders")
    @Operation(summary = "订单分账信息查询")
    @PreAuthorize("@ss.hasAnyPermissions('charge:orders:query','sub-ledger:profit-share-orders:query')")
    public CommonResult<PageResult<ProfitShareOrdersVo>> searchProfitShareOrders(@RequestBody searchProfitShareOrdersReqVo profitShareOrders) {
        return profitShareOrdersService.searchProfitShareOrders(profitShareOrders);
    }
    @PostMapping("/initProfitShareOrders")
    @Operation(summary = "初始化分账订单的大商户号")
    public CommonResult<String> initProfitShareOrders(){
        return profitShareOrdersService.initProfitShareOrders();
    }

    @PostMapping("/statisticsProfitShareOrders")
    @Operation(summary = "订单分账信息统计")
    @PreAuthorize("@ss.hasAnyPermissions('charge:orders:query','sub-ledger:profit-share-orders:query')")
    public CommonResult<StatisticsProfitShareOrdersRespVo> statisticsProfitShareOrders(@RequestBody searchProfitShareOrdersReqVo profitShareOrders) {
        return profitShareOrdersService.statisticsProfitShareOrders(profitShareOrders);
    }

    @PostMapping("/exportProfitShareOrdersExcel")
    @Operation(summary = "商户导出订单分账Excel")
    @PreAuthorize("@ss.hasAnyPermissions('charge:orders:export','sub-ledger:profit-share-orders:export')")
    public void exportOrdersExcel(HttpServletResponse response, @RequestBody searchProfitShareOrdersReqVo profitShareOrders) {
        profitShareOrdersService.getProfitShareOrdersExcel(profitShareOrders, response);
    }
}
