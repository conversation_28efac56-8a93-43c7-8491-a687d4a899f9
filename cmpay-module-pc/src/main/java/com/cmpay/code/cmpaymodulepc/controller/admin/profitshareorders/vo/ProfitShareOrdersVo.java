package com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-16 11:33:38
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfitShareOrdersVo {

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "分账订单号")
    private String psOrderId;

    @Schema(description = "分账商户号")
    private String shopId;

    @Schema(description = "分账短商户号")
    private String trueid;

    @Schema(description = "分账商户名称")
    private String shopName;

    @Schema(description = "被分账商户号")
    private String psShopId;

    @Schema(description = "被分账商户号")
    private String psTrueid;

    @Schema(description = "被分账商户名称")
    private String psShopName;

    @Schema(description = "订单金额")
    private Double orderMoney;

    @Schema(description = "分账金额")
    private Double psMoney;

    @Schema(description = "订单状态")
    private String psOrderStatus;

    @Schema(description = "订单状态")
    private String psOrderStatusStr;

    @Schema(description = "时间")
    private String psEndTime;

    @Schema(description = "通道")
    private String channel;

    @Schema(description = "通道字符串")
    private String channelStr;

    @Schema(description = "分账类型")
    private String psType;

    @Schema(description = "分账类型字符串")
    private String psTypeStr;
}
