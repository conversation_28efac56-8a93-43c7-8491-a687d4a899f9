package com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-16 11:24:20
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsProfitShareOrdersRespVo {

    @Schema(description = "出账商户数量")
    private int shopCount;

    @Schema(description = "入账交易数量")
    private int psShopCount;

    @Schema(description = "出账交易额")
    private Double moneySum;

    @Schema(description = "出/入账笔数")
    private Long ordersCount;

    @Schema(description = "入账交易额")
    private Double psMoneySum;

}
