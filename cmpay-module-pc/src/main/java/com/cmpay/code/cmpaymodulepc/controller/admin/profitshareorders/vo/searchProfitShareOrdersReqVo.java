package com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-16 11:17:32
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class searchProfitShareOrdersReqVo extends PageParam {

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "分账订单id")
    private String psOrderId;

    @Schema(description = "订单id")
    private String orderId;

    @Schema(description = "分账订单状态：0-失败  1-成功")
    private String psOrderStatus;

    @Schema(description = "商户id")
    private String shopId;

    @Schema(description = "平台id")
    private Integer platform;

    @Schema(description = "大商户id")
    private String agentSubId;
}
