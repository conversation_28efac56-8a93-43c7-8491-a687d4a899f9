package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage;

import com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo.RyxRiskMerRespVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo.RyxRiskMerSubmitReqVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo.SearchRiskManageMerchantReqVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo.SearchRiskManageMerchantRespVO;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.riskmanage.RiskManageDO;
import com.cmpay.code.cmpaymodulepc.service.riskmanage.RiskManageService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 商户风控管理")
@RestController
@RequestMapping(value = "/risk/manage")
@Validated
public class RiskManageController {

    @Resource
    private RiskManageService riskManageService;

    @PostMapping("/searchRiskManageMerchant")
    @Operation(summary = "风险管理——商户风控管理——分页查询")
    public CommonResult<PageResult<SearchRiskManageMerchantRespVO>> searchRiskManageMerchant(@RequestBody SearchRiskManageMerchantReqVO riskManageMerchantVO) {
        PageResult<SearchRiskManageMerchantRespVO> pageResult = riskManageService.searchRiskManageMerchant(riskManageMerchantVO);
        return success(pageResult);
    }

    @PostMapping("/exportRiskManageMerchantExcel")
    @Operation(summary = "风险管理——商户风控管理——导出订单投诉管理Excel")
    //@PreAuthorize("@ss.hasPermission('charge:orders:export')")
    public void exportRiskManageMerchantExcel(HttpServletResponse response, @RequestBody SearchRiskManageMerchantReqVO riskManageMerchantVO) {
        riskManageService.exportRiskManageMerchantExcel(riskManageMerchantVO, response);
    }

    @PostMapping("/saveOrUpdateRiskManageMerchant")
    @Operation(summary = "风险管理——商户风控管理——添加或修改新风控")
    public CommonResult<Boolean> saveOrUpdateRiskManageMerchant(@RequestBody RiskManageDO riskManageDO) {
        riskManageService.saveOrUpdateRiskManageMerchant(riskManageDO);
        return success(true);
    }

    @PostMapping("/updateRiskManageMerchant")
    @Operation(summary = "风险管理——商户风控管理——备注按钮")
    public CommonResult<Boolean> updateRiskManageMerchant(@RequestBody RiskManageDO riskManageDO) {
        riskManageService.updateRiskMerchantRemark(riskManageDO);
        return success(true);
    }

    @DeleteMapping("/deleteRiskManageMerchant/{riskManageId}")
    @Operation(summary = "风险管理——商户风控管理——删除按钮")
    public CommonResult<Boolean> deleteRiskMerchantMsg(@PathVariable("riskManageId") Integer riskManageId) {
        riskManageService.deleteRiskManageMerchant(riskManageId);
        return success(true);
    }

    // TODO 这个功能需要完善没写完
    @GetMapping("/searchRyxRiskMerByRequestId")
    @Operation(summary = "风险管理——商户风控管理——获取瑞银信风控信息按钮")
    public CommonResult<RyxRiskMerRespVO> searchRyxRiskMerByRequestId(@RequestParam("requestId") String requestId, @RequestParam("shopId") String shopId) {
        return success(riskManageService.searchRyxRiskMerByRequestId(requestId,shopId));
    }

    @PostMapping("/ryx-risk-mer-submit")
    @Operation(summary = "风险管理——商户风控管理——瑞银信风控信息提交")
    public CommonResult submitposblack(@RequestBody RyxRiskMerSubmitReqVO ryxRiskMerSubmitReqVO){
        return riskManageService.submitposblack(ryxRiskMerSubmitReqVO);
    }
}

