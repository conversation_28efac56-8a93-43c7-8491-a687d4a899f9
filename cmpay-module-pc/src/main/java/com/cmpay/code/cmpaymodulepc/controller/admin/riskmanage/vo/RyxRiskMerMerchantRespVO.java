package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
//商户资料
public class RyxRiskMerMerchantRespVO {

    @Schema(description = "收银台")
    private String checkOut;

    @Schema(description = "门头照")
    private String doorHead;

    @Schema(description = "营业执照")
    private String businessLicense;

    @Schema(description = "经营场所")
    private String inStoreProduct;

    @Schema(description = "收银台(是否必填)")
    private Integer checkOutRight = 0;

    @Schema(description = "门头照(是否必填)")
    private Integer doorHeadRight = 0;

    @Schema(description = "营业执照(是否必填)")
    private Integer businessLicenseRight = 0;

    @Schema(description = "经营场所(是否必填)")
    private Integer inStoreProductRight = 0;
}
