package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
//其他资料
public class RyxRiskMerOtherRespVO {

    @Schema(description = "持卡人手持支付声明")
    private String settlerPayStatement;

    @Schema(description = "担保函或解冻申请表")
    private String unfreezingApplicationForm;

    @Schema(description = "手持身份证")
    private String holdingIdcard;

    @Schema(description = "手持营业执照")
    private String holdingBusinessLicense;

    @Schema(description = "其他资料")
    private String other;

    @Schema(description = "手持营业执照(是否必填)")
    private Integer holdingBusinessLicenseRight = 0;

    @Schema(description = "手持身份证(是否必填)")
    private Integer holdingIdcardRight = 0;

    @Schema(description = "持卡人手持支付声明(是否必填)")
    private Integer settlerPayStatementRight = 1;

    @Schema(description = "担保函或解冻申请表(是否必填)")
    private Integer unfreezingApplicationFormRight = 1;

    @Schema(description = "其他资料(是否必填)")
    private Integer otherRight = 1;
}
