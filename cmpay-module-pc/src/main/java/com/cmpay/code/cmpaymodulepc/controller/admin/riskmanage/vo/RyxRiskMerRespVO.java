package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RyxRiskMerRespVO {

    @Schema(description = "文本资料")
    private RyxRiskMerTxtRespVO ryxRiskMerTextRespVO;

    @Schema(description = "结算资料")
    private RyxRiskMerSettlementRespVO ryxRiskMerSettlementRespVO;

    @Schema(description = "商户资料")
    private RyxRiskMerMerchantRespVO ryxRiskMerMerchantRespVO;

    @Schema(description = "交易相关")
    private RyxRiskMerTransactionRespVO ryxRiskMerTransactionRespVO;

    @Schema(description = "其他资料")
    private RyxRiskMerOtherRespVO ryxRiskMerOtherRespVO;
}
