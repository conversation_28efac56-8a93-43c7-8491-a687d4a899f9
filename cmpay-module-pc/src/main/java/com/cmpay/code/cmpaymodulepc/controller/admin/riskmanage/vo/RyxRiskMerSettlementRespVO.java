package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
//结算人资料
public class RyxRiskMerSettlementRespVO {

    @Schema(description = "结算人身份证人像面")
    private String settlerIdcardFront;

    @Schema(description = "结算人身份证国徽面")
    private String settlerIdcardBack;

    @Schema(description = "银行卡正面")
    private String cardFront;

    @Schema(description = "银行卡背面")
    private String cardBack;

    @Schema(description = "结算人身份证人像面(是否必填)")
    private Integer settlerIdcardFrontRight = 1;

    @Schema(description = "结算人身份证国徽面(是否必填)")
    private Integer settlerIdcardBackRight = 1;

    @Schema(description = "银行卡正面(是否必填)")
    private Integer cardFrontRight = 1;

    @Schema(description = "银行卡背面(是否必填)")
    private Integer cardBackRight = 1;
}
