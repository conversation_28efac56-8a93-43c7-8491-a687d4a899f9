package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RyxRiskMerSubmitReqVO {

    @NotNull(message = "商户号不能为空")
    @Schema(description = "商户号")
    private String shopId;

    @NotNull(message = "请求id不能为空")
    @Schema(description = "请求id")
    private String requestId;

    @Schema(description = "结算人身份证人像面")
    private String settlerIdcardFront;

    @Schema(description = "结算人身份证国徽面")
    private String settlerIdcardBack;

    @Schema(description = "银行卡正面")
    private String cardFront;

    @Schema(description = "银行卡背面")
    private String cardBack;

    @Schema(description = "交易凭证")
    private String transactionReceipt;

    @Schema(description = "店内商品价目表")
    private String priceList;

    @Schema(description = "收据")
    private String receipt;

    @Schema(description = "持卡人手持支付声明")
    private String settlerPayStatement;

    @Schema(description = "担保函或解冻申请表")
    private String unfreezingApplicationForm;

    @Schema(description = "其他资料")
    private String other;

    @NotNull(message = "收银台不能为空")
    @Schema(description = "收银台")
    private String checkOut;

    @NotNull(message = "门头照不能为空")
    @Schema(description = "门头照")
    private String doorHead;

    @NotNull(message = "手持身份证不能为空")
    @Schema(description = "手持身份证")
    private String holdingIdcard;

    @NotNull(message = "经营场所不能为空")
    @Schema(description = "经营场所")
    private String inStoreProduct;

    @NotNull(message = "营业执照不能为空")
    @Schema(description = "营业执照")
    private String businessLicense;

    @NotNull(message = "手持营业执照不能为空")
    @Schema(description = "手持营业执照")
    private String holdingBusinessLicense;

    @NotNull(message = "经营范围说明不能为空")
    @Schema(description = "经营范围说明")
    private String manaScop;

    @NotNull(message = "交易场景说明不能为空")
    @Schema(description = "交易场景说明")
    private String tranScop;

    @Schema(description = "是否致电银行")
    private String isPhone;

    @Schema(description = "承认交易时间")
    private String bankTime;

    @Schema(description = "经营行业说明")
    private String manaIndustry;

    @Schema(description = "调单原因说明")
    private String singleReason;

    @Schema(description = "退单原因说明")
    private String backReason;
}
