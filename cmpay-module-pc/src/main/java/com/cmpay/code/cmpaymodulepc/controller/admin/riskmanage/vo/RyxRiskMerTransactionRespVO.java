package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
//交易相关
public class RyxRiskMerTransactionRespVO {

    @Schema(description = "交易凭证")
    private String transactionReceipt;

    @Schema(description = "店内商品价目表")
    private String priceList;

    @Schema(description = "收据")
    private String receipt;

    @Schema(description = "交易凭证(是否必填)")
    private Integer transactionReceiptRight = 1;

    @Schema(description = "店内商品价目表(是否必填)")
    private Integer priceListRight = 1;

    @Schema(description = "收据(是否必填)")
    private Integer receiptRight = 1;
}
