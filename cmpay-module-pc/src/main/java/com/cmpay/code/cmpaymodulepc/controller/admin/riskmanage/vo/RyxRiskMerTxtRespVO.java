package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
//文本资料
public class RyxRiskMerTxtRespVO {

    @Schema(description = "经营范围说明")
    private String manaScop;

    @Schema(description = "交易场景说明")
    private String tranScop;

    @Schema(description = "是否致电银行")
    private String isPhone;

    @Schema(description = "承认交易时间")
    private String bankTime;

    @Schema(description = "经营行业说明")
    private String manaIndustry;

    @Schema(description = "调单原因说明")
    private String singleReason;

    @Schema(description = "退单原因说明")
    private String backReason;

    @Schema(description = "经营范围说明(是否必填)")
    private Integer manaScopRight = 0;

    @Schema(description = "交易场景说明(是否必填)")
    private Integer tranScopRight = 0;

    @Schema(description = "是否致电银行(是否必填)")
    private Integer isPhoneRight = 1;

    @Schema(description = "承认交易时间(是否必填)")
    private Integer bankTimeRight = 1;

    @Schema(description = "经营行业说明(是否必填)")
    private Integer manaIndustryRight = 1;

    @Schema(description = "调单原因说明(是否必填)")
    private Integer singleReasonRight = 1;

    @Schema(description = "退单原因说明(是否必填)")
    private Integer backReasonRight = 1;

}
