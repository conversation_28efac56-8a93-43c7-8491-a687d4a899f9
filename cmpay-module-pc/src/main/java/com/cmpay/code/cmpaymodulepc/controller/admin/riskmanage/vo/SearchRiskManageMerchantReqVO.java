package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 风险管理——订单投诉管理——分页查询请求
 * @date 2023-08-02 11:41:00
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchRiskManageMerchantReqVO extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "风控原因")
    private String riskType;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "省级id")
    private String branchId;

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "风控处理状态")
    private String complaintHandleState;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "平台")
    private Integer platform;

    @Schema(description = "风控来源")
    private String source;
}
