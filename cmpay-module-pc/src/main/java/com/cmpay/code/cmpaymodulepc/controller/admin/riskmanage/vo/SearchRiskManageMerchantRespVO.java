package com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 风险管理——订单投诉管理——分页查询返回
 * @date 2023-08-02 11:46:52
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchRiskManageMerchantRespVO {

    @Schema(description = "商户风控id")
    private String riskManageId;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "风控原因")
    private String riskType;

    @Schema(description = "风控详情")
    private String riskReason;

    @Schema(description = "风控来源")
    private String source;

    @Schema(description = "风控来源字符串")
    private String sourceStr;

    @Schema(description = "冻结资金")
    private BigDecimal frozenAmount;

    @Schema(description = "风控下发时间")
    private String riskTime;

    @Schema(description = "反馈时效")
    private String feedbackTimeliness;

    @Schema(description = "风控处理状态码")
    private String complaintHandleState;

    @Schema(description = "风控处理状态字符串")
    private String complaintHandleStateStr;

    @Schema(description = "业务员id")
    private String ywyOpenid;

    @Schema(description = "业务员名称")
    private String ywyName;

    @Schema(description = "通道")
    private String channel;

    @Schema(description = "通道")
    private String channelStr;

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "营业点名称")
    private String acceptName;

    @Schema(description = "区县名称")
    private String agentName;

    @Schema(description = "市级名称")
    private String partnerName;

    @Schema(description = "省级名称")
    private String branchName;

    @Schema(description = "申诉要求")
    private String appealRequest;

    @Schema(description = "微信商户号")
    private String merchantOther3;

    @Schema(description = "渠道号")
    private String channelMchId;

    @Schema(description = "备注")
    private String remarksMsg;

    @Schema(description = "请求id")
    private String requestId;
}
