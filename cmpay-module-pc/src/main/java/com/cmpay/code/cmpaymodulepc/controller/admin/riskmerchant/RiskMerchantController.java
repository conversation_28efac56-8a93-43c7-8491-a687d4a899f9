package com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant;

import com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo.*;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.riskmerchant.RiskMerchantDO;
import com.cmpay.code.cmpaymodulepc.service.riskmerchant.RiskMerchantService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 商户风险")
@RestController
@RequestMapping("/risk/merchant")
@Validated
public class RiskMerchantController {

    @Resource
    private RiskMerchantService riskMerchantService;

    @PostMapping("/searchRiskMerchant")
    @Operation(summary = "风险管理——订单投诉管理——分页查询")
    //@PreAuthorize("@ss.hasPermission('partner:setting:create')")
    public CommonResult<PageResult<SearchRiskMerchantRespVo>> searchRiskMerchant(@RequestBody SearchRiskMerchantReqVo riskMerchantReqVo) {
        PageResult<SearchRiskMerchantRespVo> pageResult = riskMerchantService.searchRiskMerchant(riskMerchantReqVo);
        return success(pageResult);
    }

    @PostMapping("/exportRiskMerchantExcel")
    @Operation(summary = "风险管理——订单投诉管理——导出订单投诉管理Excel")
    //@PreAuthorize("@ss.hasPermission('charge:orders:export')")
    public void exportRiskMerchantExcel(HttpServletResponse response, @RequestBody SearchRiskMerchantReqVo riskMerchantReqVo) {
        riskMerchantService.exportRiskMerchantExcel(riskMerchantReqVo, response);
    }

    @PostMapping("/saveOrUpdateRiskMerchant")
    @Operation(summary = "风险管理——订单投诉管理——添加或修改新投诉")
    public CommonResult<Boolean> saveOrUpdateRiskMerchant(@RequestBody RiskMerchantVO riskMerchantVO) {
        riskMerchantService.saveOrUpdateRiskMerchant(riskMerchantVO);
        return success(true);
    }

    @PostMapping("/updateRiskMerchantRemark")
    @Operation(summary = "风险管理——订单投诉管理——备注按钮")
    public CommonResult<Boolean> updateRiskMerchantRemark(@RequestBody RiskMerchantDO riskMerchantDO) {
        riskMerchantService.updateRiskMerchantRemark(riskMerchantDO);
        return success(true);
    }

    @GetMapping("/deleteRiskMerchantRemark/{riskId}")
    @Operation(summary = "风险管理——订单投诉管理——删除按钮")
    public CommonResult<Boolean> deleteRiskMerchantMsg(@PathVariable("riskId") Integer riskId) {
        riskMerchantService.deleteRiskMerchantMsg(riskId);
        return success(true);
    }

    @PostMapping("/uploadOssRegFile")
    @Operation(summary = "上传oss_reg的图片接口")
    public CommonResult<String> uploadOssRegFile(UploadOssRegFileReqVo uploadOssRegFileReqVo) {
        return success(riskMerchantService.uploadOssRegFile(uploadOssRegFileReqVo));
    }

    @PostMapping("/commitRiskControlResponse")
    @Operation(summary = "风险管理——订单投诉管理——提交按钮")
    public CommonResult<String> commitRiskControlResponse(@RequestBody @Validated CommitRiskControlResponseReqVo commitRiskControlResponse) {
        return success(riskMerchantService.commitRiskControlResponse(commitRiskControlResponse));
    }
}
