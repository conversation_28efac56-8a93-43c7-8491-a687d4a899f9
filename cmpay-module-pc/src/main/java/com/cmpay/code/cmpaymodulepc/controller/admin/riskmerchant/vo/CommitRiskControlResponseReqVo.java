package com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-11-03 16:10:12
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommitRiskControlResponseReqVo {

    @Schema(description = "投诉渠道")
    @NotNull(message = "投诉渠道不能为空")
    private String channel;

    @Schema(description = "商户号")
    @NotNull(message = "商户号不能为空")
    private String shopId;

    @Schema(description = "投诉订单号")
    @NotNull(message = "投诉订单号不能为空")
    private String orderId;

    @Schema(description = "渠道号")
    private String channelMchId;

    @Schema(description = "投诉来源")
    @NotNull(message = "投诉来源不能为空")
    private String riskType;

    @Schema(description = "回复内容")
    @NotNull(message = "回复内容不能为空")
    private String riskContext;

    @Schema(description = "图片路径")
    private String files;
}
