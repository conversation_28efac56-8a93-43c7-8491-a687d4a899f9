package com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-11-03 17:02:55
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskMerchantVO {

    @Schema(description = "id")
    private Integer riskId;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "投诉通道")
    private String channel;

    @Schema(description = "投诉来源")
    private String riskType;

    @Schema(description = "投诉处理状态")
    private String complaintHandleState;

    @Schema(description = "付款人电话")
    private String payerPhone;

    @Schema(description = "评论内容")
    private String remarksMsg;

    @Schema(description = "投诉详情")
    private String riskReason;

    @Schema(description = "投诉时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date riskTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "插入时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND,timezone = "GMT+8")
    private Date insertTime;

    @Schema(description = "风险内容")
    private String riskContext;

    @Schema(description = "风险图片")
    private String riskImages;

}
