package com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 风险管理——订单投诉管理——分页查询请求
 * @date 2023-06-19 17:22:21
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchRiskMerchantReqVo extends PageParam {

    @Schema(description = "投诉来源")
    private String riskType;

    @Schema(description = "投诉状态")
    private String complaintHandleState;

    @Schema(description = "投诉订单号")
    private String orderId;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "平台")
    private Integer platform;

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "省id")
    private String branchId;

    @Schema(description = "市id")
    private String partnerId;
}
