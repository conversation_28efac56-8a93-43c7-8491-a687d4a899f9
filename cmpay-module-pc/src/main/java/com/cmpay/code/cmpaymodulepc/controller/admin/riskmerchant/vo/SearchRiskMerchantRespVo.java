package com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 风险管理——订单投诉管理——分页查询返回
 * @date 2023-06-19 16:53:18
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchRiskMerchantRespVo {

    @Schema(description = "投诉订单号")
    private String orderId;

    @Schema(description = "投诉商户id")
    private String riskId;

    @Schema(description = "投诉来源")
    private String riskType;

    @Schema(description = "投诉人信息")
    private String payerPhone;

    @Schema(description = "投诉渠道")
    private String riskChannel;

    @Schema(description = "投诉渠道对应值")
    private String riskChannelStr;

    @Schema(description = "渠道号")
    private String channelMchId;

    @Schema(description = "投诉原因")
    private String riskReason;

    @Schema(description = "投诉时间")
    private String riskTime;

    @Schema(description = "投诉处理状态")
    private String complaintHandleState;

    @Schema(description = "投诉处理状态对应值")
    private String complaintHandleStateStr;

    @Schema(description = "申诉要求")
    private String treatmentProcess;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "业务员")
    private String ywyOpenid;

    @Schema(description = "业务员名称")
    private String ywyName;

    @Schema(description = "营业点id")
    private String acceptId;

    @Schema(description = "营业点名称")
    private String acceptName;

    @Schema(description = "区县名称")
    private String agentName;

    @Schema(description = "市级名称")
    private String partnerName;

    @Schema(description = "省级名称")
    private String branchName;

    @Schema(description = "备注")
    private String remarksMsg;

    @Schema(description = "提交图片信息")
    private String riskImages;

    @Schema(description = "回复")
    private String riskContext;

}
