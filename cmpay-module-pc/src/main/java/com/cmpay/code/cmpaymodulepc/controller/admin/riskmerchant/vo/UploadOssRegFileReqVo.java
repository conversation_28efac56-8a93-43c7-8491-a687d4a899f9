package com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-11-03 14:49:06
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UploadOssRegFileReqVo {

    @Schema(description = "文件")
    private MultipartFile file;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "上传类型")
    private String type;
}
