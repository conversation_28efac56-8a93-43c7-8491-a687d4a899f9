package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple;

import com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo.*;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.scenetemple.SceneTempleDO;
import com.cmpay.code.cmpaymodulepc.service.scenetemple.SceneTempleService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 寺庙场景")
@RestController
@RequestMapping("/scene/temple")
@Validated
public class SceneTempleController {

    @Resource
    private SceneTempleService templeService;

    @PostMapping("/templePage")
    @Operation(summary = "行善项目管理——获得寺庙场景分页")
    //@PreAuthorize("@ss.hasPermission('scene:temple:query')")
    public CommonResult<PageResult<SceneTempleVO>> getTemplePage(@RequestBody TemplePageReqVO pageVO) {
        return templeService.getTemplePage(pageVO);
    }

    @PostMapping("/templeCreate")
    @Operation(summary = "行善项目管理——创建寺庙场景")
    //@PreAuthorize("@ss.hasPermission('scene:temple:create')")
    public CommonResult<Long> createTemple(@RequestBody SceneTempleDO sceneTempleDO) {
        return success(templeService.createTemple(sceneTempleDO));
    }

    @PutMapping("/templeUpdate")
    @Operation(summary = "行善项目管理——更新寺庙场景")
    //@PreAuthorize("@ss.hasPermission('scene:temple:update')")
    public CommonResult<Boolean> updateTemple(@RequestBody UpdateTempleReqVo updateTempleReqVo) {
        templeService.updateTemple(updateTempleReqVo);
        return success(true);
    }

    @PostMapping("/templeOrdersPage")
    @Operation(summary = "行善详情——获得详情分页")
    //@PreAuthorize("@ss.hasPermission('scene:temple:create')")
    public CommonResult<TempleOrdersPageRespVo> getTempleOrdersPage(@RequestBody @Valid TempleOrdersPageReqVo templeOrdersPage) {
        return templeService.getTempleOrdersPage(templeOrdersPage);
    }

    @PostMapping("/exportTempleOrdersExcel")
    @Operation(summary = "行善详情——导出行善详情Excel")
    //@PreAuthorize("@ss.hasPermission('charge:orders:export')")
    public void exportTempleOrdersExcel(HttpServletResponse response, @RequestBody TempleOrdersPageReqVo templeOrders) {
        templeService.exportTempleOrdersExcel(templeOrders, response);
    }
}
