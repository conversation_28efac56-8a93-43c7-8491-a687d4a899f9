package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;

import com.cmpay.code.cmpaymodulepc.dal.dataobject.scenetemple.SceneTempleDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-21 14:09:44
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SceneTempleVO extends SceneTempleDO {

    @Schema(description = "行善金额")
    private String moneyStr;

    @Schema(description = "项目状态")
    private String statusStr;
}
