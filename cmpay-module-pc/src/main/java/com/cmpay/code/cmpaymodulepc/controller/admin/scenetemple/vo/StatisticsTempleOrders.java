package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-25 9:48:24
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsTempleOrders {

    @Schema(description = "行善数量")
    private Integer count;

    @Schema(description = "行善金额")
    private BigDecimal totalNormal;
}

