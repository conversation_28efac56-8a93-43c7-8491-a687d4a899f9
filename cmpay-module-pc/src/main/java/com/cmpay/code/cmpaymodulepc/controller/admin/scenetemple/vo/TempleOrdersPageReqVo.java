package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-25 9:31:56
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TempleOrdersPageReqVo extends PageParam {

    @Schema(description = "商户号")
    @NotBlank(message = "商户不能为空")
    private String shopId;

    @Schema(description = "行善项目名")
    private String alms;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;
}
