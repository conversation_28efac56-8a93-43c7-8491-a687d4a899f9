package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;

import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-25 9:46:30
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TempleOrdersPageRespVo {

    @Schema(description = "分页对象")
    private PageResult<TempleOrdersPageVo> pageResult;

    @Schema(description = "统计行善详情")
    private StatisticsTempleOrders statistics;
}
