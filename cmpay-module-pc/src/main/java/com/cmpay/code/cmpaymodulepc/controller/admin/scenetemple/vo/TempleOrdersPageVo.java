package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-25 9:32:05
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TempleOrdersPageVo {

    @Schema(description = "功德主")
    private String name;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "行善金额")
    private BigDecimal totalNormal;

    @Schema(description = "行善项目")
    private String shopproContent;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "行善时间")
    private String timeEnd;

    @Schema(description = "功德回向")
    private String goodsContent;
}
