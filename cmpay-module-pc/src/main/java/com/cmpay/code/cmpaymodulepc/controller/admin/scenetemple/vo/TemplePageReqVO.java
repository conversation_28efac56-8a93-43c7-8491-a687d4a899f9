package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-21 10:13:41
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplePageReqVO extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "行善项目名")
    private String alms;

}
