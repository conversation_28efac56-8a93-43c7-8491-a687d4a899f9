package com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateTempleReqVo {

    @Schema(description = "寺庙场景项目id")
    private Integer id;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "金额")
    private BigDecimal money;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "寺庙场景项目名称")
    private String alms;
}
