package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl;

import com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.*;
import com.cmpay.code.cmpaymodulepc.convert.shorturl.ShortUrlConvert;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.shorturl.ShortUrlDO;
import com.cmpay.code.cmpaymodulepc.service.shorturl.ShortUrlService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import com.google.zxing.WriterException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 设备")
@RestController
@RequestMapping("/shorturl/short-url")
@Validated
public class ShortUrlController {

    @Resource
    private ShortUrlService shortUrlService;


    @PutMapping("/update")
    @Operation(summary = "更新设备")
    // @PreAuthorize("@ss.hasPermission('shorturl:short-url:update')")
    public CommonResult<Boolean> updateShortUrl(@Valid @RequestBody ShortUrlUpdateReqVO updateReqVO) {
        shortUrlService.updateShortUrl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('shorturl:short-url:delete')")
    public CommonResult<Boolean> deleteShortUrl(@RequestParam("id") String id) {
        shortUrlService.deleteShortUrl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('shorturl:short-url:query')")
    public CommonResult<ShortUrlRespVO> getShortUrl(@RequestParam("id") String id) {
        ShortUrlDO shortUrl = shortUrlService.getShortUrl(id);
        return success(ShortUrlConvert.INSTANCE.convert(shortUrl));
    }

//    @GetMapping("/list")
//    @Operation(summary = "获得设备列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('shorturl:short-url:query')")
//    public CommonResult<List<ShortUrlRespVO>> getShortUrlList(@RequestParam("ids") Collection<String> ids) {
//        List<ShortUrlDO> list = shortUrlService.getShortUrlList(ids);
//        return success(ShortUrlConvert.INSTANCE.convertList(list));
//    }

    @GetMapping("/page")
    @Operation(summary = "获得设备分页") 
//    @PreAuthorize("@ss.hasPermission('shorturl:short-url:query')")
    public CommonResult<PageResult<ShortUrlRespVO>> getShortUrlPage(@Valid ShortUrlPageReqVO pageVO) {
        return success(shortUrlService.getShortUrlPage(pageVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出设备 Excel")
    // @PreAuthorize("@ss.hasPermission('shorturl:short-url:export')")
    @OperateLog(type = EXPORT)
    public void exportShortUrlExcel(@Valid ShortUrlExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ShortUrlDO> list = shortUrlService.getShortUrlList(exportReqVO);
        // 导出 Excel
        List<ShortUrlExcelVO> datas = ShortUrlConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "设备.xls", "数据", ShortUrlExcelVO.class, datas);
    }

//    @PermitAll
    @PostMapping("/init")
    @Operation(summary = "初始化设备")
    public CommonResult<List<InitShortUrlRespVo>> initShortUrl(@RequestBody InitShortUrlReqVo shortUrlReqVo) {
        return shortUrlService.initShortUrl(shortUrlReqVo);
    }

    @PostMapping("/create")
    @Operation(summary = "创建设备")
    // @PermitAll
//    @PreAuthorize("@ss.hasPermission('shorturl:short-url:create')")
    public CommonResult<Map<String,Object>> createShortUrl(@Valid @RequestBody ShortUrlCreateReqVO createReqVO) throws Exception {
        return success(shortUrlService.createShortUrl(createReqVO));
    }

    @PostMapping("/update-cashier")
    @Operation(summary = "修改收银员")
    public CommonResult<Boolean> updateCashier(@RequestBody ShortUrlCashierUpdReqVO shortUrlCashierUpdReqVO){
        shortUrlService.updateCashier(shortUrlCashierUpdReqVO);
        return success(true);
    }

    @PostMapping("/unbind-devide")
    @Operation(summary = "解绑")
    public CommonResult<String> unbindDevide(@RequestBody ShortUrlUnbindReqVO shortUrlCashierUpdReqVO){
        return success(shortUrlService.unbindDevide(shortUrlCashierUpdReqVO));
    }

    @GetMapping("/get-yhc")
    @Operation(summary = "绑定收银设备查询")
    @Parameters({
            @Parameter(name = "shotKey", description = "设备号", required = true, example = "1024"),
            @Parameter(name = "shopId", description = "商户号", required = true, example = "1024")
    })
    public CommonResult<ShortUrlYhcRespVO> getYhc(@RequestParam("shortKey") String shortKey,@RequestParam("shopId")String shopId){
        return success(shortUrlService.getYhc(shortKey,shopId));
    }

    @PostMapping("/bind-yhc")
    @Operation(summary = "绑定萤火虫")
    public CommonResult<Map<String,Object>> bindYhcV5(@RequestBody ShortUrlYhcReqVO shortUrlYhcReqVO) throws Exception {
        return success( shortUrlService.bindYhcV5(shortUrlYhcReqVO));
    }

    @PostMapping("/bind-out")
    @Operation(summary = "绑定外插收银设备")
    public CommonResult<Map<String, Object>> bindOut(@RequestBody ShortUrlYhcReqVO shortUrlYhcReqVO) throws Exception {

        return success(shortUrlService.bindOut(shortUrlYhcReqVO));
    }

    @GetMapping("/get-yhc-produce")
    @Operation(summary = "获取萤火虫软件品牌")
    public CommonResult<List<ShortUrlYhcProduceRespVO>> getYhcProduce(){
        return success(shortUrlService.getYhcProduce());
    }

    @GetMapping("/get-yhc-soft")
    @Operation(summary = "获取萤火虫软件二级品牌")
    public CommonResult<List<ShortUrlYhcProduceRespVO>> getYhcSoftMsg(String produceId){
        return success(shortUrlService.getYhcSoftMsg(produceId));
    }

    @PostMapping("/reset-qr-code")
    @Operation(summary = "重置设备二维码")
    public CommonResult resetQrCode(@RequestBody ShortUrlRestQrCodeReqVO shortUrlRestQrCodeReqVO){
        return shortUrlService.resetQrCode(shortUrlRestQrCodeReqVO);
    }

    @PostMapping("/cloud-horn-test")
    @Operation(summary = "测试语音播报")
    public CommonResult cloudHornTest(@RequestBody ShortUrlCloudHornTestReqVO shortUrlRestQrCodeReqVO){
        return shortUrlService.cloudHornTest(shortUrlRestQrCodeReqVO);
    }

    @PostMapping("/saveqrcode")
    @Operation(summary = "绑定解绑设备")
    public CommonResult<String> saveqrcode(@Valid @RequestBody ShortUrlCreateReqVO createReqVO){
        return shortUrlService.saveqrcode(createReqVO);
    }

    @PostMapping("/saveMerchantStatusWithAlipay")
    @Operation(summary = "绑定如意设备")
    public CommonResult<String> saveMerchantStatusWithAlipay(@RequestBody RyDeviceVo ryDeviceVo) throws IOException, WriterException {
       return shortUrlService.saveMerchantStatusWithAlipay(ryDeviceVo);
    }

}
