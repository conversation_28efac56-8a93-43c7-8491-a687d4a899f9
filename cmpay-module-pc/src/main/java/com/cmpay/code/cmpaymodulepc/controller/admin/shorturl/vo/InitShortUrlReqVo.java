package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.InitDevicesReqVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description:
 * @date 2023-04-19 16:59:26
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitShortUrlReqVo extends InitDevicesReqVo {

    @Schema(description = "分店名称")
    private String device;

    @Schema(description = "当前人id")
    private String xmid;

    @Schema(description = "商户id")
    private String shopId;

    @Schema(description = "当前身份")
    private String isBoss;
}
