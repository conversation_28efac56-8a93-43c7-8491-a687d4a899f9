package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ShortUrlCloudHornTestReqVO {

    @Schema(description = "商户号")
    private String shopId;

    @NotNull(message = "sn号不能为空")
    @Schema(description = "sn号")
    private String sn;

    @NotNull(message = "云喇叭品牌不能为空")
    @Schema(description = "云喇叭品牌id")
    private String ylbBrand;

}
