package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Schema(description = "管理后台 - 设备创建 Request VO")
@Data
@ToString(callSuper = true)
public class ShortUrlCreateReqVO{

    @Schema(description = "设备号", example = "30919")
    private String shortKey="";
    @Schema(description = "商户号", example = "30919")
    @NotBlank(message = "商户号不能为空")
    private String shopId="";

    @Schema(description = "设备简称", example = "张三")
    private String nickname="";

    @Schema(description = "type", example = "2")
    private String type="";

    @Schema(description = "openid", example = "17318")
    private String openid="";

    @Schema(description = "agent_sub_openid", example = "24394")
    private String agentSubOpenid="";

    @Schema(description = "设备绑定的打印设备编号", example = "23964")
//    @Pattern(regexp = "^[a-zA-Z0-9]{1,30}$", message = "请填写正确的打印设备编号，由数字字母组成")
    private String printDeviceId="";

    @Schema(description = "设备绑定的打印设备状态", example = "1-开启，2-关闭")
    private String printDeviceStatus="0";

    @Schema(description = "设备绑定的收银设备编号", example = "4895")
    private String wsyDeviceId="";

    @Schema(description = "设备绑定的收银设备秘钥")
    private String wsyDeviceKey="";

    @Schema(description = "设备绑定的打印机品牌")
    private String printBrand="";

    @Schema(description = "设备绑定的云喇叭编号", example = "10179")
//    @Pattern(regexp = "^[a-zA-Z0-9,]{1,30}$", message = "请填写正确的云喇叭编号，由数字字母组成")
//    @NotBlank(message = "云喇叭品牌id不能为空")
    private String audioHornId="";

    @Schema(description = "设备绑定的收银机品牌")
    private String registerBrand="";

    @Schema(description = "设备绑定的云喇叭品牌")
//    @NotBlank(message = "云喇叭品牌不能为空")
    private String ylbBrand="";

    @Schema(description = "设备名称")
    private String shortKeyName="";


    @Schema(description = "萤火虫插件品牌id")
    private String softId="";

    @Schema(description = "设备所属分店主键")
    private String deviceId="";

    @Schema(description = "打印份数")
    private String printDeviceNumber="1";

    @Schema(description = "所属门店")
    private String device="";

    @Schema(description = "是否播报取消支付订单")
    private String isPlayCannelOrder="1";

    @Schema(description = "订单小条是否打印退款条码")
    private String printOrderRefund="0";

    @Schema(description = "云喇叭品牌id")
    private String ylbId="";

    @Schema(description = "云喇叭品牌id")
    private String tradeId="";

}
