package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ShortUrlExcelVO {

    @ExcelProperty("设备主键（终端号）")
    private String shortKey;

    @ExcelProperty("商户号")
    private String shopId;

    @ExcelProperty("设备所属分店名字")
    private String device;

    @ExcelProperty("设备简称")
    private String nickname;

    @ExcelProperty("trade_id")
    private String tradeId;

    @ExcelProperty("order_id")
    private String orderId;

    @ExcelProperty("time")
    private LocalDateTime time;

    @ExcelProperty("goods_number")
    private Integer goodsNumber;

    @ExcelProperty("total_fee")
    private Object totalFee;

    @ExcelProperty("type")
    private String type;

    @ExcelProperty("固定码牌，1：是；0：否。")
    private Integer fixed;

    @ExcelProperty("openid")
    private String openid;

    @ExcelProperty("xmid")
    private String xmid;

    @ExcelProperty("agent_id")
    private String agentId;

    @ExcelProperty("sub_appid")
    private String subAppid;

    @ExcelProperty("sub_appidse")
    private String subAppidse;

    @ExcelProperty("sub_mid")
    private String subMid;

    @ExcelProperty("salesman")
    private String salesman;

    @ExcelProperty("rate")
    private Integer rate;

    @ExcelProperty("rate_quota")
    private Object rateQuota;

    @ExcelProperty("page")
    private String page;

    @ExcelProperty("pp_device_no")
    private String ppDeviceNo;

    @ExcelProperty("agent_sub_openid")
    private String agentSubOpenid;

    @ExcelProperty("设备绑定的打印设备编号")
    private String printDeviceId;

    @ExcelProperty("设备绑定的打印设备装填")
    private Integer printDeviceStatus;

    @ExcelProperty("设备绑定的收银设备编号")
    private String wsyDeviceId;

    @ExcelProperty("设备绑定的收银设备秘钥")
    private String wsyDeviceKey;

    @ExcelProperty("switch_time")
    private String switchTime;

    @ExcelProperty("设备绑定的打印机品牌")
    private String printBrand;

    @ExcelProperty("scene_type")
    private Integer sceneType;

    @ExcelProperty("print_device_number")
    private Integer printDeviceNumber;

    @ExcelProperty("print_order_refund")
    private Integer printOrderRefund;

    @ExcelProperty("设备绑定的云喇叭编号")
    private String audioHornId;

    @ExcelProperty("????,1:?;2:?")
    private Integer lockCashier;

    @ExcelProperty("设备绑定的收银机品牌")
    private String registerBrand;

    @ExcelProperty("设备绑定的云喇叭品牌")
    private String ylbBrand;

    @ExcelProperty("设备所属分店主键")
    private Long deviceId;

    @ExcelProperty("设备名称")
    private String shortKeyName;

    @ExcelProperty("audio_horn_token")
    private String audioHornToken;

    @ExcelProperty("arf_a8_anyhow")
    private Integer arfA8Anyhow;

    @ExcelProperty("ios:??;android:android")
    private String audioHornAppType;

    @ExcelProperty("???????,??1:??;0:???")
    private Integer status;

    @ExcelProperty("1:??;2:???")
    private Integer isPlayCannelOrder;

    @ExcelProperty("拉卡拉云mis收款设备")
    private String deviceSeq;

}
