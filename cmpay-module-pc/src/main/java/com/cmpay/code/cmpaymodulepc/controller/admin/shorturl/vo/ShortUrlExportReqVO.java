package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备 Excel 导出 Request VO，参数和 ShortUrlPageReqVO 是一致的")
@Data
public class ShortUrlExportReqVO {

    @Schema(description = "商户号", example = "30919")
    private String shopId;

    @Schema(description = "设备所属分店名字")
    private String device;

    @Schema(description = "设备简称", example = "张三")
    private String nickname;

    @Schema(description = "trade_id", example = "988")
    private String tradeId;

    @Schema(description = "order_id", example = "24960")
    private String orderId;

    @Schema(description = "time")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] time;

    @Schema(description = "goods_number")
    private Integer goodsNumber;

    @Schema(description = "total_fee")
    private Object totalFee;

    @Schema(description = "type", example = "2")
    private String type;

    @Schema(description = "固定码牌，1：是；0：否。")
    private Integer fixed;

    @Schema(description = "openid", example = "17318")
    private String openid;

    @Schema(description = "xmid", example = "18243")
    private String xmid;

    @Schema(description = "agent_id", example = "19246")
    private String agentId;

    @Schema(description = "sub_appid", example = "8321")
    private String subAppid;

    @Schema(description = "sub_appidse")
    private String subAppidse;

    @Schema(description = "sub_mid", example = "23226")
    private String subMid;

    @Schema(description = "salesman")
    private String salesman;

    @Schema(description = "rate")
    private Integer rate;

    @Schema(description = "rate_quota")
    private Object rateQuota;

    @Schema(description = "page")
    private String page;

    @Schema(description = "pp_device_no")
    private String ppDeviceNo;

    @Schema(description = "agent_sub_openid", example = "24394")
    private String agentSubOpenid;

    @Schema(description = "设备绑定的打印设备编号", example = "23964")
    private String printDeviceId;

    @Schema(description = "设备绑定的打印设备装填", example = "1")
    private Integer printDeviceStatus;

    @Schema(description = "设备绑定的收银设备编号", example = "4895")
    private String wsyDeviceId;

    @Schema(description = "设备绑定的收银设备秘钥")
    private String wsyDeviceKey;

    @Schema(description = "switch_time")
    private String switchTime;

    @Schema(description = "设备绑定的打印机品牌")
    private String printBrand;

    @Schema(description = "scene_type", example = "1")
    private Integer sceneType;

    @Schema(description = "print_device_number")
    private Integer printDeviceNumber;

    @Schema(description = "print_order_refund")
    private Integer printOrderRefund;

    @Schema(description = "设备绑定的云喇叭编号", example = "10179")
    private String audioHornId;

    @Schema(description = "????,1:?;2:?")
    private Integer lockCashier;

    @Schema(description = "设备绑定的收银机品牌")
    private String registerBrand;

    @Schema(description = "设备绑定的云喇叭品牌")
    private String ylbBrand;

    @Schema(description = "设备所属分店主键", example = "14473")
    private Long deviceId;

    @Schema(description = "设备名称", example = " ")
    private String shortKeyName;

    @Schema(description = "audio_horn_token")
    private String audioHornToken;

    @Schema(description = "arf_a8_anyhow")
    private Integer arfA8Anyhow;

    @Schema(description = "ios:??;android:android", example = "1")
    private String audioHornAppType;

    @Schema(description = "???????,??1:??;0:???", example = "2")
    private Integer status;

    @Schema(description = "1:??;2:???")
    private Integer isPlayCannelOrder;

    @Schema(description = "拉卡拉云mis收款设备")
    private String deviceSeq;

}
