package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShortUrlPageReqVO extends PageParam {

    @Schema(description = "商户号", example = "30919",required = true)
    private String shopId;

    @Schema(description = "设备所属分店id")
    private String deviceId;

    @Schema(description = "终端号")
    private String shortKey;

    @Schema(description = "收银员")
    private String cashRegister;

    @Schema(description = "终端号")
    private String simpleCode;

    @Schema(description = "设备类型id")
    private String simpleCodeType;

    @Schema(description = "wsyAgain")
    private String wsyAgain;

    @Schema(description = "ylb")
    private String ylb;

    @Schema(description = "wsy")
    private String wsy;

    @Schema(description = "ylbAgain")
    private String ylbAgain;

    @Schema(description = "ydy")
    private String ydy;

    @Schema(description = "ydyAgain")
    private String ydyAgain;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "门店id/设备id")
    private String simpleCodeId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "短商户号")
    private String trueid;

    @Schema(description = "平台")
    private Integer platform;

    @Schema(description = "市级id")
    private String partnerId;

    @Schema(description = "省级id")
    private Integer branchId;

}
