package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ToString(callSuper = true)
public class ShortUrlRespVO {

    @Schema(description = "设备主键（终端号）")
    private String shortKey;

    @Schema(description = "所属门店")
    private String device;

    @Schema(description = "设备简称")
    private String nickname;

    @Schema(description = "收银员")
    private String name;

    @Schema(description = "创建时间")
    private String time;

    @Schema(description = "设备绑定的打印设备编号")
    private String printDeviceId;

    @Schema(description = "设备绑定的打印设备状态")
    private Integer printDeviceStatus;

    @Schema(description = "设备绑定的打印设备状态字符串")
    private String printDeviceStatusStr;

    @Schema(description = "设备绑定的收银设备编号")
    private String wsyDeviceId;

    @Schema(description = "设备绑定的打印机品牌")
    private String printBrand;

    @Schema(description = "设备绑定的打印机品牌中文名称")
    private String printBrandStr;

    @Schema(description = "设备绑定的打印机品数量")
    private String printDeviceNumber;

    @Schema(description = "设备退款订单")
    private String printOrderRefund;

    @Schema(description = "设备绑定的云喇叭编号")
    private String audioHornId;

    @Schema(description = "微信对应的小程序id")
    private String openid;

    @Schema(description = "设备绑定的收银机品牌")
    private String registerBrand;

    @Schema(description = "设备绑定的收银机品牌中文名称")
    private String registerBrandStr;

    @Schema(description = "设备绑定的云喇叭品牌")
    private String ylbBrand;

    @Schema(description = "设备绑定的云喇叭品牌中文名称")
    private String ylbBrandStr;

    @Schema(description = "是否锁定收银员")
    private String lockCashier;

    @Schema(description = "xmid")
    private String xmid;

    @Schema(description = "设备所属分店id")
    private String deviceId;

    @Schema(description = "设备名称")
    private String shortKeyName;

    @Schema(description = "设备绑定的收银设备秘钥")
    private String wsyDeviceKey;

    @Schema(description = "是否播报订单语音")
    private String isPlayCannelOrder;

    @Schema(description = "拉卡拉云mis收款设备")
    private String deviceSeq;

    @Schema(description = "收银员简称")
    private String nickUser;

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "是否属于邮政的商户")
    private Integer isYouz = 0;

    @Schema(description = "云喇叭品牌id")
    private String tradeId;

    @Schema(description = "商户号")
    private String trueid;

    @Schema(description = "商户名")
    private String shopName;

    @Schema(description = "是否有萤火虫外插")
    private String isYhc;

    @Schema(description = "是否微信黑名单:0：否，1：是")
    private String isBlackWx="0";
}
