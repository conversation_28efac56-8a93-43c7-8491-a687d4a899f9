package com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 设备更新 Request VO")
@Data
@ToString(callSuper = true)
public class ShortUrlUpdateReqVO{

    @Schema(description = "设备主键（终端号）", required = true)
    @NotNull(message = "设备主键（终端号）不能为空")
    private String shortKey;

    private String xmid;
    private String shopId;
    private String salesman;
    private String device;
    private Long deviceId;

}
