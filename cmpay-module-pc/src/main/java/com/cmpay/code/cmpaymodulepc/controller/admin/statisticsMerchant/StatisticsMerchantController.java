package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant;

import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.*;
import com.cmpay.code.cmpaymodulepc.service.statisticsMerchant.StatisticsMerchantService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @description: 统计商户表
 * @date 2023-04-13 14:09:58
 * @version: 1.0
 */
@Tag(name = "管理后台 - 统计交易信息")
@RestController
@RequestMapping("/statistics/merchant")
@Validated
public class StatisticsMerchantController {

    @Resource
    private StatisticsMerchantService statisticsMerchantService;


    @GetMapping("/home-page")
    @Operation(summary = "首页日周月统计")
//    @PreAuthorize("@ss.hasPermission('statistics:merchant:query')")
//     @PermitAll
    public CommonResult<HomePageRespVO> getHomeStatistics(String acceptId,String agentId) {
        HomePageRespVO homeStatistics = statisticsMerchantService.getHomeStatistics(acceptId,agentId);
        return success(homeStatistics);
    }

    // @PermitAll
    @PostMapping("/message")
    @Operation(summary = "搜索框模糊查询商户信息")
    public CommonResult<List<MerchantMessageRespVo>> searchMerchantMessage(@RequestBody MerchantMessageReqVo merchantMessageReqVo) {
        return statisticsMerchantService.message(merchantMessageReqVo);
    }

    // @PermitAll
    @PostMapping("/statisticsYard")
    @Operation(summary = "换码统计查询")
//    @PreAuthorize("@ss.hasPermission('statistics:hm-statistics:query')")
    public CommonResult<PageResult<StatisticsYardRespVo>> statisticsYard(@RequestBody StatisticsYardReqVo statisticsYardReqVo) {
        return statisticsMerchantService.statisticsYard(statisticsYardReqVo);
    }
}
