package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.StatisticsMonthRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 首页 Response VO")
@Data
@ToString(callSuper = true)
public class HomePageRespVO {

    @Schema(description = "日统计")
    private HomeStatisticsRespVO daily;

    @Schema(description = "周统计")
    private HomeStatisticsRespVO weekly;

    @Schema(description = "月统计")
    private HomeStatisticsRespVO monthly;

    @Schema(description = "统计集合")
    private List<StatisticsMonthRespVO>  list;
}
