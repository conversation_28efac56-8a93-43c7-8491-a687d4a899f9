package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 首页流水 Response VO")
@Data
@ToString(callSuper = true)
public class HomeStatisticsRespVO {
    @Schema(description = "订单数", example = "20100")
    private Long orders;
    @Schema(description = "总流水", example = "52.58")
    private BigDecimal total;
}
