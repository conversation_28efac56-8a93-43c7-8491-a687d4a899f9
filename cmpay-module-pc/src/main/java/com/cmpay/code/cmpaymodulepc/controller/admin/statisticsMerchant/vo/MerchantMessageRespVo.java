package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 获取商户信息的返回vo
 * @date 2023-04-11 14:36:40
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MerchantMessageRespVo {

    /**
     * 商户id
     */
    @Schema(description = "商户id")
    private String shopId;

    /**
     * 商户名称
     */
    @Schema(description = "商户名称")
    private String shopName;

    /**
     * 商户简称
     */
    @Schema(description = "商户简称")
    private String shopNickname;

    /**
     * 管理人
     */
    @Schema(description = "管理人")
    private String shopKeeper;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "短商户号")
    private String trueid;
}
