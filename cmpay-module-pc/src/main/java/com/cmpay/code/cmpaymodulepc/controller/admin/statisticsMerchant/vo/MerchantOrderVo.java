package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 商户交易统计order
 * @date 2023-04-14 10:10:29
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantOrderVo {

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    private double moneyPayable;

    /**
     * 交易数量
     */
    @Schema(description = "交易数量")
    private int orderCount;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;

    /**
     * 商户注册时间
     */
    @Schema(description = "商户注册时间")
    private String registerTime;

    /**
     * 商户id
     */
    @Schema(description = "商户标识码")
    private String shopId;

    /**
     * 负责人
     */
    @Schema(description = "联系人")
    private String shopKeeper;

    /**
     * 商户简称
     */
    @Schema(description = "商户简称")
    private String shopNickname;

    /**
     * 短的商户号
     */
    @Schema(description = "短的商户号")
    private String trueid;

    @Schema(description = "交易天数")
    private String transactionDay;

    @Schema(description = "商户全称")
    private String shopName;

    @Schema(description = "商户类型状态")
    private String businessType;

    @Schema(description = "商户类型字符串")
    private String businessTypeStr;

    @Schema(description = "客户经理openid")
    private String ywyOpenid;

    @Schema(description = "客户经理手机号")
    private String ywyPhone;

    @Schema(description = "客户经理姓名")
    private String ywyName;

    @Schema(description = "微信费率")
    private String wxRate;

    @Schema(description = "支付宝费率")
    private String aliRate;

    @Schema(description = "云闪付费率")
    private String uniRate;

    @Schema(description = "数币支付费率")
    private String dcpayRate;

    @Schema(description = "MCC代码")
    private String ryxMcc;

    @Schema(description = "所属网点Id")
    private String acceptId;

    @Schema(description = "所属区县Id")
    private String agentId;

    @Schema(description = "所属地市Id")
    private String partnerId;

    @Schema(description = "所属网点")
    private String acceptCompany;

    @Schema(description = "所属区县")
    private String agentCompany;

    @Schema(description = "所属地市")
    private String partnerCompany;

    @Schema(description = "网点机构号")
    private String acceptYzOrgId;

    @Schema(description = "区县机构号")
    private String agentYzOrgId;

    @Schema(description = "地市机构号")
    private String partnerYzOrgId;

    @Schema(description = "微信商户号（间联）")
    private String merchantOther3;

    @Schema(description = "支付宝商户号（间联）")
    private String merchantOther4;

    @Schema(description = "三方商户号")
    private String merchantNo;

    @Schema(description = "通道key")
    private String channel;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "阈值")
    private Integer endAmt;

    @Schema(description = "超阈值")
    private BigDecimal wxThresholdRate;

    @Schema(description = "超阈值")
    private String payType;
}
