package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-14 15:08:11
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantTransactionMessageVo {

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 商户注册时间
     */
    private String registerTime;


    /**
     * 负责人
     */
    private String shopKeeper;

    /**
     * 商户名
     */
    private String shopName;

    /**
     * 短的商户号
     */
    private String trueid;

    /**
     * 商户简称
     */
    private String shopNickname;

    private String ywyOpenid;

    private String payType;
}
