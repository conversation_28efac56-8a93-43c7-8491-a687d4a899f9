package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 商户交易统计请求
 * @date 2023-04-14 10:12:09
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantTransactionReqVo extends PageParam {

    /**
     * 商户名称
     */
    @Schema(description = "商户名")
    private String shopNickname;

    @Schema(description = "平台")
    private Integer platform;

    /**
     * 商户号
     */
    @Schema(description = "商户号")
    private String trueid;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String start;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String end;

    /**
     * 结算通道
     */
    @Schema(description = "通道")
    private String channel;

    /**
     * 营业点id
     */
    @Schema(description = "营业点id")
    private String acceptId;

    /**
     * 区县分公司
     */
    @Schema(description = "区县分公司")
    private String agentId;

    /**
     * 市级分公司
     */
    @Schema(description = "市级分公司")
    private String partnerId;

    /**
     * 省级分公司
     */
    @Schema(description = "省级分公司")
    private String branchId;

    @Schema(description = "商户标识码")
    private String shopId;

    @Schema(description = "注册时间")
    private String registerTime;

    @Schema(description = "客户经理手机号")
    private String ywyPhone;

    @Schema(description = "客户经理姓名")
    private String ywyName;

    @Schema(description = "MCC代码")
    private String ryxMcc;

    @Schema(description = "商户类型")
    private String businessType;

    @Schema(description = "所属网点")
    private String acceptCompany;

    @Schema(description = "所属区县")
    private String agentCompany;

    @Schema(description = "所属地市")
    private String partnerCompany;

    @Schema(description = "商户全称")
    private String shopName;

    @Schema(description = "微信费率")
    private String wxRate;

    @Schema(description = "支付宝费率")
    private String aliRate;

    @Schema(description = "云闪付费率")
    private String uniRate;

    @Schema(description = "数币支付费率")
    private String dcpayRate;

    @Schema(description = "微信商户号（间联）")
    private String merchantOther3;

    @Schema(description = "支付宝商户号（间联）")
    private String merchantOther4;

    @Schema(description = "三方商户号")
    private String merchantNo;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "内部id")
    private String account;

    @Schema(description = "下载id")
    private String xzOrderId;

    @Schema(description = "下载名称")
    private String downloadName;

    @Schema(description = "登录用户类型")
    private String userType;

    @Schema(description = "登录用户id")
    private String userId;

    @Schema(description = "阈值")
    private String endAmt;

    @Schema(description = "超阈值")
    private String wxThresholdRate;

}
