package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-07 10:40:35
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsYardReqVo extends PageParam {

    /**
     * 截止时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;

    /**
     * 市级
     */
    private String partnerId;

    /**
     * 省级
     */
    private String branchId;

    /**
     * 区县
     */
    private String agentId;
}
