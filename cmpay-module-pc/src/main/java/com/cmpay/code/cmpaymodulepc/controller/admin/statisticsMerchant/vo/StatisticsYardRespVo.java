package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-07 10:56:52
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsYardRespVo {

    private String company;

    /**
     * 累计换码商户数
     */
    private Integer hadHmMerchants;

    /**
     * 目标换码商户数
     */
    private Integer wantHmMerchants;

    /**
     * 换码进度
     */
    private Double hmBili;

    /**
     * 机构id（省，市，区）用户
     */
    private String nowGradeId;

    /**
     * 当天换码数量
     */
    private Integer hadHmYesday;
}
