package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 商户交易统计
 * @date 2023-04-14 14:29:06
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionNumberVo {

    /**
     * 交易数
     */
    @Schema(description = "交易数")
    private int orderCount;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    private BigDecimal moneyPayable;
}
