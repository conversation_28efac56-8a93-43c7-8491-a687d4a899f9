package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 交易统计明细
 * @date 2023-03-30 17:07:15
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransactionOrderVo {

    /**
     * 区县分公司
     */
    @Schema(description = "区县分公司")
    private String agentCompany;

    /**
     * 代理商id
     */
    @Schema(description = "代理商id")
    private String agentId;

    /**
     * 手续费
     */
    @Schema(description = "手续费")
    private Double commission;


    private Double moneyActivity;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    private Double moneyPaidout;

    /**
     * 交易流水
     */
    @Schema(description = "交易流水")
    private Double moneyPayable;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private Double moneyRefund;

    /**
     * 订单数量
     */
    @Schema(description = "订单数量")
    private int orderCount;

    /**
     * 市级分公司
     */
    @Schema(description = "市级分公司")
    private String partnerCompany;

    /**
     * 商户id
     */
    @Schema(description = "商户id")
    private String shopId;

    /**
     * 商户名称
     */
    @Schema(description = "商户名称")
    private String shopName;

    /**
     * 营业点名称
     */
    @Schema(description = "营业点名称")
    private String acceptCompany;

    /**
     * 短商户号
     */
    @Schema(description = "短商户号")
    private String shopAccount;

    /**
     * 交易日期
     */
    @Schema(description = "交易日期")
    private String statDay;

}
