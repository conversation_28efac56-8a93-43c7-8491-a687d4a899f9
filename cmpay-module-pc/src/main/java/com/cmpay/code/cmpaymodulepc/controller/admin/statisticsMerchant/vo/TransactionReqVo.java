package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 交易统计参数
 * @date 2023-03-30 14:18:31
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransactionReqVo extends PageParam {

    /**
     * 商户id
     */
    @Schema(description = "商户id")
    private String shopId;

    /**
     * 商户名称
     */
    @Schema(description = "商户名称")
    private String shopName;

    /**
     * 结算通道
     */
    @Schema(description = "结算通道")
    private String channel;

    /**
     * 区县分公司
     */
    @Schema(description = "区县分公司")
    private String agentId;

    /**
     * 市级分公司
     */
    @Schema(description = "市级分公司")
    private String partnerId;

    /**
     * 省级分公司
     */
    @Schema(description = "省级分公司")
    private String branchId;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String start;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String end;

    /**
     * 当前用户人
     */
    @Schema(description = "当前用户人")
    private String account;

    /**
     * 营业点id
     */
    @Schema(description = "营业点id")
    private String acceptId;
}
