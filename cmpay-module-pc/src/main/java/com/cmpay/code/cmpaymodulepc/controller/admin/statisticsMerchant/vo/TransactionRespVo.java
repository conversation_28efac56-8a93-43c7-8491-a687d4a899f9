package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 交易统计返回的vo
 * @date 2023-03-31 8:51:56
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransactionRespVo {

    /**
     * 分页对象
     */
    private Page<TransactionOrderVo> pageResult;

    /**
     * 交易统计对象
     */
    private TransactionStatisticsVo statistics;
}
