package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 统计总交易vo
 * @date 2023-03-30 15:30:30
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionStatisticsVo {

    /**
     * 交易数量
     */
    @Schema(description = "交易数量")
    private int orderCount = 0;

    /**
     * 交易总流水
     */
    @Schema(description = "交易总流水")
    private Double moneyPayable = 0.00;

    /**
     * 总手续费
     */
    @Schema(description = "总手续费")
    private Double commission = 0.00;

    /**
     * 总结算金额
     */
    @Schema(description = "总结算金额")
    private Double moneyPaidout = 0.00;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private Double moneyRefund = 0.00;

    /**
     * 活动优惠金额
     */
    @Schema(description = "活动优惠金额")
    private Double moneyActivity = 0.00;
}
