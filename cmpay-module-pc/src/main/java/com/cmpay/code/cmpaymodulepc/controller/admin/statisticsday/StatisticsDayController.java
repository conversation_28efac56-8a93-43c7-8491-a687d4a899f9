package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday;

import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomePageRespVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.ShopRespVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.StatisticsMerchantOrdersReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.StatisticsMerchantOrdersRespVo;
import com.cmpay.code.cmpaymodulepc.service.statisticsday.StatisticsDayService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.controller.admin.auth.vo.AuthMenuRespVO;
import com.cmpay.code.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVO;
import com.cmpay.code.module.system.convert.auth.AuthConvert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 商户维度日统计")
@RestController
@RequestMapping("/statisticsday/statistics-day")
@Validated
@Slf4j
public class StatisticsDayController {

    @Resource
    private StatisticsDayService statisticsDayService;


    @GetMapping("/get-shop-name")
    @Operation(summary = "获取当前登录商户信息")
    //@PreAuthorize("@ss.hasPermission('statisticsday:statistics-day:query')")
    public CommonResult<ShopRespVO> getShopName(HttpServletRequest request, String shopId, String username,String refreshToken) {
        log.info("request merchant get-shop-name param:{},{}", shopId,username);
        ShopRespVO shopRespVOByUserId = statisticsDayService.getShopRespVOByUserId(request,shopId,username,refreshToken);
        log.info("response merchant get-shop-name param:{}", shopRespVOByUserId);
        return success(shopRespVOByUserId);
    }

    @GetMapping("/list-menus-shop")
    @Operation(summary = "商户获得登录用户的菜单列表")
    public CommonResult<List<AuthMenuRespVO>> getShopMenuList(String shopId,String username) {
        // 转换成 Tree 结构返回
        return success(AuthConvert.INSTANCE.buildMenuTree(statisticsDayService.getShopMenuList(shopId,username)));
    }

    @GetMapping("get-shop-list")
    @Operation(summary = "获取用户的所有商户列表")
    public CommonResult<Set<ShopRespVO>> getShopList(Integer isTrueid,String shopId) {
        return success(statisticsDayService.getShopList(isTrueid,shopId));
    }

    @GetMapping("/get-permission-shop")
    @Operation(summary = "获取登录的商户用户权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo(String shopId,String username) {
        // 拼接结果返回
        return success(statisticsDayService.getPermissionShop(shopId,username));
    }

    @GetMapping("/get-merchant-statistics")
    @Operation(summary = "商户首页统计")
    public CommonResult<HomePageRespVO> getMerchantStatistics(@RequestParam("shopId") String shopId) {
        return success(statisticsDayService.getMerchantStatistics(shopId));
    }

    @PostMapping("/statisticsMerchantOrders")
    @Operation(summary = "商户统计管理——交易统计查询")
    //@PreAuthorize("@ss.hasPermission('merchantstatistics:merchant-statistics:query')")
    public CommonResult<StatisticsMerchantOrdersRespVo> statisticsMerchantOrders(@RequestBody StatisticsMerchantOrdersReqVo merchantOrders) {
        return statisticsDayService.statisticsMerchantOrders(merchantOrders);
    }

    @PostMapping("/exportMerchantOrdersExcel")
    @Operation(summary = "商户统计管理——导出交易统计Excel")
    //@PreAuthorize("@ss.hasPermission('merchantstatistics:merchant-statistics:export')")
    public void exportMerchantOrdersExcel(@RequestBody StatisticsMerchantOrdersReqVo merchantOrders, HttpServletResponse response) {
        statisticsDayService.exportMerchantOrdersExcel(merchantOrders, response);
    }
}
