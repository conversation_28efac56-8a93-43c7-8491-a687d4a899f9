package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-05-08 11:28:02
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MerchantOrdersDetailVo {

    @Schema(description = "分店")
    private String device;

    @Schema(description = "收银员")
    private String cashier;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "订单类型")
    private String orderTypeStr;

    @Schema(description = "交易日期")
    private String statDay;

    @Schema(description = "订单量")
    private Integer orderCount;

    @Schema(description = "交易流水")
    private Double moneyPayable;

    @Schema(description = "退款金额")
    private Double moneyRefund;

    @Schema(description = "活动金额")
    private Double moneyActivity;

    @Schema(description = "手续费")
    private Double commission;

    @Schema(description = "结算金额")
    private Double moneyPaidOut;

    @Schema(description = "分账金额")
    private Double moneyProfitShare;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "通道")
    private String channel;
}
