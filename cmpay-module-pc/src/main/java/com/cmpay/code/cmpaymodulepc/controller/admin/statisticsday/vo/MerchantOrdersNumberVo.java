package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 商户交易统计查询数据总结果
 * @date 2023-05-08 11:34:57
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MerchantOrdersNumberVo {

    @Schema(description = "订单量")
    private Integer orderCount = 0;

    @Schema(description = "交易流水")
    private Double moneyPayable = 0.0;

    @Schema(description = "退款金额")
    private Double moneyRefund = 0.0;

    @Schema(description = "活动金额")
    private Double moneyActivity = 0.0;

    @Schema(description = "手续费")
    private Double commission = 0.0;

    @Schema(description = "结算金额")
    private Double moneyPaidOut = 0.0;

    @Schema(description = "分账金额")
    private Double moneyProfitShare = 0.0;
}
