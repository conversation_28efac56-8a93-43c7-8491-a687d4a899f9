package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShopRespVO {
    @Schema(description = "商户号", required = true, example = "8267")
    private String shopId;
    @Schema(description = "商户名称", required = true, example = "8267")
    private String shopName;
    @Schema(description = "短商户号", required = true, example = "8267")
    private String trueid;
    @Schema(description = "超管手机号", required = true, example = "8267")
    private String superPhone;
    @Schema(description = "登录账号", required = true, example = "8267")
    private String username;
    @Schema(description = "xmid", required = true, example = "8267")
    private String xmid;
    @Schema(description = "角色", required = true, example = "8267")
    private String isBossName;
    @Schema(description = "登陆人名称", required = true, example = "8267")
    private String loginName;
    @Schema(description = "角色id", required = true, example = "8267")
    private Integer isBoss;

    @Schema(description = "商户简称", required = true, example = "简称")
    private String nickShopName;
}
