package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 商户交易统计查询请求
 * @date 2023-05-08 11:42:23
 * @version: 1.0
 */
@Data
public class StatisticsMerchantOrdersReqVo extends PageParam {

    @Schema(description = "商户id", required = true)
    private String shopId;

    @Schema(description = "当前人身份", required = true)
    private String isBoss;

    @Schema(description = "当前身份的xmid", required = true)
    private String xmid;

    @Schema(description = "分店")
    private String device;

    @Schema(description = "收银员的xmid")
    private String cashierId;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private String start;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private String end;

    @Schema(description = "统计模式：分店-->device  全列-->all")
    private String statisticsType;

    @Schema(description = "商户有无退款：有->refund; 无->noRefund")
    private String refundAmount;
}
