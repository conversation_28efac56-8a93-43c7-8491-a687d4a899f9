package com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo;

import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 商户交易统计vo
 * @date 2023-05-08 11:16:50
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsMerchantOrdersRespVo {

    @Schema(description = "交易统计明细")
    private PageResult<MerchantOrdersDetailVo> pageResult = new PageResult<>();

    @Schema(description = "封装数据总结果")
    private MerchantOrdersNumberVo merchantOrdersNumberVo = new MerchantOrdersNumberVo();
}
