package com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders;

import com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo.UpdateWholesaleStatusReqVo;
import com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo.WholesaleOrderPage;
import com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo.WholesaleOrderReqVo;
import com.cmpay.code.cmpaymodulepc.service.wholesaleorders.WholesaleOrdersService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;



@Tag(name = "管理后台 - 批发订单")
@RestController
@RequestMapping("/wholesale/orders")
@Validated
public class WholesaleOrdersController {

    @Resource
    private WholesaleOrdersService wholesaleOrdersService;


    @PostMapping("/page")
    @Operation(summary = "批发订单分页")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('statistics:orders:query')")
    public CommonResult<PageResult<WholesaleOrderPage>> searchWholesaleOrders(@RequestBody WholesaleOrderReqVo wholesaleOrder) {
        return wholesaleOrdersService.searchWholesaleOrders(wholesaleOrder);
    }


    @PutMapping("/update")
    @Operation(summary = "批发订单——确认结单")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('statistics:orders:query')")
    public CommonResult<String> updateWholesaleStatus(@RequestBody UpdateWholesaleStatusReqVo wholesaleStatusReqVo) {
        return  wholesaleOrdersService.updateWholesaleStatus(wholesaleStatusReqVo);
    }

    @PostMapping("/export")
    @Operation(summary = "商户导出批发订单Excel")
    //@PreAuthorize("@ss.hasPermission('charge:orders:export')")
    public void exportWholesaleOrderExcel(HttpServletResponse response, @RequestBody WholesaleOrderReqVo wholesaleOrder) {
        wholesaleOrdersService.exportWholesaleOrderExcel(wholesaleOrder, response);
    }
}
