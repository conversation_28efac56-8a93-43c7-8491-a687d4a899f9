package com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-26 14:56:03
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateWholesaleStatusReqVo {

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "状态：3")
    private String status;
}
