package com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-26 10:15:39
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WholesaleOrderPage {

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "买家姓名")
    private String buyerName;

    @Schema(description = "买家电话")
    private String buyerPhone;

    @Schema(description = "支付金额")
    private Double totalMoney;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "状态对应字符串")
    private String statusStr;

    @Schema(description = "下单时间")
    private String insertTime;

    @Schema(description = "支付成功时间")
    private String endTime;

    @Schema(description = "订单详情")
    private String goodsMsg;
}
