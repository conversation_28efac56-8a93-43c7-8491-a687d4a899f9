package com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-06-26 10:15:02
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WholesaleOrderReqVo extends PageParam {

    @Schema(description = "商户号")
    private String shopId;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "买家电话")
    private String phone;

    @Schema(description = "场景")
    private Integer scene;
}

