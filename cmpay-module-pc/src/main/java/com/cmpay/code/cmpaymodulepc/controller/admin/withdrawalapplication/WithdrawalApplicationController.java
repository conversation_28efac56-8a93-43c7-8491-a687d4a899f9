package com.cmpay.code.cmpaymodulepc.controller.admin.withdrawalapplication;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cmpay.code.cmpaymodulepc.controller.admin.withdrawalapplication.vo.BatchGtMerchantImportExcel;
import com.cmpay.code.cmpaymodulepc.service.withdrawalapplication.WithdrawalApplicationService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

import static com.cmpay.code.framework.common.exception.GlobalErrorCodePc.READ_FILE_ERROR;
import static com.cmpay.code.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 提现申请")
@RestController
@RequestMapping("/withdrawal-application")
@Validated
public class WithdrawalApplicationController {

    @Resource
    private WithdrawalApplicationService withdrawalApplicationService;


    @PostMapping("/batch-gt-merchant-import")
    @Operation(summary = "问题处理——批量开通提现按钮")
    public CommonResult<Object> batchGtMerchantImport(MultipartFile file, String channel) {

        List<BatchGtMerchantImportExcel> list = null;
        if (file != null) {
            try {
                list = ExcelUtils.read(file, BatchGtMerchantImportExcel.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            throw exception(READ_FILE_ERROR);
        }
        return success(withdrawalApplicationService.batchGtMerchantImport(list, channel));
    }
}
