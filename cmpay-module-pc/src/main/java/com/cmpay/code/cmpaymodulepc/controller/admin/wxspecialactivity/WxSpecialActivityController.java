package com.cmpay.code.cmpaymodulepc.controller.admin.wxspecialactivity;

import com.cmpay.code.cmpaymodulepc.controller.admin.wxspecialactivity.vo.WxSpecialActivityMerchantReqVO;
import com.cmpay.code.cmpaymodulepc.controller.admin.wxspecialactivity.vo.WxSpecialActivityMerchantRespVO;
import com.cmpay.code.cmpaymodulepc.service.wxspecialactivity.WxSpecialActivityService;
import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

@Tag(name = "管理后台 - 微信活动报名")
@RestController
@RequestMapping("/wx-special-merchant")
@Validated
public class WxSpecialActivityController {
    @Resource
    private WxSpecialActivityService wxSpecialActivityService;

    @PostMapping("/getPage")
    @Operation(summary = "微信活动报名商户分页")
    @PermitAll
    public CommonResult<PageResult<WxSpecialActivityMerchantRespVO>> getPage(@RequestBody WxSpecialActivityMerchantReqVO wxSpecialActivityMerchantReqVO){
        return CommonResult.success(wxSpecialActivityService.queryWxSpecialActivityList(wxSpecialActivityMerchantReqVO));
    }


}
