package com.cmpay.code.cmpaymodulepc.controller.admin.wxspecialactivity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WxSpecialActivityMerchantRespVO {
    @Schema(description = "短商户号")
    private String trueid;
    private String shopId;
    @Schema(description = "政策类型")
    private String mccType;
    @Schema(description = "商户简称")
    private String shopNickName;
    @Schema(description = "市id")
    private String partnerId;
    @Schema(description = "市级公司名")
    private String partnerName;
    @Schema(description = "区县id")
    private String agentId;
    @Schema(description = "区县公司名")
    private String agentName;
    @Schema(description = "营业所id")
    private String acceptId;
    @Schema(description = "营业所公司名")
    private String acceptName;
    @Schema(description = "微信活动报名申请状态")
    private String applyStatus;
    @Schema(description = "微信活动报名开始时间")
    private String startTime;
    @Schema(description = "微信活动报名结束时间")
    private String endTime;
    @Schema(description = "商户通道")
    private String payType;
    @Schema(description = "当前费率")
    private String rate;
    @Schema(description = "成本费率")
    private String rateRuiyinxin;
    @Schema(description = "商户注册时间")
    private String registerTime;
    private String account;
    private String errorMsg;
    private String mccTypeStr;
    private String applyStatusStr;
    private String payTypeStr;
}
