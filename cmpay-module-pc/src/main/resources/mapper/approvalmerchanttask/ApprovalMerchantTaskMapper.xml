<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymoduleagent.dal.dataobject.approvalmerchanttask.ApprovalMerchantTaskMapper">
    <update id="updateByApprovalId" >
        update approval_merchant_task
        <set>
            status = #{status},
        <if test="time !=null and time !=''">
            expiration_time = #{time}
        </if>
        </set>
        where approval_id = #{approvalId}
    </update>
</mapper>