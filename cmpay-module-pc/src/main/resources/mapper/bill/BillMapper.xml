<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.bill.BillMapper">

    <select id="getPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.bill.vo.BillRespVO">
        select a.id,a.bill_name,a.stu_no,a.projects,a.money,a.description,a.order_id,a.status,a.pay_type,b.stu_name,
               b.stu_class,(case when a.pay_time='0000-00-00 00:00:00' then '' else a.pay_time end) as pay_time,c.sub_openid
            from edu_bill a left join edu_students b on a.stu_no=b.stu_no
            left join edu_binding c on c.stu_no=a.stu_no where a.shop_id=#{bpr.shopId}
            <if test="bpr.billName != null and bpr.billName != ''">
                and a.bill_name = #{bpr.billName}
            </if>
            <if test="bpr.stuName != null and bpr.stuName != ''">
                and b.stu_name = #{bpr.stuName}
            </if>
            <if test="bpr.insertTime != null and bpr.insertTime != ''">
                and a.insert_time = #{bpr.insertTime}
            </if>
            <if test="bpr.status != null and bpr.status != ''">
                and a.status = #{bpr.status}
            </if>
            <if test="bpr.stuNo != null and bpr.stuNo != ''">
                and a.stu_no = #{bpr.stuNo}
            </if>
            <if test="bpr.stuClass != null and bpr.stuClass != ''">
                and b.stu_class = #{bpr.stuClass}
            </if>
    </select>

</mapper>