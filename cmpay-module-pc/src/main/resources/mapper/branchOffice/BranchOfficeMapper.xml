<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.branchoffice.BranchOfficeMapper">

    <!-- 初始化省级公司 -->
    <select id="initBranchOffice"
            resultType="com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo">
        select id,company FROM branch_office
        <where>
             and id !=99
            <if test="initBranchReqVO.company != null and initBranchReqVO.company != ''">
                and company like concat('%',#{initBranchReqVO.company},'%')
            </if>
            <if test="initBranchReqVO.isReport != null and initBranchReqVO.isReport != '' and initBranchReqVO.isReport == '1'.toString()">
                and status = '1'
                and id != '1'
                <if test="initBranchReqVO.isGd != null and initBranchReqVO.isGd != '' and initBranchReqVO.isGd == '1'.toString()">
                    and id = '2'
                </if>
                <if test="initBranchReqVO.isGd != null and initBranchReqVO.isGd != '' and initBranchReqVO.isGd == '0'.toString()">
                    and id != '2'
                </if>
            </if>
            <if test="initBranchReqVO.isReport != null and initBranchReqVO.isReport != '' and initBranchReqVO.isReport == '2'.toString()">
                and status = '1'
                and id != '1'
                and id in ('3','6','7','9')
            </if>
        </where>
        order by insert_time
    </select>

    <select id="selectNameById"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.branchoffice.BranchOfficeDo">
        select * FROM branch_office where id = #{id} order by insert_time
    </select>

    <select id="selectByCompany"
            resultType="java.lang.String">
        select id from branch_office where company like concat('%',#{company},'%')
    </select>
</mapper>