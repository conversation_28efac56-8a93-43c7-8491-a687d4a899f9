<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.brandextend.BrandExtendMapper">

    <select id="selectAllPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.brandextend.vo.BrandExtendPageRespVO">
      select device_id as device_id,brand_msg_id as brand_msg_id,device_token as device_token,
             case when is_online='1' then '在线' when is_online = '0' then '离线' else '未知' end as is_online,
             `signal` as de_signal,electricity as electricity,is_arrear as is_arrear,update_time as update_time,
             case when is_bind_license = 1 then '开通' else '未开通' end as is_bind_license,
             if_use ,branch_id ,insert_time,is_play_audio,if_broadcast_cancel,firmware_version from brand_extend
        <where>
            <if test="ber.deviceId!=null and ber.deviceId!=''">
                device_id =#{ber.deviceId}
            </if>
            <if test="ber.brandMsgId!=null and ber.brandMsgId!=''">
                and brand_msg_id = #{ber.brandMsgId}
            </if>
            <if test="ber.branchId!=null and ber.branchId!=''">
                and branch_id =#{ber.branchId}
            </if>
        </where>
        order by update_time desc
  </select>

    <!-- 根据设备品牌id查询最大的设备SN号 -->
    <select id="searchMaxDeviceId"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.brandextend.BrandExtendDO">
        select max(device_id) as device_id from brand_extend
        <where>
            device_id LIKE CONCAT(#{brandSx},'%')
            <if test="brandMsgId != null and brandMsgId != ''">
                and brand_msg_id = #{brandMsgId}
            </if>
        </where>
        order by insert_time desc limit 1
    </select>

    <!-- 根据sn号查询云喇叭品牌 -->
    <select id="getYlbBrandByAudioHornId" resultType="java.lang.String">
        select bm.brand_id
        from brand_extend be
                 left join brand_msg bm on be.brand_msg_id = bm.id
        where device_id = #{deviceId}
    </select>

    <!-- 根据sn号查询支付宝盒子的供应商id -->
    <select id="searchBrandMsgVersion" resultType="java.lang.String">
        select a.firmware_version
        from brand_extend a,
             brand_msg b
        where a.brand_msg_id = b.id
          and a.device_id = #{audioHornId}
          and (b.brand_id = #{alipayBox} or b.brand_id = 'things')
    </select>

    <!-- 根据设备id查询品牌id -->
    <select id="getBrandMsgIdByDeviceId" resultType="java.lang.String">
        select b.id
        from brand_extend a,
             brand_msg b
        where a.brand_msg_id = b.id
          and a.device_id = #{deviceId}
    </select>

    <!-- 查询支付宝公参 -->
    <select id="queryPublicConstant" resultType="java.lang.String">
        select constant_value from public_constant where constant_type = #{constantType};
    </select>

    <!-- 根据sn号与设备类型查询是否存在 -->
    <select id="selectListByDeviceIdAndBrandtype"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.brandextend.BrandExtendDO">
        SELECT be.*
        FROM brand_extend be
        LEFT JOIN brand_msg bm ON be.brand_msg_id = bm.id
        WHERE be.device_id = #{deviceId} and bm.brand_type = #{brandType}
    </select>
</mapper>