<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.canteenemployee.CanteenEmployeeMapper">

    <!-- 食堂收费管理——人员管理——添加人员 -->
    <insert id="createEmployee">
        insert into canteen.employee(employee_id,name,type,organization,shop_id)
        values(#{createReqVO.employeeId},#{createReqVO.employeeName},#{createReqVO.type},#{createReqVO.organization},#{createReqVO.shopId})
    </insert>

    <!-- 食堂收费管理——人员管理——更新人员 -->
    <update id="updateEmployee">
        update canteen.employee
        <set>
            <if test="updateReqVO.employeeName ！= null and updateReqVO.employeeName != ''">
                `name` = #{updateReqVO.employeeName}
            </if>
            <if test="updateReqVO.type != null and updateReqVO.type != ''">
                type = #{updateReqVO.type}
            </if>
            <if test="updateReqVO.organization != null and updateReqVO.organization != ''">
                organization = #{updateReqVO.organization}
            </if>
        </set>
        where id = #{updateReqVO.id}
    </update>

    <!-- 食堂收费管理——人员管理——重新认证 -->
    <update id="resetEmployee">
        update canteen.employee set openid = '',status = '0'
        where id = #{resetEmployeeReq.id} and shop_id = #{resetEmployeeReq.shopId}
    </update>

    <!-- 食堂收费管理——人员管理——删除人员 -->
    <delete id="deleteEmployee">
        delete from canteen.employee where id = #{updateReqVO.id}
    </delete>

    <!-- 食堂收费管理——人员管理分页 -->
    <select id="searchEmployeePage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.canteenemployee.vo.EmployeePage">
        select id,employee_id,`name` employee_name,type,organization,`status`
        from canteen.employee
        <where>
            and status != 2
            <if test="employeePageReqVo.shopId != null and employeePageReqVo.shopId != ''">
                and shop_id = #{employeePageReqVo.shopId}
            </if>
            <if test="employeePageReqVo.employeeName != null and employeePageReqVo.employeeName != ''">
                and `name` like concat('%',#{employeePageReqVo.employeeName},'%')
            </if>
            <if test="employeePageReqVo.employeeId != null and employeePageReqVo.employeeId != ''">
                and employee_id = #{employeePageReqVo.employeeId}
            </if>
        </where>
        order by auth_time desc
    </select>

    <!-- 查询员工编号是否存在 -->
    <select id="getEmployeeId" resultType="java.lang.String">
        select employee_id from canteen.employee where employee_id = #{employeeId} and status != 2
    </select>

    <!-- 根据id查询人员 -->
    <select id="getEmployee"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenemployee.CanteenEmployeeDO">
        select id,employee_id,`name`,`type`,organization,openid,status,auth_time,shop_id
        from canteen.employee
        where id = #{id}
    </select>
</mapper>
