<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.canteenmealprogrem.CanteenMealProgremMapper">

    <!-- 食堂收费管理——套餐管理——添加套餐 -->
    <insert id="insertMealProgrem">
        insert into canteen.meal_progrem (`type`,original_price,discount_price,shop_id)
        values (#{createReqVO.type},#{createReqVO.originalPrice},#{createReqVO.discountPrice},#{createReqVO.shopId})
    </insert>

    <!-- 食堂收费管理——套餐管理——修改套餐 -->
    <update id="updateMealProgrem">
        update canteen.meal_progrem
        <set>
            <if test="updateReqVO.type != null and  updateReqVO.type != ''">
                `type` = #{updateReqVO.type},
            </if>
            <if test="updateReqVO.originalPrice != null and  updateReqVO.originalPrice != ''">
                original_price = #{updateReqVO.originalPrice},
            </if>
            <if test="updateReqVO.discountPrice != null and updateReqVO.discountPrice != ''">
                discount_price = #{updateReqVO.discountPrice}
            </if>
        </set>
        where id = #{updateReqVO.id}
   </update>

    <!-- 食堂收费管理——套餐管理分页 -->
    <select id="searchMealProgremPage"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenmealprogrem.CanteenMealProgremDO">
        select id, `type`, original_price, discount_price, shop_id
        from canteen.meal_progrem
        where shop_id = #{mealProgremPageReqVo.shopId}
        <if test="mealProgremPageReqVo.type != null and mealProgremPageReqVo.type != ''">
            and `type` like concat('%',#{mealProgremPageReqVo.type},'%')
        </if>
        order by id desc
    </select>

    <!-- 食堂收费管理——套餐管理——删除套餐 -->
    <delete id="deleteMealProgrem">
        delete from canteen.meal_progrem where id = #{id}
    </delete>

    <select id="getMealProgrem"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenmealprogrem.CanteenMealProgremDO">
        select id, `type`, original_price, discount_price, shop_id
        from canteen.meal_progrem
        where id = #{id}
    </select>

    <select id="searchMealProgremName"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenmealprogrem.CanteenMealProgremDO">
        select id, `type`, original_price, discount_price, shop_id
        from canteen.meal_progrem
        where `type` = #{type}
        and shop_id = #{shopId}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>
</mapper>
