<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.canteenorders.CanteenOrdersMapper">


    <!-- 学校送奶管理——订单信息分页 -->
    <select id="searchMilkOrdersPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo.MilkOrdersPage">
        select order_id,(case when pay_time='0000-00-00 00:00:00' then '' else pay_time end) pay_time,project milk_project,school,st_grade,st_class,st_name,status,money,phone
        from canteen.orders
        <where>
            and meal_name = 'milk'
            <if test="milkOrdersPage.shopId != null and milkOrdersPage.shopId != ''">
                and shop_id = #{milkOrdersPage.shopId}
            </if>
            <if test="milkOrdersPage.milkProject != null and milkOrdersPage.milkProject != ''">
                and project like concat('%',#{milkOrdersPage.milkProject},'%')
            </if>
            <if test="milkOrdersPage.start != null and milkOrdersPage.start != ''">
                and insert_time <![CDATA[  >=  ]]> #{milkOrdersPage.start}
            </if>
            <if test="milkOrdersPage.end != null and milkOrdersPage.end != ''">
                and insert_time <![CDATA[  <=  ]]> #{milkOrdersPage.end}
            </if>
            <if test="milkOrdersPage.orderId != null and milkOrdersPage.orderId != ''">
                and order_id = #{milkOrdersPage.orderId}
            </if>
            <if test="milkOrdersPage.school != null and milkOrdersPage.school != ''">
                and school like concat('%',#{milkOrdersPage.school},'%')
            </if>
            <if test="milkOrdersPage.stClass != null and milkOrdersPage.stClass != ''">
                and st_class = #{milkOrdersPage.stClass}
            </if>
            <if test="milkOrdersPage.stName != null and milkOrdersPage.stName != ''">
                and st_name like concat('%',#{milkOrdersPage.stName},'%')
            </if>
            <if test="milkOrdersPage.milkStatus != null and milkOrdersPage.milkStatus != ''">
                and status = #{milkOrdersPage.milkStatus}
            </if>
        </where>
        ORDER BY insert_time DESC
    </select>

    <!-- 食堂收费管理——订单信息分页 -->
    <select id="searchShiTangPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.canteenorders.vo.ShiTangPage">
        select o.order_id,o.employee_id,o.meal_name,(case when o.pay_time='0000-00-00 00:00:00' then '' else o.pay_time end) pay_time,o.status,o.money,e.name,e.organization
        from canteen.orders o
        left join canteen.employee e
        on o.employee_id = e.employee_id
        <where>
            and o.shop_id = #{shiTangPage.shopId}
            <if test="shiTangPage.stConsumer != null and shiTangPage.stConsumer != ''">
                and e.name like concat('%',#{shiTangPage.stConsumer},'%')
            </if>
            <if test="shiTangPage.start != null and shiTangPage.start != ''">
                and o.insert_time <![CDATA[  >=  ]]> #{shiTangPage.start}
            </if>
            <if test="shiTangPage.end != null and shiTangPage.end != ''">
                and o.insert_time <![CDATA[  <=  ]]> #{shiTangPage.end}
            </if>
            <if test="shiTangPage.stOrderId != null and shiTangPage.stOrderId != ''">
                and o.order_id = #{shiTangPage.stOrderId}
            </if>
            <if test="shiTangPage.mealName != null and shiTangPage.mealName != ''">
                and o.meal_name = #{shiTangPage.mealName}
            </if>
        </where>
        order by o.insert_time desc
    </select>
</mapper>
