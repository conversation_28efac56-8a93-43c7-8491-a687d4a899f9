<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.canteenproject.CanteenProjectMapper">

    <!-- 添加送奶项目 -->
    <insert id="insertProject">
        insert into canteen.project (`name`,price,shop_id,insert_time)
        values (#{createReqVO.project},#{createReqVO.price},#{createReqVO.shopId},#{createReqVO.insertTime})
    </insert>

    <!-- 更新送奶项目 -->
    <update id="updateProject">
        update canteen.project
        <set>
            <if test="updateReqVO.project != null and updateReqVO.project != ''">
                `name` = #{updateReqVO.project}
            </if>
            <if test="updateReqVO.price != null and updateReqVO.price != ''">
                price = #{updateReqVO.price}
            </if>
        </set>
        where project_id = #{updateReqVO.projectId}
    </update>

    <!-- 删除送奶项目 -->
    <delete id="deleteProject">
        delete from canteen.project where project_id = #{projectId}
    </delete>

    <!-- 送奶项目分页 -->
    <select id="searchProjectPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.canteenproject.vo.ProjectPage">
        select project_id,`name` project_name,price
        from canteen.project
        <where>
            <if test="projectPage.shopId != null and projectPage.shopId != ''">
                and shop_id = #{projectPage.shopId}
            </if>
            <if test="projectPage.milkProject != null and projectPage.milkProject != ''">
                and `name` like concat('%',#{projectPage.milkProject},'%')
            </if>
        </where>
        order by insert_time desc
    </select>

    <!--  根据id查询该对象  -->
    <select id="findById"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenproject.CanteenProjectDO">
        select project_id,`name`,price,shop_id,insert_time
        from canteen.project
        where project_id = #{projectId}
    </select>
</mapper>