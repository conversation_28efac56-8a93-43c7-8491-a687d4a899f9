<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.canteenschool.CanteenSchoolMapper">

    <!-- 创建送奶学校 -->
    <insert id="insertSchool">
        insert into canteen.school (`name`,address,shop_id,insert_time)
        values (#{createReqVO.name},#{createReqVO.address},#{createReqVO.shopId},#{createReqVO.insertTime})
    </insert>

    <!-- 更新送奶学校 -->
    <update id="updateSchool">
        update canteen.school
        <set>
            <if test="updateReqVO.name != null and updateReqVO.name != ''">
                `name` = #{updateReqVO.name}
            </if>
            <if test="updateReqVO.address != null and updateReqVO.address != ''">
                address = #{updateReqVO.address}
            </if>
        </set>
        where school_id = #{updateReqVO.schoolId}
    </update>

    <!-- 根据id删除学校-->
    <delete id="deleteSchool">
        delete from canteen.school where school_id = #{schoolId}
    </delete>

    <!-- 送奶学校分页 -->
    <select id="searchSchoolPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.canteenschool.vo.SchoolPage">
        select school_id,`name`,address
        from canteen.school
        <where>
            <if test="schoolPageReqVo.shopId != null and schoolPageReqVo.shopId != ''">
                and shop_id = #{schoolPageReqVo.shopId}
            </if>
            <if test="schoolPageReqVo.milkSchool != null and schoolPageReqVo.milkSchool != ''">
                and `name` like concat('%',#{schoolPageReqVo.milkSchool},'%')
            </if>
        </where>
        order by insert_time desc
    </select>

    <!-- 根据id查询学校 -->
    <select id="getById"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.canteenschool.CanteenSchoolDO">
        select *
        from canteen.school
        where school_id = #{schoolId}
    </select>
</mapper>