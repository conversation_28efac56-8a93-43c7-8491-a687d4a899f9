<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.channel.ChannelMapper">

    <!-- 获取支付通道 -->
    <select id="getChannel" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.channel.ChannelDO">
        select id,channel_value,channel_name,channel_abbreviation,create_time,update_time,status,is_signin,is_test_pay,is_change_rate,is_change_card,is_set_appid_path,is_wx_auth,is_alipay_auth,is_alipay_level,is_channel_contract,is_weixin,is_alipay,is_unionpay,is_dcpay
        from channel
        <where>
            <if test="type == 'weixin'">
                is_weixin = '1'
            </if>
            <if test="type == 'alipay'">
                is_alipay = '1'
            </if>
            <if test="type == 'unionpay'">
                is_unionpay = '1'
            </if>
            <if test="type == 'dcpay'">
                is_dcpay = '1'
            </if>
        </where>
    </select>

    <!-- 根据通道key查询通道信息 -->
    <select id="findByChannelValue" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.channel.ChannelDO">
        select id,channel_value,channel_name,channel_abbreviation,create_time,update_time,status,is_signin,is_test_pay,is_change_rate,is_change_card,is_set_appid_path,is_wx_auth,is_alipay_auth,is_alipay_level,is_channel_contract,is_weixin,is_alipay,is_unionpay,is_dcpay
        from channel
        <where>
            <if test="channelKey != null and channelKey != ''">
                channel_value = #{channelKey}
            </if>
        </where>
    </select>

    <select id="searchChannelMsg"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.channel.vo.MerchantPayChannelMsg">
        select id,channel_value,channel_name,channel_abbreviation,create_time,update_time,status,is_signin,is_test_pay,is_change_rate,is_change_card,is_set_appid_path,is_wx_auth,is_alipay_auth,
               is_alipay_level,is_channel_contract,is_set_channel_mch_id,is_weixin,is_alipay,is_unionpay,is_dcpay,is_fast,is_search_merchant_status,is_open_dcpay
        from channel
    </select>
    <!-- 查询商户所支持的通道 -->
    <select id="searchMerchantChannel" resultType="java.lang.String">
        <choose>
            <when test="type == 'noAll'">
                SELECT mc.channel
                FROM merchant m
                LEFT JOIN merchant_channel mc
                ON mc.shop_id = m.shop_id
                WHERE m.shop_id = #{shopId}
            </when>
            <when test="type == 'all'">
                SELECT mc.channel
                FROM merchant m
                LEFT JOIN merchant_channel mc
                ON mc.shop_id = m.shop_id
                WHERE m.shop_id = #{shopId}
                AND mc.status = '1'
                UNION
                SELECT ps.settle_channel
                FROM merchant m
                LEFT JOIN partner_setting ps
                ON ps.partner_id = m.partner_id
                WHERE m.shop_id = #{shopId}
                AND ps.status = '1'
            </when>
        </choose>
    </select>
    <!-- 根据通道查询通道信息 -->
    <select id="findByChannel" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.channel.ChannelDO">
        select id,channel_value,channel_name,channel_abbreviation,create_time,update_time,status,is_signin,is_test_pay,is_change_rate,is_change_card,is_set_appid_path,is_wx_auth,is_alipay_auth,
               is_alipay_level,is_channel_contract,is_set_channel_mch_id,is_weixin,is_alipay,is_unionpay,is_dcpay,is_fast,is_search_merchant_status,is_open_dcpay
        from channel
        where channel_value = #{channel}
    </select>

</mapper>