<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.channelsubsidymerchantorders.ChannelSubsidyMerchantOrdersMapper">

    <!-- 补贴明细分页 -->
    <select id="getSubsidyDetailPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo.SubsidyDetailVo">
        select a.money subsidyMoney,a.context content,a.insert_time,a.pay_time,a.order_id,a.shop_order_id,a.status,(case when a.status='0' then '未补贴' when a.status='1' then '已补贴' when a.status='5' then '已补贴'  when a.status='2' then '补贴失败' when a.status='3' then '已撤销' when a.status='4' then '未清分'  else '未知' end) status_str,a.shop_name,a.accept_id,a.shop_id,a.channel,c.channel_abbreviation channel_str,y.partner_id,y.agent_id,b.trueid,y.company_accept accept_name,y.company_agent agent_name,y.company_partner partner_name
        from channel_subsidy_merchant_orders a
        left join merchant b on a.shop_id = b.shop_id
        left join yz_org y ON y.accept_id = a.accept_id
        LEFT JOIN channel c ON  c.channel_value = a.channel
        <where>
            <if test="subsidyDetailReqVo.shopId != null and subsidyDetailReqVo.shopId != ''">
                and a.shop_id = #{subsidyDetailReqVo.shopId}
            </if>
            <if test="subsidyDetailReqVo.start != null and subsidyDetailReqVo.start != ''">
                and a.insert_time <![CDATA[  >=  ]]> #{subsidyDetailReqVo.start}
            </if>
            <if test="subsidyDetailReqVo.end != null and subsidyDetailReqVo.end != ''">
                and a.insert_time <![CDATA[  <=  ]]> #{subsidyDetailReqVo.end}
            </if>
            <if test="subsidyDetailReqVo.orderId != null and subsidyDetailReqVo.orderId != ''">
                and a.order_id = #{subsidyDetailReqVo.orderId}
            </if>
            <if test="subsidyDetailReqVo.shopOrderId != null and subsidyDetailReqVo.shopOrderId != ''">
                and a.shop_order_id = #{subsidyDetailReqVo.shopOrderId}
            </if>
            <if test="subsidyDetailReqVo.acceptId != null and subsidyDetailReqVo.acceptId != ''">
                and b.accept_id = #{subsidyDetailReqVo.acceptId}
            </if>
            <if test="subsidyDetailReqVo.status != null and subsidyDetailReqVo.status != ''">
                <choose>
                    <when test="subsidyDetailReqVo.status == '1'.toString()">
                        and a.status in ('1','5')
                    </when>
                    <otherwise>
                        and a.status = #{subsidyDetailReqVo.status}
                    </otherwise>
                </choose>
            </if>
            <if test="subsidyDetailReqVo.subsidySource != null and subsidyDetailReqVo.subsidySource != ''">
                and a.source = #{subsidyDetailReqVo.subsidySource}
            </if>
            <if test="subsidyDetailReqVo.channel != null and subsidyDetailReqVo.channel != ''">
                and a.channel = #{subsidyDetailReqVo.channel}
            </if>
            <if test="subsidyDetailReqVo.agentId != null and subsidyDetailReqVo.agentId != ''">
                and b.agent_id = #{subsidyDetailReqVo.agentId}
            </if>
            <if test="subsidyDetailReqVo.partnerId != null and subsidyDetailReqVo.partnerId != ''">
                and b.partner_id = #{subsidyDetailReqVo.partnerId}
            </if>
            <if test="subsidyDetailReqVo.branchId != null and subsidyDetailReqVo.branchId != ''">
                and b.branch_id = #{subsidyDetailReqVo.branchId}
            </if>
        </where>
        order by a.insert_time desc
    </select>

    <!-- 补贴明细统计 -->
    <select id="statisticsSubsidyDetail"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorders.vo.StatisticsSubsidyDetailVo">
        SELECT COUNT(order_id) subsidyNum,SUM(money) subsidyMoney
        from channel_subsidy_merchant_orders
        <where>
            and status!=2
            <if test="subsidyDetailReqVo.shopId != null and subsidyDetailReqVo.shopId != ''">
                and shop_id = #{subsidyDetailReqVo.shopId}
            </if>
            <if test="subsidyDetailReqVo.start != null and subsidyDetailReqVo.start != ''">
                and insert_time <![CDATA[  >=  ]]> #{subsidyDetailReqVo.start}
            </if>
            <if test="subsidyDetailReqVo.end != null and subsidyDetailReqVo.end != ''">
                and insert_time <![CDATA[  <=  ]]> #{subsidyDetailReqVo.end}
            </if>
            <if test="subsidyDetailReqVo.orderId != null and subsidyDetailReqVo.orderId != ''">
                and order_id = #{subsidyDetailReqVo.orderId}
            </if>
            <if test="subsidyDetailReqVo.shopOrderId != null and subsidyDetailReqVo.shopOrderId != ''">
                and shop_order_id = #{subsidyDetailReqVo.shopOrderId}
            </if>
            <if test="subsidyDetailReqVo.acceptId != null and subsidyDetailReqVo.acceptId != ''">
                and accept_id = #{subsidyDetailReqVo.acceptId}
            </if>
            <if test="subsidyDetailReqVo.status != null and subsidyDetailReqVo.status != ''">
                <choose>
                    <when test="subsidyDetailReqVo.status == '1'.toString()">
                        and status in ('1','5')
                    </when>
                    <otherwise>
                        and status = #{subsidyDetailReqVo.status}
                    </otherwise>
                </choose>
            </if>
            <if test="subsidyDetailReqVo.subsidySource != null and subsidyDetailReqVo.subsidySource != ''">
                and source = #{subsidyDetailReqVo.subsidySource}
            </if>
            <if test="subsidyDetailReqVo.channel != null and subsidyDetailReqVo.channel != ''">
                and channel = #{subsidyDetailReqVo.channel}
            </if>
            <if test="subsidyDetailReqVo.agentId != null and subsidyDetailReqVo.agentId != ''">
                and agent_id = #{subsidyDetailReqVo.agentId}
            </if>
        </where>
        order by insert_time desc
    </select>

</mapper>