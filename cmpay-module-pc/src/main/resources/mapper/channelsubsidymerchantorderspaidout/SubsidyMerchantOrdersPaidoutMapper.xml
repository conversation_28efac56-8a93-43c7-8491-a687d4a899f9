<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.channelsubsidymerchantorderspaidout.SubsidyMerchantOrdersPaidoutMapper">

    <!-- 邮政权益平台——结算明细分页 -->
    <select id="getPaidoutDetailPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.channelsubsidymerchantorderspaidout.vo.PaidoutDetailVo">
        select a.money,DATE_FORMAT(a.paidout_day, '%Y-%m-%d') paidout_day,a.stat_day subsidy_day,a.status paidout_status,(case when a.status='1' then '已结算' when a.status='0' then '未结算' else '结算失败' end) paidout_status_str,a.shop_name,a.accept_id, ac.company accept_name,a.shop_id,b.trueid,a.channel,c.channel_abbreviation channel_str,a.partner_id,pc.company partner_name,a.agent_id,ag.company agent_name
        from channel_subsidy_merchant_orders_paidout a
        left join merchant b
        on a.shop_id = b.shop_id
        LEFT JOIN agent ag ON ag.agent_id = a.agent_id
        LEFT JOIN accept ac ON ac.accept_id = a.accept_id
        LEFT JOIN partner_channel pc ON pc.partner_id = a.partner_id
        LEFT JOIN channel c ON a.channel = c.channel_value
        <where>
            <if test="paidoutDetailReqVo.shopId != null and paidoutDetailReqVo.shopId != ''">
                and a.shop_id = #{paidoutDetailReqVo.shopId}
            </if>
            <if test="paidoutDetailReqVo.start != null and paidoutDetailReqVo.start != ''">
                and a.paidout_day <![CDATA[  >=  ]]> #{paidoutDetailReqVo.start}
            </if>
            <if test="paidoutDetailReqVo.end != null and paidoutDetailReqVo.end != ''">
                and a.paidout_day <![CDATA[  <=  ]]> DATE_ADD(#{paidoutDetailReqVo.end}, INTERVAL 1 DAY)
            </if>
            <if test="paidoutDetailReqVo.paidoutStatus != null and paidoutDetailReqVo.paidoutStatus != ''">
                and a.status = #{paidoutDetailReqVo.paidoutStatus}
            </if>
            <if test="paidoutDetailReqVo.channel != null and paidoutDetailReqVo.channel != ''">
                and a.channel = #{paidoutDetailReqVo.channel}
            </if>
            <if test="paidoutDetailReqVo.acceptId != null and paidoutDetailReqVo.acceptId != ''">
                and a.accept_id = #{paidoutDetailReqVo.acceptId}
            </if>
            <if test="paidoutDetailReqVo.agentId != null and paidoutDetailReqVo.agentId != ''">
                and a.agent_id = #{paidoutDetailReqVo.agentId}
            </if>
            <if test="paidoutDetailReqVo.partnerId != null and paidoutDetailReqVo.partnerId != ''">
                and a.partner_id = #{paidoutDetailReqVo.partnerId}
            </if>
            <if test="paidoutDetailReqVo.branchId != null and paidoutDetailReqVo.branchId != ''">
                and b.branch_id = #{paidoutDetailReqVo.branchId}
            </if>
        </where>
        order by a.paidout_day desc
    </select>

    <!-- 邮政权益平台——统计结算明细 -->
    <select id="getStatisticsPaidoutDetail" resultType="java.lang.Double">
        select SUM(money)
        from channel_subsidy_merchant_orders_paidout
        <where>
            <if test="paidoutDetailReqVo.shopId != null and paidoutDetailReqVo.shopId != ''">
                and shop_id = #{paidoutDetailReqVo.shopId}
            </if>
            <if test="paidoutDetailReqVo.start != null and paidoutDetailReqVo.start != ''">
                and paidout_day <![CDATA[  >=  ]]> #{paidoutDetailReqVo.start}
            </if>
            <if test="paidoutDetailReqVo.end != null and paidoutDetailReqVo.end != ''">
                and paidout_day <![CDATA[  <=  ]]> DATE_ADD(#{paidoutDetailReqVo.end}, INTERVAL 1 DAY)
            </if>
            <if test="paidoutDetailReqVo.paidoutStatus != null and paidoutDetailReqVo.paidoutStatus != ''">
                and status = #{paidoutDetailReqVo.paidoutStatus}
            </if>
            <if test="paidoutDetailReqVo.channel != null and paidoutDetailReqVo.channel != ''">
                and channel = #{paidoutDetailReqVo.channel}
            </if>
            <if test="paidoutDetailReqVo.acceptId != null and paidoutDetailReqVo.acceptId != ''">
                and accept_id = #{paidoutDetailReqVo.acceptId}
            </if>
            <if test="paidoutDetailReqVo.agentId != null and paidoutDetailReqVo.agentId != ''">
                and agent_id = #{paidoutDetailReqVo.agentId}
            </if>
        </where>
    </select>
</mapper>
