<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.channeltype.ChannelTypeMapper">

    <!-- 根据通道key查询通道信息 -->
    <select id="findByChannelValue" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.channel.ChannelDO">
        select id,channel_value,channel_name,channel_abbreviation,create_time,update_time,status,is_signin,is_test_pay,is_change_rate,is_change_card,is_set_appid_path,is_wx_auth,is_alipay_auth,is_alipay_level,is_channel_contract,is_weixin,is_alipay,is_unionpay,is_dcpay
        from channel
        <where>
            <if test="channel != null and channel != ''">
                channel_value = #{channel}
            </if>
        </where>
    </select>
</mapper>