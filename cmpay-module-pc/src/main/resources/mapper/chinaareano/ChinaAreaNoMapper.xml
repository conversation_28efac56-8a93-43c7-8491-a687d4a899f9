<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.chinaareano.ChinaAreaNoMapper">

    <!-- 获取地区编码 -->
    <select id="getCityCodeFromCity" resultType="java.lang.String">
        select area_no
        from china_area_no
        where locate(#{province}, province)
          and locate(#{city}, city)
          and locate(#{area}, dist)
    </select>

    <!-- 查询所有省 -->
    <select id="searchProvinceAll"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.chinaareano.ChinaAreaNoDO">
        SELECT * FROM `china_area_no` WHERE province is not null and city is null and dist is null
        <if test="company != null and company != ''">
            and province = #{company}
        </if>
    </select>

    <!-- 查询所有市 -->
    <select id="searchCityAll"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.chinaareano.ChinaAreaNoDO">
        SELECT * FROM `china_area_no` WHERE province is not null and city is not null and dist is null
        <if test="partnerId != null and partnerId != ''">
            and partner_id = #{partnerId}
        </if>
    </select>

    <!-- 查询所有区县 -->
    <select id="searchAreaAll"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.chinaareano.ChinaAreaNoDO">
        SELECT * FROM `china_area_no` WHERE province is not null and city is not null and dist is not null and dist !=
        ''
        <if test="cityCode != null and cityCode != ''">
            and partner_id = #{cityCode}
        </if>
        <if test="company != null and company != ''">
            and dist like concat('%',#{company},'%')
        </if>
    </select>

    <select id="searchAreaCode" resultType="java.lang.String">
        select area_no from china_area_no
        <where>
            <if test="province != null and province != ''">
                and province = #{province} and city is null and dist is null
            </if>
            <if test="city != null and city != ''">
                and city = #{city} and province is not null and city is not null and dist is null
            </if>
            <if test="area != null and area != ''">
                and dist = #{area} and province is not null and city is not null and dist is not null
            </if>
        </where>
    </select>

    <!-- 获取pc端市级行政区域 -->
    <select id="searchPcCityAll"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.chinaareano.ChinaAreaNoDO">
        SELECT c.* FROM `china_area_no` c LEFT JOIN partner_right pr ON c.area_no = pr.city_code
        WHERE c.province is not null and c.city is not null and c.dist is null
        <if test="partnerIdList != null and partnerIdList.size > 0">
            and pr.partner_id in
            <foreach collection="partnerIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="company != null and company != ''">
            and c.city like concat('%',#{company},'%')
        </if>
    </select>
    <!--    根据城市名称查询-->
    <select id="selectByCity"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.chinaareano.ChinaAreaNoDO">
        SELECT *
        FROM china_area_no
        WHERE city LIKE concat('%',#{cityName},'%')
          AND (dist IS NULL OR dist = '') ORDER BY id DESC LIMIT 1
    </select>


</mapper>