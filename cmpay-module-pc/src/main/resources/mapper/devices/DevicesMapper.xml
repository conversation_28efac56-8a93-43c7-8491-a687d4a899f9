<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.devices.DevicesMapper">

    <!-- 新增门店 -->
    <insert id="insertDevice">
        insert into devices(shop_id,device)
        values(
               #{shopId},#{deviceName}
               )
    </insert>

    <!-- 修改门店 -->
    <update id="updateDevice">
        update devices set device = #{deviceName},shop_id = #{shopId} where id = #{deviceId}
    </update>

    <!--  查询负责人和超管分店信息 -->
    <select id="initDevicesAll"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.InitDevicesRespVo">
        select distinct device,id as device_id
        from devices
        where shop_id = #{shopId} and status = 1  and device is not null and device != ''
        ORDER BY insert_time desc
    </select>

    <!-- 查询店长分店信息 -->
    <select id="initDevicesStore"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.InitDevicesRespVo">
        select distinct a.device,a.id as device_id
        from devices a, person_in_charge b
        where a.id=b.device_id
        and b.status=1
        and b.xmid = #{devicesReqVo.xmid}
        and a.shop_id = #{devicesReqVo.shopId}
        and a.status=1
        and a.device is not null and a.device != ''
        ORDER BY a.insert_time desc
    </select>
    
    <select id="selectDevicesPage" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.DevicesRespVO">
        select d.device,d.shop_id,d.insert_time,d.id,d.alipay_shop_id,d.shop_phone
        from devices d
        where d.status = 1
              <if test="pageReqVO.shopId!=null and pageReqVO.shopId!=''">
                  and d.shop_id = #{pageReqVO.shopId}
              </if>
              <if test="pageReqVO.device!=null and pageReqVO.device!=''">
                  and d.device = #{pageReqVO.device}
              </if>
              <if test="pageReqVO.shopPhone!=null and pageReqVO.shopPhone!=''">
                  and d.shop_phone = #{pageReqVO.shopPhone}
               </if>
              order by d.insert_time desc
    </select>


    <select id="get" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.DevicesDetailsRespVO">
        select d.*,u.`name`,d.city,d.shop_phone
        from devices d left join person_in_charge pic on pic.device_id=d.id left join user u on pic.xmid=u.xmid  and pic.status = 1
        where d.status = 1
        <if test="id!=null">
            and d.id = #{id}
        </if>
        group by d.device
    </select>

    <!-- 查询分店店长 -->
    <select id="getShopManager" resultType="java.lang.String">
        select GROUP_CONCAT(a.name) shop_manager_name
        from devices b,user a,person_in_charge c
        where c.xmid = a.xmid
        and  c.device_id = b.id
        and c.device_id = #{deviceId}
        and c.status = '1'
        and c.shop_id = #{shopId}
    </select>

    <!-- 根据商户号+分店名称查询分店 -->
    <select id="findDeviceByShopIdOrName"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.devices.DevicesDO">
        SELECT id,shop_id,device,short_key,province,city,area,address,shop_phone,insert_time,status,alipay_shop_id,alipay_store_id
        from devices
        <where>
            <if test="status != null and status != ''">
                status = #{status}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and device = #{deviceName}
            </if>
            <if test="shopId != null and shopId != ''">
                and shop_id = #{shopId}
            </if>
            <if test="deviceId != null and deviceId != ''">
                id != #{deviceId}
            </if>
        </where>
    </select>
</mapper>