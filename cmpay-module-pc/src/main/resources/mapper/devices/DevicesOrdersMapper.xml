<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.devices.devicesorders.DevicesOrdersMapper">
    <select id="getByDeviceId" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.devices.devicesorders.DevicesOrdersDO">
        select * from devices_orders
            <where>
                <if test="deviceId!=null and deviceId !=''">
                    device_id = #{deviceId}
                </if>
                <if test="status!=null and status !=''">
                    and status = #{status}
                </if>
                <if test="pid!=null and pid !=''">
                    and pid = #{pid}
                </if>
                 and merchant_id_type = 02
            </where>

    </select>

</mapper>