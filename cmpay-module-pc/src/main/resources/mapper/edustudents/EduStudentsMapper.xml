<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.edustudents.EduStudentsMapper">

    <select id="getPage" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.edustudents.vo.EduStudentsPageRespVO">
        select a.id,a.stu_no,a.stu_name,a.stu_class,c.openid,b.phone,b.`name`
            from edu_students a left join edu_binding c on a.stu_no=c.stu_no
                left join user b on c.openid=b.openid
        <where>
            a.shop_id= #{esp.shopId}
            <if test="esp.studentName!=null and esp.studentName != ''">
                and a.stu_name =  #{esp.studentName}
            </if>
            <if test="esp.studentNo != null and esp.studentNo != ''">
                and a.stu_no = #{esp.studentNo}
            </if>
            <if test="esp.studentClass !=null and esp.studentClass != ''">
                and a.stu_class = #{esp.studentClass}
            </if>
        </where>
        order by a.insert_time desc
    </select>
    <select id="getCanSendStudents" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.edustudents.vo.EduStudentsSendVO">
        select a.stu_no,a.stu_name,a.stu_class,c.sub_openid,b.bill_name,b.projects,b.money,b.id
            from edu_students a,edu_bill b,edu_binding c
                where a.stu_no=b.stu_no and a.stu_no=c.stu_no and a.shop_id=b.shop_id and b.status=0
                  <if test="shopId != null and shopId != ''">
                      and a.shop_id=#{shopId}
                  </if>
                  <if test="billName != null and billName != ''">
                      and b.bill_name=#{billName}
                  </if>
                  and c.sub_openid is not null

    </select>

</mapper>