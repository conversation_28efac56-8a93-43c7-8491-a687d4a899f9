<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchant.MerchantMapper">

    <!-- 查询用户密码 -->
    <select id="getPassword" resultType="java.lang.String">
        select refund_password
        from merchant
        where shop_id = #{shopId}
    </select>

    <select id="selectShopPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.MerchantShopRespVO">
        select * from merchant where
        <choose>
            <when test="mprv.status == '99'.toString()">
                status = 99
            </when>
            <otherwise>
                status <![CDATA[  <  ]]> 99
            </otherwise>
        </choose>
        <if test="mprv.shopId !=null and mprv.shopId != ''">
            and shop_id = #{mprv.shopId}
        </if>
        <if test="mprv.shopName !=null and mprv.shopName != ''">
            and (shop_name like concat('%',#{mprv.shopName},'%') or shop_nickname like concat('%',#{mprv.shopName},'%'))
        </if>
        <if test="mprv.keeper !=null and mprv.keeper != ''">
            and shop_keeper like concat('%',#{mprv.keeper},'%')
        </if>
        <if test="mprv.phone !=null and mprv.phone != ''">
            and phone = #{mprv.phone}
        </if>
        <if test="mprv.subType !=null and mprv.subType != ''">
            <choose>
                <when test=" mprv.subType == 'wxmaidan' or mprv.subType == 'cloudpay'">
                    and pay_type in ('wxmaidan','cloudpay')
                </when>
                <otherwise>
                    and (pay_type = #{mprv.subType} or pay_type_alipay = #{mprv.subType} or pay_type_unionpay =
                    #{mprv.subType} or pay_type_dcpay = #{mprv.subType})
                </otherwise>
            </choose>
        </if>
        <if test="mprv.ywyOpenid != null and mprv.ywyOpenid !=''">
            and ywy_openid = #{mprv.ywyOpenid}
        </if>
        <if test="mprv.start != null and mprv.start !='' and (mprv.shopId == null or mprv.shopId == '')">
            and register_time <![CDATA[  >=  ]]> #{mprv.start}
        </if>
        <if test="mprv.end != null and mprv.end !='' and (mprv.shopId == null or mprv.shopId == '')">
            and register_time <![CDATA[  <=  ]]> #{mprv.end}
        </if>
        <if test="mprv.acceptId != null and mprv.acceptId != ''">
            and accept_id = #{mprv.acceptId}
        </if>
        <if test="mprv.agentId != null and mprv.agentId != ''">
            and agent_id = #{mprv.agentId}
        </if>
        <if test="mprv.partnerId != null and mprv.partnerId != ''">
            and partner_id = #{mprv.partnerId}
        </if>
        <if test="mprv.branchId != null and mprv.branchId != ''">
            and branch_id = #{mprv.branchId}
        </if>
        <if test="mprv.keeperIdentity != null and mprv.keeperIdentity != ''">
            and keeper_identity = #{mprv.keeperIdentity}
        </if>

        <choose>
            <when test="mprv.upNoList !=null and mprv.upNoList.size > 0">
                and shop_id in
                <foreach collection="mprv.upNoList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        order by register_time desc limit 1000
    </select>


    <select id="selectByShopId" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchant.MerchantDO">
        select * from merchant
        <where>
            status !=99
            <if test="shopId != null and shopId != ''">
                and shop_id = #{shopId}
            </if>
        </where>
    </select>
    <select id="selectShopId" resultType="java.lang.String">
        SELECT shop_id
        FROM pay.merchant
        WHERE ywy_openid = #{ywyOpenid}
          AND agent_id = #{agentId}
          AND accept_id = #{accpetId}
          AND status != 99
    </select>

    <select id="selectByAgentAccept" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchant.MerchantDO">
        SELECT *
        FROM pay.merchant
        WHERE shop_id = #{shopId}
        <if test="accpetId != null and accpetId != ''">
            AND accept_id = #{accpetId}
        </if>
        <if test="agentId != null and agentId != ''">
            AND agent_id=#{agentId}
        </if>
    </select>
    <select id="updateMerchant">
        UPDATE pay.merchant SET ywy_openid=#{newOpenid}
        WHERE ywy_openid=#{oldOpenid} AND shop_id = #{shopId}
        <if test="accpetId != null and accpetId != ''">
            AND accept_id = #{accpetId}
        </if>
        <if test="agentId != null and agentId != ''">
            AND agent_id=#{agentId}
        </if>
    </select>

    <!-- 查询商户进件次数 -->
    <select id="searchMerChantAddCount" resultType="java.lang.Long">
        select count(*)
        from merchant
        where identity = #{identity}
          and status != '99'
    </select>

    <select id="selectAllId" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.MerchantAllIdRespVO">
        select ac.accept_id, ac.company accept_name, b.agent_id,b.company agent_name,c.partner_id,c.company partner_name, bo.id,bo.company branch_office_name
        from merchant a,
             agent b,
             accept ac,
             partner_channel c,
             branch_office bo
        where a.status != 99
          and a.agent_id = b.agent_id
          and a.accept_id = ac.accept_id
          and b.partner_id = c.partner_id
          and c.branch_office_id = bo.id
          and a.shop_id = #{shopId}
    </select>
    <select id="selectRightByShopId" resultType="java.lang.String">
        select ar.yhc_out_right
        from merchant m
                 left join agent_right ar on m.agent_id = ar.agent_id
        where m.shop_id = #{shopId}
    </select>

    <select id="selectPartnerRightByShopId" resultType="java.lang.String">
        select pc.is_yhc_out
        from merchant m
                 left join partner_channel pc on m.partner_id = pc.partner_id
        where m.shop_id = #{shopId}
    </select>

    <!-- 获取此商户的代理商的api的key值 -->
    <select id="getApiKey" resultType="java.lang.String">
        select a.api_key
        from agent a,
             merchant b
        where b.shop_id = #{shopId}
          and a.agent_id = b.agent_id
    </select>

    <!-- 根据商户号查询商户 -->
    <select id="selectMerchantByShopId"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchant.MerchantDO">
        select shop_id,
               password,
               shop_name,
               shop_nickname,
               shop_address,
               shop_keeper,
               phone,
               email,
               internet,
               platform,
               type,
               province,
               city,
               openid,
               pay_type,
               pay_type_alipay,
               pay_type_unionpay,
               (case when register_time = '0000-00-00 00:00:00' then '' else register_time end) register_time,
               trueid,
               card,
               card_name,
               card_phone,
               bank_name,
               bank_address,
               bank_add_no,
               quota,
               sub_type,
               sub_mid,
               sub_appid,
               sub_appidse,
               sub_openid,
               agent_id,
               alipay_sub_id,
               rate_pay,
               rate,
               rate_alipay,
               rate_unionpay,
               rate_quota, identity,
                wechatid, subsidy_status, subsidy, lng, lat, ywy_openid, merchant_id, sub_merchant_id, logo_url, lc_merchant_no, lc_merchant_level, lc_terminal_id, lc_access_token, lc_contract_status, status, micro_mch_id, sub_classify, area, trade_area, feature, shop_phone, wft_xy_merchant_no, wft_xy_merchant_key, sub_channel_status, wft_xy_merchant_password, description, agent_sub_openid, sub_agent_id, scene, refund_auth, alipay_store_id, reject, agent_rate, is_member, agent_rate_alipay, agent_rate_unionpay, member_type, auth_bank4, xtbank_merchant_no, xtbank_proxy_no, leshua_merchant_no, leshua_merchant_key, zhonghui_merchant_no, refund_password, yrm_merchant_no, sxf_merchant_no, yrm_high_quality, yrm_high_quality_agent_rate, sxf_merchant_mch_id, sxf_merchant_task_code, yrm_org_id, yrm_org_key, sxf_org_id, sxf_org_privite_key, sb_merchant_no, yrm_paidout_channel_sub, accept_id, fft_merchant_no, fft_terminal_id, wsy_client_id, jl_merchant_no, mch_id, cp_mch_id, cp_merchant_no, cp_merchant_auth_key, cp_merchant_device_no, cp_merchant_device_id, cp_merchant_cashier, cp_merchant_order_prefix, keeper_identity, ch_merchant_no, is_update_mer, up_pwd_time, is_allow_wangjin, xmid, is_yz_swap, (case when yz_swap_time='0000-00-00 00:00:00' then NULL else register_time end) yz_swap_time, org_register_time, partner_id, UPDATE_TIME, pay_type_fast, shop_business_name, pay_type_douyin, branch_id, pay_type_dcpay, rate_dcpay, rate_douyin
        from merchant
        where status !=99 and shop_id=#{shopId};
    </select>

    <update id="deleteMerchant">
        update merchant
        set status = 99
        where shop_id = #{shopId}
    </update>

    <update id="updateMerchantStatus">
        update merchant
        set status = 1
        where shop_id = #{shopId} and status = 99
    </update>

    <!-- 根据id修改结算身份证号 -->
    <update id="updateByIdentity">
        update merchant
        set identity = #{merchant.identity}
        where shop_id = #{merchant.shopId}
    </update>

    <!-- 针对商户补资料得修改接口（只会修改部分信息） -->
    <update id="updateMerchantMsg">
        update merchant
        <set>
            <if test="merchantDO.shopNickname != null and merchantDO.shopNickname != ''">
                shop_nickname = #{merchantDO.shopNickname},
            </if>
            <if test="merchantDO.shopAddress != null and merchantDO.shopAddress != ''">
                shop_address = #{merchantDO.shopAddress},
            </if>
            <if test="merchantDO.cardName != null and merchantDO.cardName != ''">
                card_name = #{merchantDO.cardName},
            </if>
            <if test="merchantDO.cardPhone != null and merchantDO.cardPhone != ''">
                card_phone = #{merchantDO.cardPhone},
            </if>
            <if test="merchantDO.card != null and merchantDO.card != ''">
                card = #{merchantDO.card},
            </if>
            <if test="merchantDO.identity != null and merchantDO.identity != ''">
                identity = #{merchantDO.identity},
            </if>
            <if test="merchantDO.bankName != null and merchantDO.bankName != ''">
                bank_name = #{merchantDO.bankName},
            </if>
            <if test="merchantDO.bankAddress != null and merchantDO.bankAddress != ''">
                bank_address = #{merchantDO.bankAddress},
            </if>
            <if test="merchantDO.bankAddNo != null and merchantDO.bankAddNo != ''">
                bank_add_no = #{merchantDO.bankAddNo},
            </if>
            <if test="merchantDO.shopBusinessName != null and merchantDO.shopBusinessName != ''">
                shop_business_name = #{merchantDO.shopBusinessName},
            </if>
            <if test="merchantDO.lng != null and merchantDO.lng != ''">
                lng = #{merchantDO.lng},
            </if>
            <if test="merchantDO.lat != null and merchantDO.lat != ''">
                lat = #{merchantDO.lat},
            </if>
        </set>
        where shop_id = #{merchantDO.shopId}
    </update>
    <!-- 修改商户类型 -->
    <update id="updateMerchantChannel">
        update merchant
        <set>
            <if test="type == 'wx'.toString() or type == 'all'.toString()">
                pay_type = #{merchantChannelDO.channel},
                rate = #{merchantChannelDO.rateWeixin},
            </if>
            <if test="type == 'ali'.toString() or type == 'all'.toString()">
                pay_type_alipay = #{merchantChannelDO.channel},
                rate_alipay = #{merchantChannelDO.rateAlipay},
            </if>
            <if test="type == 'union'.toString() or type == 'all'.toString()">
                pay_type_unionpay = #{merchantChannelDO.channel},
                rate_unionpay = #{merchantChannelDO.rateUnionpay},
            </if>
            <if test="type == 'dcpay'.toString()">
                pay_type_dcpay = #{merchantChannelDO.channel},
                rate_dcpay = #{merchantChannelDO.rateDcpay},
            </if>
            <if test="type == 'dy'.toString()">
                pay_type_douyin = #{merchantChannelDO.channel},
                rate_douyin = #{merchantChannelDO.rateDouyin},
            </if>
        </set>
        where shop_id = #{shopId}
    </update>
    <update id="updateChannel">
        update merchant
        <set>
        <if test="type == 'wx'.toString() or type == 'all'.toString()">
            pay_type = #{merchantChannelDO.channel},
        </if>
        <if test="type == 'ali'.toString() or type == 'all'.toString()">
            pay_type_alipay = #{merchantChannelDO.channel},
        </if>
        <if test="type == 'union'.toString() or type == 'all'.toString()">
            pay_type_unionpay = #{merchantChannelDO.channel},
        </if>
        <if test="type == 'dcpay'.toString()">
            pay_type_dcpay = #{merchantChannelDO.channel},
            rate_dcpay = #{merchantChannelDO.rateDcpay},
        </if>
        <if test="type == 'dy'.toString()">
            pay_type_douyin = #{merchantChannelDO.channel},
            rate_douyin = #{merchantChannelDO.rateDouyin},
        </if>
        </set>
        where shop_id = #{shopId}
    </update>
    <select id="selectPageByAgentSuId" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.LargeMerchantRespVO">
        select shop_id,shop_name,shop_nickname,shop_keeper,phone,shop_address,trueid,register_time,sub_agent_id
        from merchant
        <where>
            sub_agent_id=#{reqVO.agentSubId}
            <if test="reqVO.shopName!=null and reqVO.shopName!=''">
                and shop_name like concat('%',#{reqVO.shopName},'%')
            </if>
            <if test="reqVO.shopId !=null and reqVO.shopId !=''">
                and shop_id = #{reqVO.shopId}
            </if>
            <if test="reqVO.phone!=null and reqVO.phone!=''">
                and phone = #{reqVO.phone}
            </if>
            <if test="reqVO.shopKeeper!=null and reqVO.shopKeeper!=''">
                and shop_keeper = #{reqVO.shopKeeper}
            </if>
        </where>
        order by register_time desc
    </select>
    <select id="selectByAgentSuId" resultType="java.lang.String">
        select shop_id
        from merchant
        <where>
            sub_agent_id=#{agentSubId}
        </where>
    </select>
    <select id="selectListByAgentSuId" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.LargeMerchantRespVO">
        select shop_id,trueid,shop_name,shop_nickname
        from merchant
        <where>
            sub_agent_id=#{agentSubId}
        </where>
    </select>

    <!-- 查询国通商户信息 -->
    <select id="queryGtMerchant"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchant.vo.api.QueryGtMerchantRespVO"
            parameterType="java.lang.String">
        SELECT
               m.trueid shop_id,
               mc.merchant_no trueid,
               m.shop_nickname shop_name,
               m.pay_type channel,
               mc.ali_authorize_state ali_authorize_state,
               mc.authorize_state wx_authorize_state,
               mc.merchant_other_3 gt_wx_merchant_no,
               mc.merchant_other_4 gt_ali_merchant_no,
               mc.merchant_no gt_merchant_no,
               sdd2.label wx_authorize_reject,
               sdd1.label ali_authorize_reject
        FROM merchant m
                 LEFT JOIN merchant_channel mc ON m.shop_id = mc.shop_id
                 LEFT JOIN system_dict_data sdd1 ON  sdd1.value = mc.ali_authorize_state and sdd1.dict_type = 'authorize_state'
                 LEFT JOIN system_dict_data sdd2 ON  sdd2.value = mc.authorize_state and sdd2.dict_type = 'authorize_state'
          <where>
              mc.status = '1'
              AND mc.channel = 'guotong'
              <if test="shopId != null and shopId !=''">
                  AND m.shop_id = #{shopId}
              </if>
              <if test="gtMerchantNo != null and gtMerchantNo !=''">
                  AND mc.merchant_no = #{gtMerchantNo}
              </if>
          </where>

    </select>
</mapper>