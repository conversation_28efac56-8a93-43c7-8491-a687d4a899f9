<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantcashier.MerchantCashierMapper">
    <!-- 导如user失败得用户 -->
    <insert id="insertUserError">
        INSERT INTO user_error(xmid, username, phone, error)
        VALUES (#{userErrorDO.xmid}, #{userErrorDO.username}, #{userErrorDO.phone}, #{userErrorDO.error});
    </insert>

    <!-- 修改认证状态 -->
    <update id="updateCertified">
        UPDATE merchant_cashier set is_or_not_certified = 1 WHERE id = #{id}
    </update>

    <update id="updateByXmid">
        update merchant_cashier set is_boss = 4 where shop_id = #{shopId} and xmid = #{xmid}
    </update>

    <select id="selectShopTeamPage" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo.ShopTeamRespVO">
        select d.headimgurl,d.name,u.username as phone,b.is_boss,c.is_opend,b.is_or_not_certified,b.insert_time,b.id,u.user_id_old as xmid
        from merchant_cashier b,system_users u left join mer_order_subscribe_msg c on u.user_id_old=c.xmid left join user d on u.user_id_old=d.xmid
        where u.user_id_old=b.xmid
        <if test="name != null and name != ''">
            and u.nickname like concat('%',#{name},'%')
        </if>
        <if test="phone != null and phone != ''">
            and u.username = #{phone}
        </if>
        and b.shop_id= #{shopId}
        group by u.user_id_old order by b.insert_time desc
    </select>

    <!-- 查询商户下收银员xmid -->
    <select id="getXmid" resultType="java.lang.String">
        select xmid from merchant_cashier WHERE is_boss = 3 and shop_id = #{shopId}
    </select>

    <!-- 使用xmId查询商户团队成员表 -->
    <select id="getIsBossList" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO">
        select distinct shop_id,is_boss,xmid,openid,pw,is_or_not_certified,insert_time
        from merchant_cashier where xmid = #{xmid}
    </select>

    <!-- 查询userError表 -->
    <select id="selectUserError" resultType="com.cmpay.code.cmpaymodulepc.service.merchantcashier.UserErrorDO">
        select id,xmid,username,phone,error from user_error where xmid = #{xmid}
    </select>

    <select id="selectMerchantCashierAndMerchantExtend"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo.CashierAndExtendRespVO">
        select me.shop_id,me.is_profit_share,mc.xmid from merchant_extend me left join merchant_cashier mc
            on me.shop_id = mc.shop_id
                where me.is_profit_share = 1
                <if test="xmid != null and xmid != ''">
                    and mc.xmid = #{xmid}
                </if>
                <if test="shopId != null and shopId != ''">
                    and me.shop_id = #{shopId}
                </if>

    </select>

    <!-- 根据商户号+isBoos查询商户 -->
    <select id="getMerchantCashier"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO">
        select id,shop_id,openid,pw,is_or_not_certified,insert_time,is_boss,xmid
        from merchant_cashier
        where shop_id = #{shopId} and is_boss = 4
    </select>

    <!-- 根据xmid和shopId查询商户成员 -->
    <select id="xmidOrShopIdByIsBoss"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO">
        select id,shop_id,openid,pw,is_or_not_certified,insert_time,is_boss,xmid
        from merchant_cashier where shop_id = #{shopId} and xmid = #{xmid}
    </select>

    <select id="selectListByXmid"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO">
        SELECT mc.*
        FROM merchant_cashier mc
        LEFT JOIN merchant m ON m.shop_id = mc.shop_id
        WHERE mc.xmid = #{xmid} and m.status != 99
    </select>

    <select id="selectByXmid"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO">
        select mc.* from merchant_cashier mc
        LEFT JOIN merchant m ON mc.shop_id = m.shop_id
        where mc.xmid = #{xmid} and mc.is_or_not_certified = 1 and m.status != 99 order by field(mc.is_boss , 4,1,2,3,0) limit 1
    </select>

    <!-- 根据商户号和用户角色查询团队信息 -->
    <select id="findByShopIdAndIsBoss"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantcashier.MerchantCashierDO">
        select * from merchant_cashier where shop_id = #{shopId} and is_boss = #{isBoss}
    </select>

    <select id="findXimdByIsBoss" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo.MerchantCashierXimdVo">
        SELECT DISTINCT xmid,is_boss FROM merchant_cashier
        WHERE shop_id = #{shopId}
        <if test="isBoss != null and isBoss.size() > 0">
            AND is_boss IN
            <foreach item="item" index="index" collection="isBoss" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findByXmid"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantcashier.vo.MerchantCashierHandlerVO">
        select c.shop_id,m.trueid,m.shop_name,c.is_boss,m.`status` from merchant_cashier c left join merchant m on  c.shop_id = m.shop_id  where c.xmid=#{xmid} and c.is_or_not_certified = 1;
        </select>
</mapper>