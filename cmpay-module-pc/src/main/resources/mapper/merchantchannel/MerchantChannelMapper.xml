<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantchannel.MerchantChannelMapper">

    <select id="selectMerchantChannelByShopIdAndChannel" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select * from merchant_channel
                <where>
                    <if test="isWx != null and isWx != ''">
                        is_weixin = #{isWx}
                    </if>
                    <if test="isAli != null and isAli != ''">
                        is_alipay = #{isAli}
                    </if>
                    <if test="isUnion != null and isUnion != ''">
                        is_unionpay = #{isUnion}
                    </if>
                    <if test="isDcpay != null and isDcpay != ''">
                        is_dcpay = #{isDcpay}
                    </if>
                    <if test="shopId != null and shopId != ''">
                      and shop_id = #{shopId}
                    </if>
                        and channel = #{channel}
                </where>
    </select>


    <select id="selectMerchantChannel"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select id,shop_id,channel,status,status_str,reject_msg,org_no,merchant_no,sign_key,merchant_other_1,merchant_other_2,merchant_other_3,merchant_other_4,merchant_other_5,merchant_other_6,merchant_other_7,merchant_other_8,merchant_other_9,merchant_other_10,rate_weixin,rate_alipay,rate_unionpay,card_no,insert_time,
               is_weixin,is_alipay,is_unionpay,appid,appidse,reject_time,(case when pass_time='0000-00-00 00:00:00' then '' else pass_time end) as pass_time,account_order_id,account_status,authorize_reject,channel_mch_id,authorize_state,subscribe_appid,channel_ali_mch_id,merchant_other_11,paidout_type,ali_level,ladder_type,end_amt,subsides,
               (case when polity_start_time='0000-00-00 00:00:00' then '' else polity_start_time end) as polity_start_time,(case when polity_end_time='0000-00-00 00:00:00' then '' else polity_end_time end) as polity_end_time,wx_threshold_rate,ali_threshold_rate,union_threshold_rate,is_wx_activity,is_ali_activity,wx_activity_time,
               ali_activity_time,merchant_other_12,amt_status,merchant_other_13,merchant_other_14,amt_error_msg,amt_time,is_dcpay,rate_dcpay,ali_authorize_state,merchant_other_15,merchant_other_16,merchant_other_17,merchant_other_18,merchant_other_19,merchant_other_20,merchant_other_21,merchant_other_22,is_send_auth_notice
        from merchant_channel
        where status = '1' and shop_id = #{shopId} and channel = #{channel}
    </select>

    <select id="selectChangeMerchantChannel"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select id,shop_id,channel,status,status_str,reject_msg,org_no,merchant_no,sign_key,merchant_other_1,merchant_other_2,merchant_other_3,merchant_other_4,merchant_other_5,merchant_other_6,merchant_other_7,merchant_other_8,merchant_other_9,merchant_other_10,rate_weixin,rate_alipay,rate_unionpay,card_no,insert_time,
               is_weixin,is_alipay,is_unionpay,appid,appidse,reject_time,(case when pass_time='0000-00-00 00:00:00' then '' else pass_time end) as pass_time,account_order_id,account_status,authorize_reject,channel_mch_id,authorize_state,subscribe_appid,channel_ali_mch_id,merchant_other_11,paidout_type,ali_level,ladder_type,end_amt,subsides,
               (case when polity_start_time='0000-00-00 00:00:00' then '' else polity_start_time end) as polity_start_time,(case when polity_end_time='0000-00-00 00:00:00' then '' else polity_end_time end) as polity_end_time,wx_threshold_rate,ali_threshold_rate,union_threshold_rate,is_wx_activity,is_ali_activity,wx_activity_time,
               ali_activity_time,merchant_other_12,amt_status,merchant_other_13,merchant_other_14,amt_error_msg,amt_time,is_dcpay,rate_dcpay,ali_authorize_state,merchant_other_15,merchant_other_16,merchant_other_17,merchant_other_18,merchant_other_19,merchant_other_20,merchant_other_21,merchant_other_22,is_send_auth_notice,`status`
        from merchant_channel
        where shop_id = #{shopId} and channel = #{channel}
    </select>


    <select id="selectMerchantOldChannel"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select id,shop_id,channel,status,status_str,reject_msg,org_no,merchant_no,sign_key,merchant_other_1,merchant_other_2,merchant_other_3,merchant_other_4,merchant_other_5,merchant_other_6,merchant_other_7,merchant_other_8,merchant_other_9,merchant_other_10,rate_weixin,rate_alipay,rate_unionpay,card_no,insert_time,
               is_weixin,is_alipay,is_unionpay,appid,appidse,reject_time,(case when pass_time='0000-00-00 00:00:00' then '' else pass_time end) as pass_time,account_order_id,account_status,authorize_reject,channel_mch_id,authorize_state,subscribe_appid,channel_ali_mch_id,merchant_other_11,paidout_type,ali_level,ladder_type,end_amt,subsides,
               (case when polity_start_time='0000-00-00 00:00:00' then '' else polity_start_time end) as polity_start_time,(case when polity_end_time='0000-00-00 00:00:00' then '' else polity_end_time end) as polity_end_time,wx_threshold_rate,ali_threshold_rate,union_threshold_rate,is_wx_activity,is_ali_activity,wx_activity_time,
               ali_activity_time,merchant_other_12,amt_status,merchant_other_13,merchant_other_14,amt_error_msg,amt_time,is_dcpay,rate_dcpay,ali_authorize_state,merchant_other_15,merchant_other_16,merchant_other_17,merchant_other_18,merchant_other_19,merchant_other_20,merchant_other_21,merchant_other_22,is_send_auth_notice,rate_fast
        from merchant_channel
        where shop_id = #{shopId} and channel = #{channel}
    </select>
    <!-- 根据商户和通道查询通道信息 -->
    <select id="findMerchantByChannel"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select id,shop_id,channel,status,status_str,reject_msg,org_no,merchant_no,sign_key,merchant_other_1,merchant_other_2,merchant_other_3,merchant_other_4,merchant_other_5,merchant_other_6,merchant_other_7,merchant_other_8,merchant_other_9,merchant_other_10,rate_weixin,rate_alipay,rate_unionpay,card_no,insert_time,
               is_weixin,is_alipay,is_unionpay,appid,appidse,reject_time,(case when pass_time='0000-00-00 00:00:00' then '' else pass_time end) as pass_time,account_order_id,account_status,authorize_reject,channel_mch_id,authorize_state,subscribe_appid,channel_ali_mch_id,merchant_other_11,paidout_type,ali_level,ladder_type,end_amt,subsides,
               (case when polity_start_time='0000-00-00 00:00:00' then '' else polity_start_time end) as polity_start_time,(case when polity_end_time='0000-00-00 00:00:00' then '' else polity_end_time end) as polity_end_time,wx_threshold_rate,ali_threshold_rate,union_threshold_rate,is_wx_activity,is_ali_activity,wx_activity_time,
               ali_activity_time,merchant_other_12,amt_status,merchant_other_13,merchant_other_14,amt_error_msg,amt_time,is_dcpay,rate_dcpay,ali_authorize_state,merchant_other_15,merchant_other_16,merchant_other_17,merchant_other_18,merchant_other_19,merchant_other_20,merchant_other_21,merchant_other_22,is_send_auth_notice,rate_fast
        from merchant_channel
        where shop_id = #{shopId} and channel = #{channel}
    </select>

    <select id="selectByShopId"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select *
        from merchant_channel
        where shop_id = #{shopId}
    </select>

    <select id="selectByShopIdAndStatus"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO">
        select id,shop_id,channel,status,status_str,reject_msg,org_no,merchant_no,sign_key,merchant_other_1,merchant_other_2,merchant_other_3,merchant_other_4,merchant_other_5,merchant_other_6,merchant_other_7,merchant_other_8,merchant_other_9,merchant_other_10,rate_weixin,rate_alipay,rate_unionpay,card_no,insert_time,
               is_weixin,is_alipay,is_unionpay,appid,appidse,reject_time,(case when pass_time='0000-00-00 00:00:00' then '' else pass_time end) as pass_time,account_order_id,account_status,authorize_reject,channel_mch_id,authorize_state,subscribe_appid,channel_ali_mch_id,merchant_other_11,paidout_type,ali_level,ladder_type,end_amt,subsides,
               (case when polity_start_time='0000-00-00 00:00:00' then '' else polity_start_time end) as polity_start_time,(case when polity_end_time='0000-00-00 00:00:00' then '' else polity_end_time end) as polity_end_time,wx_threshold_rate,ali_threshold_rate,union_threshold_rate,is_wx_activity,is_ali_activity,wx_activity_time,
               ali_activity_time,merchant_other_12,amt_status,merchant_other_13,merchant_other_14,amt_error_msg,amt_time,is_dcpay,rate_dcpay,ali_authorize_state,merchant_other_15,merchant_other_16,merchant_other_17,merchant_other_18,merchant_other_19,merchant_other_20,merchant_other_21,merchant_other_22,is_send_auth_notice
        from merchant_channel
        where shop_id = #{shopId} and status = '1'
    </select>

    <update id="updateMerchantActivityType">
        update merchant_channel
        <set>
            <if test="payType!=null and payType=='0'.toString">
                is_wx_activity=#{isActivity}
            </if>
            <if test="payType!=null and payType=='1'.toString">
                is_ali_activity=#{isActivity}
            </if>
        </set>
        <where>
            shop_id=#{shopId} and channel=#{channel}
        </where>
    </update>

    <!-- 更新商户费率：把随行付费率改成国通费率 -->
    <update id="updateSxfTqRateToGtRate">
        UPDATE merchant_channel b,(
            SELECT
            a.shop_id,
            a.rate_weixin,
            a.rate_alipay,
            a.rate_unionpay,
            a.end_amt,
            a.wx_threshold_rate,
            a.ali_threshold_rate
            FROM
            (
            SELECT
            shop_id,
            rate_weixin,
            rate_alipay,
            rate_unionpay,
            end_amt,
            wx_threshold_rate,
            ali_threshold_rate
            FROM
            merchant_channel
            WHERE
            channel = 'sxf_tq'
            AND shop_id = #{shopId}) AS a
            ) c
        SET b.rate_weixin = c.rate_weixin, b.rate_alipay = c.rate_alipay, b.rate_unionpay = c.rate_unionpay, b.end_amt = c.end_amt, b.wx_threshold_rate = c.wx_threshold_rate, b.ali_threshold_rate = c.ali_threshold_rate
        WHERE
            b.shop_id = c.shop_id
          AND b.channel = 'guotong'
    </update>
    <update id="removeMerchantChannel">
        update merchant
        <set>
            <if test="payType == 'wx'.toString()">
                pay_type = '',
                rate = null,
            </if>
            <if test="payType == 'ali'.toString()">
                pay_type_alipay = '',
                rate_alipay = null,
            </if>
            <if test="payType == 'union'.toString()">
                pay_type_unionpay = '',
                rate_unionpay = null,
            </if>
            <if test="payType == 'dcpay'.toString()">
                pay_type_dcpay = '',
                rate_dcpay = null,
            </if>
        </set>
        where shop_id = #{shopId}
    </update>


</mapper>