<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantdutyorders.MerchantDutyOrdersMapper">

    <!-- 查询班结信息 -->
<!--    默认展示七天-->
<!--    and DATE_SUB(CURDATE(),INTERVAL 7 DAY) <![CDATA[  <=  ]]> DATE(end_time)-->
    <select id="searchDutyMessage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo.SearchDutyMessageRespVo">
        SELECT duty_id,short_key,device,total_num,total_money,total_refund_num,total_refund_money,start_time,end_time,is_boss,xmid
        FROM merchant_duty_orders
        <where>
            and shop_id = #{dutyMessage.shopId}
            <if test="dutyMessage.isBoss != null and dutyMessage.isBoss ==3">
                and is_boss = 3 AND xmid = #{dutyMessage.xmid}
            </if>
            <if test="dutyMessage.isBoss != null and dutyMessage.isBoss ==1">
                 AND ((is_boss IN (2,3)) OR (xmid = #{dutyMessage.xmid} AND is_boss = 1))
            </if>
            <if test="dutyMessage.start != null and dutyMessage.start != ''">
                and end_time <![CDATA[  >=  ]]> #{dutyMessage.start}
            </if>
            <if test="dutyMessage.end != null and dutyMessage.end != ''">
                and end_time <![CDATA[  <=  ]]> #{dutyMessage.end}
            </if>
            <if test="devicesList != null and devicesList.size > 0">
                and device in
                <foreach collection="devicesList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="dutyMessage.device != null and dutyMessage.device != '' and dutyMessage.device != 'all'">
                and store_id = #{dutyMessage.device}
            </if>
            <if test="shortUrlList != null and shortUrlList.size > 0">
                and short_key in
                <foreach collection="shortUrlList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="dutyMessage.shortKey != null and dutyMessage.shortKey != '' and dutyMessage.shortKey != 'all'">
                and short_key = #{dutyMessage.shortKey}
            </if>
            <if test="dutyMessage.dutyType != null and dutyMessage.dutyType !='' and dutyMessage.dutyType != 4">
                and is_boss = #{dutyMessage.dutyType}
            </if>
            <if test="dutyMessage.dutyType != null and dutyMessage.dutyType !='' and dutyMessage.dutyType == 4">
                and is_boss in (1,4)
            </if>
        </where>
        order by end_time desc
    </select>



    <select id="searchDutyMessage2"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo.SearchDutyMessageRespVo">
        SELECT duty_id, short_key, device, total_num, total_money, total_refund_num, total_refund_money, start_time, end_time, is_boss, xmid
        FROM merchant_duty_orders
        <where>
            shop_id = #{dutyMessage.shopId}
            <if test="dutyMessage.start != null and dutyMessage.start != ''">
                and end_time <![CDATA[  >=  ]]> #{dutyMessage.start}
            </if>
            <if test="dutyMessage.end != null and dutyMessage.end != ''">
                and end_time <![CDATA[  <=  ]]> #{dutyMessage.end}
            </if>
            <if test="devicesList != null and devicesList.size() > 0 and (shortUrlList == null or shortUrlList.size() == 0)">
                and store_id in
                <foreach collection="devicesList" item="item" index="index" open="(" separator="," close=")">
                    #{item.deviceId}
                </foreach>
                and is_boss = 2 AND xmid = #{dutyMessage.xmid}
            </if>
            <if test="devicesList != null and devicesList.size() > 0 and shortUrlList != null and shortUrlList.size() > 0">
                and ((short_key in
                <foreach collection="shortUrlList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
                and is_boss = 3) or
                (store_id in
                <foreach collection="devicesList" item="item" index="index" open="(" separator="," close=")">
                    #{item.deviceId}
                </foreach>
                and is_boss = 2 AND xmid = #{dutyMessage.xmid}))
            </if>
            <if test="dutyMessage.device != null and dutyMessage.device != '' and dutyMessage.device != 'all'">
                and store_id = #{dutyMessage.device}
            </if>
            <if test="dutyMessage.shortKey != null and dutyMessage.shortKey != '' and dutyMessage.shortKey != 'all'">
                and short_key = #{dutyMessage.shortKey}
            </if>
            <if test=" dutyMessage.dutyType !=null and dutyMessage.dutyType !='' ">
                and is_boss = #{dutyMessage.dutyType}
            </if>
        </where>
        order by end_time desc
    </select>


    <!-- 查询班结信息详情 -->
    <select id="searchDutyDetails"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo.SearchDutyDetailsRespVo">
        select wx_num,wx_money,wx_refund_num,wx_refund_money,ali_num,ali_money,ali_refund_num,ali_refund_money,uni_num,uni_money,uni_refund_num,uni_refund_money,
               mem_num,mem_money,other_num,other_money,total_num,total_money,total_refund_num,total_refund_money,dcpay_money,dcpay_refund_num,dcpay_refund_money,dcpay_num,bank_num,bank_money,bank_refund_num,bank_refund_money
        from merchant_duty_orders
        where shop_id = #{dutyDetails.shopId} and duty_id = #{dutyDetails.dutyId}
    </select>

    <!-- 负责人/超管查询上次交班时间 -->
    <select id="getStartTime" resultType="java.lang.String">
        select end_time from merchant_duty_orders
        where xmid = #{xmid} and shop_id = #{shopId}
        order by end_time desc limit 1
    </select>

    <!-- 店长查询上次交班时间 -->
    <select id="getDeviceStartTime" resultType="java.lang.String">
        select end_time from merchant_duty_orders
        where device = #{device}
        and shop_id = #{shopId}
        and (short_key is null or short_key = '')
        order by end_time desc limit 1
    </select>

    <!-- 收银员查询上次交班时间 -->
    <select id="getShortKeyStartTime" resultType="java.lang.String">
        select end_time from merchant_duty_orders
        where short_key = #{shortKey} and shop_id = #{shopId}
        order by end_time desc limit 1
    </select>
</mapper>
