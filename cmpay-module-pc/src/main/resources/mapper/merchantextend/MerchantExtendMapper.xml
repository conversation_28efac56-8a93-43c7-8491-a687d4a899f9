<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantextend.MerchantExtendMapper">

    <!-- 修改商户星移计划状态 -->
    <update id="updateMerchantWxAlipayStatus">
        update merchant_extend set is_open_wx_alipay = #{status} where shop_id = #{shopId}
    </update>
    <!-- 修改商户扩展信息 -->
    <update id="updateMerchantExtendMsg">
        update merchant_extend
        <set>
            <if test="merchantExtendDO.artifName != null and merchantExtendDO.artifName != ''">
                artif_name = #{merchantExtendDO.artifName},
            </if>
            <if test="merchantExtendDO.artifPhone != null and merchantExtendDO.artifPhone != ''">
                artif_phone = #{merchantExtendDO.artifPhone},
            </if>
            <if test="merchantExtendDO.artifIdentity != null and merchantExtendDO.artifIdentity != ''">
                artif_identity = #{merchantExtendDO.artifIdentity},
            </if>
            <if test="merchantExtendDO.artifIdentityStartTime != null and merchantExtendDO.artifIdentityStartTime != ''">
                artif_identity_start_time = #{merchantExtendDO.artifIdentityStartTime},
            </if>
            <if test="merchantExtendDO.artifIdentityEndTime != null and merchantExtendDO.artifIdentityEndTime != ''">
                artif_identity_end_time = #{merchantExtendDO.artifIdentityEndTime},
            </if>
            <if test="merchantExtendDO.identityStarttime != null and merchantExtendDO.identityStarttime != ''">
                identity_starttime = #{merchantExtendDO.identityStarttime},
            </if>
            <if test="merchantExtendDO.identifyEndtime != null and merchantExtendDO.identifyEndtime != ''">
                identify_endtime = #{merchantExtendDO.identifyEndtime},
            </if>
            <if test="merchantExtendDO.licenceNo != null and merchantExtendDO.licenceNo != ''">
                licence_no = #{merchantExtendDO.licenceNo},
            </if>
            <if test="merchantExtendDO.licenceBeginDate != null and merchantExtendDO.licenceBeginDate != ''">
                licence_begin_date = #{merchantExtendDO.licenceBeginDate},
            </if>
            <if test="merchantExtendDO.licenceExpireDate != null and merchantExtendDO.licenceExpireDate != ''">
                licence_expire_date = #{merchantExtendDO.licenceExpireDate},
            </if>
        </set>
        where shop_id = #{merchantExtendDO.shopId}
    </update>


    <select id="selectByShopId" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantextend.MerchantExtendDO">
        SELECT *
        from merchant_extend
        where shop_id = #{shopId}
</select>

    <!-- 分账管理——分账商户管理——分页查询 -->
    <select id="searchProfitShareOpenStatus"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend.vo.ProfitShareOpenStatusRespVo">
        select m.trueid,m.shop_nickname,me.is_profit_share status,m.shop_id
        from pay.merchant_extend me
        join pay.merchant m
        on me.shop_id = m.shop_id
        <where>
            and me.is_profit_share = '1'
            <if test="profitShareOpenStatusReqVo.shopId != null and profitShareOpenStatusReqVo.shopId != ''">
                and m.shop_id = #{profitShareOpenStatusReqVo.shopId}
            </if>
            <if test="profitShareOpenStatusReqVo.shopName != null and profitShareOpenStatusReqVo.shopName != ''">
                and shop_nickname like concat('%',#{profitShareOpenStatusReqVo.shopName},'%')
            </if>
        </where>
        order by insert_time desc
    </select>

    <!-- 根据商户号查询商户扩展表 -->
    <select id="findByShopId"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantextend.MerchantExtendDO">
        SELECT shop_id,business_type,licence_no,licence_type,licence_begin_date,licence_expire_date,artif_name,artif_phone,artif_identity,paidout_type,
               paidout_days,is_self_bank,merchant_area_no,insert_time,hrt_area_code,identity_starttime,identify_endtime,is_profit_share,licence_industry,
               licence_business_scope,licence_address,licence_type_str,licence_check_status,mcc_code,balance_type,is_lvzhou,is_lanhai,alipay_login_id,is_need_cp,
               is_party_cp_50,owner_remarks,pipe_name,pipe_phone,pipe_idcard,pipe_email,rate_activity_id,is_can_use_pool,merchant_type,pool_rate_other,
               artif_identity_start_time,artif_identity_end_time,mer_ticket_url,voucher_type,is_mini_app_show_activity,yz_org_id,yz_company,is_qs,id_card_type,
               card_type,policy_id,ryx_mcc_code,pref_rate,stand_rate,stand_end_amt,pref_end_amt,contact_period_type,contact_period_begin,contact_period_end,
               contract_id_doc_type,artif_identity_address,id_doc_type,account_type,param_bak1,param_bak2,is_short_key_profit_share,is_send_register_notice,pool_type
        from merchant_extend
        where shop_id = #{shopId}
    </select>

    <!-- 导出使用支付宝大额政策 -->
    <select id="exportAliBigQuotaMerchantExcel"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantextend.vo.AliBigQuotaMerchantExcel">
        SELECT  pp.title,m.shop_id ,m.trueid ,m.shop_name ,mc.ali_authorize_state ,mc.merchant_other_4 ,mc.channel
        FROM merchant_extend me
                 LEFT JOIN partner_policy pp ON me.policy_id = pp.id
                 LEFT JOIN merchant m ON me.shop_id = m.shop_id
                 LEFT JOIN merchant_channel mc ON me.shop_id = mc.shop_id and m.pay_type = mc.channel
        WHERE  policy_id = '474'
    </select>

</mapper>
