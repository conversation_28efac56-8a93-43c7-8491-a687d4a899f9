<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantimage.MerchantImagesMapper">


    <!--    修改商户图片-->
    <update id="changeMerchantImages">
        UPDATE pay.merchant_images
        <set>
            <!-- 门头照-->
            <if test="fileType != null and fileType == 'merchantHead'.toString() ">
                merchant_head = #{imgUrl}
            </if>
            <!--  收银台-->
            <if test="fileType != null and fileType == 'merchantCheck'.toString() ">
                merchant_check = #{imgUrl}
            </if>
            <!--  内景照-->
            <if test="fileType != null and fileType == 'merchantBussiness'.toString() ">
                merchant_bussiness = #{imgUrl}
            </if>
            <!--  法人身份证头像面-->
            <if test="fileType != null and fileType == 'identityFace'.toString() ">
                identity_face = #{imgUrl}
            </if>
            <!--  法人身份证国徽面-->
            <if test="fileType != null and fileType == 'identityBack'.toString() ">
                identity_back = #{imgUrl}
            </if>
            <!--  开户许可证-->
            <if test="fileType != null and fileType == 'bussinessCard'.toString() ">
                bussiness_card = #{imgUrl}
            </if>
            <!--  营业执照-->
            <if test="fileType != null and fileType == 'bussiness'.toString() ">
                bussiness = #{imgUrl}
            </if>
            <!--  结算人头像面-->
            <if test="fileType != null and fileType == 'identityAccountFace'.toString() ">
                identity_account_face = #{imgUrl}
            </if>
            <!--  结算人国徽面-->
            <if test="fileType != null and fileType == 'identifyAccountBack'.toString() ">
                identify_account_back = #{imgUrl}
            </if>
            <!--  手持身份证-->
            <if test="fileType != null and fileType == 'identifyBody'.toString() ">
                identify_body = #{imgUrl}
            </if>
            <!--  结算卡-->
            <if test="fileType != null and fileType == 'cardFace'.toString() ">
                card_face = #{imgUrl}
            </if>
            <!--  授权函-->
            <if test="fileType != null and fileType == 'authAccount'.toString() ">
                auth_account = #{imgUrl}
            </if>
            <!--  租房协议-->
            <if test="fileType != null and fileType == 'lease'.toString() ">
                lease = #{imgUrl}
            </if>
            <!--  特殊资料-->
            <if test="fileType != null and fileType == 'otherPhoto'.toString() ">
                other_photo = #{imgUrl}
            </if>
            <!--  第三方平台截图-->
            <if test="fileType != null and fileType == 'thrPhoto'.toString() ">
                thr_photo = #{imgUrl}
            </if>
            <!--  其他资料2-->
            <if test="fileType != null and fileType == 'otherPhoto2'.toString() ">
                other_photo2 = #{imgUrl}
            </if>
            <!--  其他资料3-->
            <if test="fileType != null and fileType == 'otherPhoto3'.toString() ">
                other_photo3 = #{imgUrl}
            </if>
            <!--  其他资料4 -->
            <if test="fileType != null and fileType == 'otherPhoto4'.toString() ">
                other_photo4 = #{imgUrl}
            </if>
            <!--  变更函 -->
            <if test="fileType != null and fileType == 'contractXy'.toString() ">
                contract_xy = #{imgUrl}
            </if>
            <!--  承诺函 -->
            <if test="fileType != null and fileType == 'bussinessToPrivateAuthXy'.toString() ">
                bussiness_to_private_auth_xy = #{imgUrl}
            </if>
        </set>
        WHERE shop_id = #{shopId}
    </update>

    <!-- 修改商户图片信息 -->
    <update id="updateMerchantImagesMsg">
        UPDATE pay.merchant_images
        <set>
            <if test="merchantImagesDO.identityFace != null and merchantImagesDO.identityFace != ''">
                identity_face = #{merchantImagesDO.identityFace},
            </if>
            <if test="merchantImagesDO.identityBack != null and merchantImagesDO.identityBack != ''">
                identity_back = #{merchantImagesDO.identityBack},
            </if>
            <if test="merchantImagesDO.identityAccountFace != null and merchantImagesDO.identityAccountFace != ''">
                identity_account_face = #{merchantImagesDO.identityAccountFace},
            </if>
            <if test="merchantImagesDO.identifyAccountBack != null and merchantImagesDO.identifyAccountBack != ''">
                identify_account_back = #{merchantImagesDO.identifyAccountBack},
            </if>
            <if test="merchantImagesDO.bussiness != null and merchantImagesDO.bussiness != ''">
                bussiness = #{merchantImagesDO.bussiness},
            </if>
            <if test="merchantImagesDO.merchantHead != null and merchantImagesDO.merchantHead != ''">
                merchant_head = #{merchantImagesDO.merchantHead},
            </if>
            <if test="merchantImagesDO.merchantCheck != null and merchantImagesDO.merchantCheck != ''">
                merchant_check = #{merchantImagesDO.merchantCheck},
            </if>
            <if test="merchantImagesDO.merchantBussiness != null and merchantImagesDO.merchantBussiness != ''">
                merchant_bussiness = #{merchantImagesDO.merchantBussiness},
            </if>
            <if test="merchantImagesDO.cardFace != null and merchantImagesDO.cardFace != ''">
                card_face = #{merchantImagesDO.cardFace},
            </if>
            <if test="merchantImagesDO.authAccount != null and merchantImagesDO.authAccount != ''">
                auth_account = #{merchantImagesDO.authAccount},
            </if>
            <if test="merchantImagesDO.otherPhoto != null and merchantImagesDO.otherPhoto != ''">
                other_photo = #{merchantImagesDO.otherPhoto},
            </if>
            <if test="merchantImagesDO.otherPhoto4 != null and merchantImagesDO.otherPhoto4 != ''">
                other_photo4 = #{merchantImagesDO.otherPhoto4},
            </if>
        </set>
        WHERE shop_id = #{merchantImagesDO.shopId}
    </update>
</mapper>
