<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantmsgupdaterecord.MerchantMsgUpdateRecordMapper">

    <select id="selectMerchantUpdateCardRecordTime" resultType="java.lang.String">
        select (case when effect_time = '0000-00-00 00:00:00' then '' else effect_time end)
					from merchant_msg_update_record
                    where shop_id = #{shopId}
                    and update_type = 'card'
                    and current_card = #{card}
					and update_status = '0'
                    order by update_time desc limit 1
    </select>
    
    <select id="selectMerchantUpdateCardRecord" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantmsgupdaterecord.MerchantMsgUpdateRecordDO">
        select update_status,update_error_msg,update_time,card_image,order_id,current_card
					,card_name,bank_add_no,bank_name,branch_name,identity,card_phone,province,city,card_type,id_card_type
					,(case when effect_time='0000-00-00 00:00:00' then '' else effect_time end) as effect_time_again
					 from merchant_msg_update_record
                     where shop_id=#{shopId} and update_type='card' and channel=#{channel} and is_merchant='1' and update_status!='0'
					 order by update_time desc limit 1
    </select>

    <!-- 修改结算卡——搜索结算银行 -->
    <select id="searchSettlementBank" resultType="java.lang.String">
        select distinct bank_name from exported_bank_msg
        <where>
            <if test="settlementBankReqVo.bankName != null and settlementBankReqVo.bankName != ''">
                and bank_name like concat('%',#{settlementBankReqVo.bankName},'%')
            </if>
        </where>
        order by bank_name limit 100
    </select>

    <!-- 修改结算卡——搜索开户银行 -->
    <select id="searchSuggestBank"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantmsgupdaterecord.vo.SuggestBankMessageRqspVo">
        select bank_name,branch_name,joint_line
        from exported_bank_msg
        <where>
            <if test="suggestBankMessageReqVo.settlementBankName != null and suggestBankMessageReqVo.settlementBankName != ''">
                and bank_name = #{suggestBankMessageReqVo.settlementBankName}
            </if>
            <if test="suggestBankMessageReqVo.suggestBankName != null and suggestBankMessageReqVo.suggestBankName != ''">
                and branch_name like concat('%',#{suggestBankMessageReqVo.suggestBankName},'%')
            </if>
        </where>
        limit 100
    </select>

    <!-- 查询各渠道修改费率状态 -->
    <select id="searchMerchantChannelRateStatus"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantmsgupdaterecord.MerchantMsgUpdateRecordDO">
        select update_status,update_error_msg,update_time,card_image,order_id
        from merchant_msg_update_record
        where shop_id = #{shopId}
            and update_type = 'rate'
            and channel = #{channel}
        order by update_time desc limit 1
    </select>

    <!-- 根据商户号、通道、类型查询修改记录 -->
    <select id="findByShopIdAndChannelAndType"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantmsgupdaterecord.MerchantMsgUpdateRecordDO">
        select * from merchant_msg_update_record
        where shop_id = #{shopId}
        and channel = #{channel}
        and update_type = #{type}
        and update_status = '1'
        order by update_time desc limit 1
    </select>
    <!-- 查询商户的最后更新时间 -->
    <select id="getShopIdEffectTime" resultType="java.lang.String">
        SELECT (case when effect_time='0000-00-00 00:00:00' then null else effect_time end)
        FROM merchant_msg_update_record
        WHERE shop_id = #{shopId}
          and channel = #{channel}
        order by update_time desc LIMIT 1
    </select>
    <!-- 根据商户号、通道、类型查询修改记录 -->
    <select id="findRecordByShopIdAndChannelAndType"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantmsgupdaterecord.MerchantMsgUpdateRecordDO">
        select * from merchant_msg_update_record
        where shop_id = #{shopId}
          and channel = #{channel}
          and update_type = #{type}
        order by update_time desc limit 1
    </select>
</mapper>
