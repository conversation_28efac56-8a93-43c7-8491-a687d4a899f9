<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.merchantoutletrelation.MerchantOutletRelationMapper">

    <!-- 查询省级目标换码数 -->
    <select id="selectBranchCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.TargetYardCounts">
        select count(id) counts,partner_id id from merchant_outlet_relation
        <where>
            <if test="branchId != '' and branchId != null">
                and branch_id = #{branchId}
            </if>
        </where>
        group by partner_id
    </select>
    <!-- 查询市级目标换码数 -->
    <select id="selectPartnerCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.TargetYardCounts">
        select count(id) counts,agent_id id from merchant_outlet_relation where partner_id = #{partnerId} group by agent_id
    </select>
    <!-- 查询区县目标换码数 -->
    <select id="selectAgentCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.TargetYardCounts">
        select count(id) counts,accept_id id from merchant_outlet_relation where agent_id = #{agentId} group by accept_id
    </select>
</mapper>