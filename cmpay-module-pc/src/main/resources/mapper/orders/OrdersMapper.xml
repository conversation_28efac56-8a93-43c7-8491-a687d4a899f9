<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.orders.OrdersMapper">

    <!-- 查询成功订单明细 -->
    <select id="failureOrderList"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.OrdersDetailVo">
        select a.order_id,a.refund_order_id,a.out_refund_id transaction_id,b.device_info device,a.refund_end_time time_refund,a.refund_money,b.shoppro_content activity_content,
        (case when b.second = 1 then b.activity_money else 0 end) activity_money,a.return_commission commission,b.order_type,b.error_msg goodsContent,b.short_key,b.total_normal,
        a.refund_start_time time_end,b.total_fee,b.order_status,b.refund_status
        from orders_refund a,orders b
        <where>
            and a.order_id = b.order_id
            and a.refund_status = 2
            and b.shop_id = #{orders.shopId}
            <if test="orders.isBoss == '2' and orders.isBoss == '3'">
                and b.xmid = #{orders.xmId}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and b.device_info in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="orders.device != null and orders.device != '' and orders.device != 'all'">
                and b.device_info = #{orders.device}
            </if>
            <if test="shortUrlList != null and shortUrlList.size > 0">
                and b.short_key in
                <foreach collection="shortUrlList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="orders.shortKey != null and orders.shortKey != '' and orders.shortKey != 'all'">
                and b.short_key = #{orders.shortKey}
            </if>
            <if test="orders.orderId != null and orders.orderId != ''">
                and a.order_id = #{orders.orderId}
            </if>
            <if test="orders.start != null and orders.start != ''">
                and a.refund_end_time <![CDATA[  >=  ]]> #{orders.start}
            </if>
            <if test="orders.end != null and orders.end != ''">
                and a.refund_end_time <![CDATA[  <=  ]]> #{orders.end}
            </if>
            <if test="orders.totalNormal != null and orders.totalNormal != ''">
                and b.total_normal = #{orders.totalNormal}
            </if>
            <if test="orders.orderType != null and orders.orderType != '' and orders.orderType != 'all'">
                and b.order_type = #{orders.orderType}
            </if>
            <if test="orders.refundOrderId != null and orders.refundOrderId != ''">
                and a.refund_order_id = #{orders.refundOrderId}
            </if>
        </where>
        order by a.refund_end_time desc
        <if test="type == 'search'">
            limit #{orders.pageNo},#{orders.pageSize}
        </if>
    </select>

    <!-- 查询失败订单明细 -->
    <select id="successOrderList"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.OrdersDetailVo">
        SELECT order_id,transaction_id,device_info device,(case when time_end='0000-00-00 00:00:00' then '' else time_end end) time_end,
        total_fee,shoppro_content activity_content,(case when `second` = 1 then activity_money else 0 end) activity_money,total_normal,refund_money,commission,(total_normal - commission) paidout,
        order_type,order_status,goods_content,refund_status,shop_order_id,channel_trade_no,sub_openid,short_key,refund_time time_refund
        from orders
        <where>
            and shop_id = #{orders.shopId}
            <if test="deviceList != null and deviceList.size > 0">
                and device_info in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="orders.device != null and orders.device != '' and orders.device != 'all'">
                and device_info = #{orders.device}
            </if>
            <if test="shortUrlList != null and shortUrlList.size > 0">
                and short_key in
                <foreach collection="shortUrlList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="orders.shortKey != null and orders.shortKey != '' and orders.shortKey != 'all'">
                and short_key = #{orders.shortKey}
            </if>
            <if test="orders.start != null and orders.start != ''">
                and time_end <![CDATA[  >=  ]]> #{orders.start}
            </if>
            <if test="orders.end != null and orders.end != ''">
                and time_end <![CDATA[  <=  ]]> #{orders.end}
            </if>
            <if test="orders.orderId != null and orders.orderId != ''">
                and order_id = #{orders.orderId}
            </if>
            <if test="orders.totalNormal != null and orders.totalNormal != ''">
                and total_normal = #{orders.totalNormal}
            </if>
            <if test="orders.orderStatus != null and orders.orderStatus != '' and orders.orderStatus != 'all'">
                and order_status = #{orders.orderStatus}
            </if>
            <if test="orders.orderType != null and orders.orderType != '' and orders.orderType != 'all'">
                and order_type = #{orders.orderType}
            </if>
        </where>
        order by time_end desc
    </select>

    <!-- 查询订单详情 -->
    <select id="getOrderDetails"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.SearchOrderDetailsRespVo">
        SELECT o.shop_id,o.shop_name,o.device_info,o.order_id,o.transaction_id,o.shop_order_id,o.channel_trade_no,o.total_normal,o.total_fee,o.commission,o.order_type,o.order_status,
               o.channel,o.time time_start,(case when o.time_end='0000-00-00 00:00:00' then '' else o.time_end end ) time_end,o.cloud_print_status print_status,(case when o.cloud_print_status='1' then '已打印' else '未打印' end ) print_status_str,o.public_status,o.sub_openid openid,o.short_key,o.refund_money,o.refund_status,o.user_id,m.trueid,c.channel_abbreviation channel_str
        FROM orders o
                 LEFT JOIN merchant m ON o.shop_id = m.shop_id
                 LEFT JOIN channel c ON c.channel_value = o.channel
        WHERE o.order_id = #{ordersDetails.orderId}
    </select>

    <!-- 查询退款订单详情 -->
    <select id="getRefundOrderDetails"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.ordersrefund.OrdersRefundDO">
        select *
        from orders_refund
        where refund_order_id = #{refundOrderId} and refund_status=2
    </select>

    <!-- 查询交班成功订单 -->
    <select id="searchOrderMoney"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo.SearchOrderMoneyVo">
        select order_type,count(order_id) num,sum(total_normal) money
        from orders
        <where>
            order_status = 1
            and shop_id = #{insertDuty.shopId}
            and time_end <![CDATA[  >=  ]]> #{insertDuty.startTime}
            and time_end <![CDATA[  <=  ]]> #{insertDuty.endTime}
            <if test="insertDuty.device != null and insertDuty.device != '' and insertDuty.device != 'all'">
                and device_info = #{insertDuty.device}
            </if>
            <if test="insertDuty.shortKey != null and insertDuty.shortKey != '' and insertDuty.shortKey != 'all'">
                and short_key = #{insertDuty.shortKey}
            </if>
        </where>
        group by order_type
    </select>

    <!-- 订单信息-统计查询-统计成功订单 -->
    <select id="statisticsSuccessOrder"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.StatisticsOrdersNumberVo">
        select sum(total_fee) total_fee,sum(total_normal) total_normal,sum(commission) commission,sum(case when second=1 then activity_money else 0 end) activity_money,count(order_id) total_fee_count
        from orders
        <where>
            and shop_id = #{orders.shopId}
            <if test="orders.isBoss == '2' and orders.isBoss == '3'">
                and xmid = #{orders.xmId}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and device_info in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="orders.device != null and orders.device != '' and orders.device != 'all'">
                and device_info = #{orders.device}
            </if>
            <if test="shortUrlList != null and shortUrlList.size > 0">
                and short_key in
                <foreach collection="shortUrlList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="orders.shortKey != null and orders.shortKey != '' and orders.shortKey != 'all'">
                and short_key = #{orders.shortKey}
            </if>
            <if test="orders.start != null and orders.start != ''">
                and time_end <![CDATA[  >=  ]]> #{orders.start}
            </if>
            <if test="orders.end != null and orders.end != ''">
                and time_end <![CDATA[  <=  ]]> #{orders.end}
            </if>
            <if test="orders.orderId != null and orders.orderId != ''">
                and order_id = #{orders.orderId}
            </if>
            <if test="orders.totalNormal != null and orders.totalNormal != ''">
                and total_normal = #{orders.totalNormal}
            </if>
            <if test="orders.orderStatus != null and orders.orderStatus != '' and orders.orderStatus != 'all'">
                and order_status = #{orders.orderStatus}
            </if>
            <if test="orders.orderType != null and orders.orderType != '' and orders.orderType != 'all'">
                and order_type = #{orders.orderType}
            </if>
        </where>
    </select>

    <!-- 订单信息-统计查询-统计失败订单 -->
    <select id="statisticsFailureOrder"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.StatisticsOrdersNumberVo">
        select sum(a.refund_money) refund_money,count(a.refund_order_id) refund_count,sum(a.return_commission) return_commission
        from orders_refund a
        LEFT JOIN orders b
        on a.order_id = b.order_id
        <where>
            and a.refund_status = 2
            and a.shop_id = #{orders.shopId}
            <if test="orders.isBoss == '2' and orders.isBoss == '3'">
                and b.xmid = #{orders.xmId}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and b.device_info in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="orders.device != null and orders.device != '' and orders.device != 'all'">
                and b.device_info = #{orders.device}
            </if>
            <if test="shortUrlList != null and shortUrlList.size > 0">
                and b.short_key in
                <foreach collection="shortUrlList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="orders.shortKey != null and orders.shortKey != '' and orders.shortKey != 'all'">
                and b.short_key = #{orders.shortKey}
            </if>
            <if test="orders.orderId != null and orders.orderId != ''">
                and a.order_id = #{orders.orderId}
            </if>
            <if test="orders.start != null and orders.start != ''">
                and a.refund_end_time <![CDATA[  >=  ]]> #{orders.start}
            </if>
            <if test="orders.end != null and orders.end != ''">
                and a.refund_end_time <![CDATA[  <=  ]]> #{orders.end}
            </if>
            <if test="orders.totalNormal != null and orders.totalNormal != ''">
                and b.total_normal = #{orders.totalNormal}
            </if>
            <if test="orders.orderType != null and orders.orderType != '' and orders.orderType != 'all'">
                and b.order_type = #{orders.orderType}
            </if>
            <if test="orders.refundOrderId != null and orders.refundOrderId != ''">
                and a.refund_order_id = #{orders.refundOrderId}
            </if>
        </where>
    </select>

    <!-- 根据订单id查询支付金额 -->
    <select id="findOrderIdByTotalFee" resultType="java.lang.Double">
        select sum(total_fee)
        from orders
        <where>
            <if test="orderList != null and orderList.size > 0">
                and order_id in
                <foreach collection="orderList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectPublicOrdersPage" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.OrdersPublicRespVO">
        select a.order_id,b.user,b.address,a.user_id,a.total_normal,a.time_end,a.public_status,b.description,a.refund_status,a.refund_money,a.sub_openid
        from orders a left join publicservice b on a.user_id=b.user_id and a.openid=b.openid
        where a.order_status=1
          <if test="opr.shopId != null and opr.shopId != ''">
              and a.shop_id=#{opr.shopId}
          </if>
          <if test="opr.userId != null and opr.userId != ''">
              and a.user_id=#{opr.userId}
          </if>
          <if test="opr.start != null and opr.start != ''">
              and a.time_end <![CDATA[  >=  ]]> #{opr.start}
          </if>
          <if test="opr.end != null and opr.end != ''">
              and a.time_end <![CDATA[  <=  ]]> #{opr.end}
          </if>
          <if test="opr.publicStatus != null and opr.publicStatus != ''">
              and a.public_status=#{opr.publicStatus}
          </if>
        order by a.time_end desc
    </select>

    <!-- 代理商交易信息——查询订单信息 -->
    <select id="searchAgentOrders"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.AgentOrdersDetailVo">
        select order_id,shop_id,device_info device,time_end,total_normal,refund_money,commission,channel,refund_status,
               order_status,order_type,pay_type,shop_order_id,shop_name,transaction_id,channel_trade_no,sub_openid
        from orders
        <where>
            and agent_id = #{agentOrdersReqVo.agentId}
            <if test="agentOrdersReqVo.acceptId != null and agentOrdersReqVo.acceptId != ''">
                and accept_id = #{agentOrdersReqVo.acceptId}
            </if>
            <if test="agentOrdersReqVo.shopId != null and agentOrdersReqVo.shopId != ''">
                and shop_id = #{agentOrdersReqVo.shopId}
            </if>
            <if test="agentOrdersReqVo.orderId != null and agentOrdersReqVo.orderId != ''">
                and order_id = #{agentOrdersReqVo.orderId}
            </if>
            <if test="agentOrdersReqVo.shopOrderId != null and agentOrdersReqVo.shopOrderId != ''">
                and shop_order_id = #{agentOrdersReqVo.shopOrderId}
            </if>
            <if test="agentOrdersReqVo.transationId != null and agentOrdersReqVo.transationId != ''">
                and transaction_id = #{agentOrdersReqVo.transationId}
            </if>
            <if test="agentOrdersReqVo.start != null and agentOrdersReqVo.start != ''">
                and time_end <![CDATA[  >=  ]]> #{agentOrdersReqVo.start}
            </if>
            <if test="agentOrdersReqVo.end != null and agentOrdersReqVo.end != ''">
                and time_end <![CDATA[  <=  ]]> #{agentOrdersReqVo.end}
            </if>
            <if test="agentOrdersReqVo.totalFee != null and agentOrdersReqVo.totalFee != ''">
                and total_fee = #{agentOrdersReqVo.totalFee}
            </if>
            <if test="agentOrdersReqVo.orderStatus != null and agentOrdersReqVo.orderStatus != ''">
                and order_status = #{agentOrdersReqVo.orderStatus}
            </if>
            <if test="agentOrdersReqVo.orderType != null and agentOrdersReqVo.orderType != ''">
                and order_type = #{agentOrdersReqVo.orderType}
            </if>
        </where>
        order by time_end desc
    </select>

    <!-- 代理商交易信息——查询失败订单信息 -->
    <select id="failureAgentOrderList"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.AgentOrdersDetailVo">
        select a.order_id,a.shop_id,a.device_info device,b.refund_end_time time_end,b.refund_money,b.return_commission,b.channel,a.order_status,
               b.order_type,a.pay_type,b.refund_status,a.shop_order_id,a.shop_name,a.transaction_id,a.channel_trade_no,b.refund_order_id
        from orders_refund b,orders a
        <where>
            a.order_id=b.order_id
            and b.refund_status = '2'
            and a.agent_id = #{agentOrdersReqVo.agentId}
            <if test="agentOrdersReqVo.acceptId != null and agentOrdersReqVo.acceptId != ''">
                and a.accept_id = #{agentOrdersReqVo.acceptId}
            </if>
            <if test="agentOrdersReqVo.shopId != null and agentOrdersReqVo.shopId != ''">
                and b.shop_id = #{agentOrdersReqVo.shopId}
            </if>
            <if test="agentOrdersReqVo.orderId != null and agentOrdersReqVo.orderId != ''">
                and a.order_id = #{agentOrdersReqVo.orderId}
            </if>
            <if test="agentOrdersReqVo.shopOrderId != null and agentOrdersReqVo.shopOrderId != ''">
                and a.shop_order_id = #{agentOrdersReqVo.shopOrderId}
            </if>
            <if test="agentOrdersReqVo.transationId != null and agentOrdersReqVo.transationId != ''">
                and a.transaction_id = #{agentOrdersReqVo.transationId}
            </if>
            <if test="agentOrdersReqVo.start != null and agentOrdersReqVo.start != ''">
                and b.refund_end_time <![CDATA[  >=  ]]> #{agentOrdersReqVo.start}
            </if>
            <if test="agentOrdersReqVo.end != null and agentOrdersReqVo.end != ''">
                and b.refund_end_time <![CDATA[  <=  ]]> #{agentOrdersReqVo.end}
            </if>
            <if test="agentOrdersReqVo.totalFee != null and agentOrdersReqVo.totalFee != ''">
                and total_fee = #{agentOrdersReqVo.totalFee}
            </if>
            <if test="agentOrdersReqVo.orderStatus != null and agentOrdersReqVo.orderStatus != ''">
                and order_status = #{agentOrdersReqVo.orderStatus}
            </if>
            <if test="agentOrdersReqVo.orderType != null and agentOrdersReqVo.orderType != ''">
                and order_type = #{agentOrdersReqVo.orderType}
            </if>
        </where>
        order by b.refund_end_time desc limit #{agentOrdersReqVo.pageNo},#{agentOrdersReqVo.pageSize}
    </select>

    <!-- 获取api的需要回调的参数信息 -->
    <select id="getApiOrder" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.orders.OrdersDO">
        select * from orders where order_id = #{orderId} and order_status = 1
        UNION
        select * from orders where transaction_id = #{orderId} and order_status = 1
    </select>

    <!-- 根据订单号查询投诉单号(目前只处理订单风控) -->
    <select id="getTransactionId" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.orders.OrdersDO">
        select shop_order_id,shop_id,total_normal,pay_type,second,rotate_card_id,total_fee,sub_openid,order_type,time_end,transaction_id,channel_trade_no,agent_id,short_key,device_info,activity_money,shoppro_content,commission,bank_type,param_1
        from orders
        where order_id = #{orderId} and  shop_id = #{shopId} and channel = #{channel}
    </select>

    <!-- 根据订单号查询订单详情 -->
    <select id="getOrderNewDetails"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.OrderNewDetailsRespVO">
        SELECT m.trueid                        true_id,
               m.shop_id,
               m.shop_name,
               o.device_info                   device,
               s.short_key,
               s.nickname                      short_name,
               o.time                          start_time,
               o.time_end                      pay_time,
               o.result_time                   resp_time,
               o.order_id,
               o.shop_order_id,
               o.transaction_id,
               o.channel_trade_no,
               o.openid,
               o.total_fee,
               o.total_normal,
               o.commission,
               (o.total_normal - o.commission) paid_out_str,
               o.order_type,
               sdd1.label                      order_type_str,
               o.pay_type,
               sdd2.label                      pay_type_str,
               o.error_msg,
               o.goods_content,
               o.cloud_print_status            print_status,
               (case
                    when o.cloud_print_status = '1' then '已打印'
                    when o.cloud_print_status = '3' then '打印失败'
                    else '未打印' end)         print_status_str,
               o.user_id,
               o.public_status,
               o.channel,
               c.channel_abbreviation channel_str,
               o.order_status,
               o.refund_status,
               o.refund_money
        from orders o
                 LEFT JOIN merchant m ON o.shop_id = m.shop_id
                 LEFT JOIN shorturl s ON o.short_key = s.short_key
                 LEFT JOIN channel c ON c.channel_value = o.channel
                 LEFT JOIN system_dict_data sdd1 ON sdd1.value = o.order_type and sdd1.dict_type = 'ordersType'
                 LEFT JOIN system_dict_data sdd2 ON sdd2.value = o.pay_type and sdd2.dict_type = 'interface_type'
        WHERE o.order_id = #{orderId};
    </select>

    <!-- 查询收款订单分页 -->
    <select id="paidOutOrdersPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.api.PaidOutOrdersPageRespVO">
        SELECT order_id,time_end pay_time,(case when order_status = 1 then total_normal else total_fee end) pay_money,(case when device_info = null then '未配置分店' when device_info = '' then '未命名分店' else device_info end) device_name,order_type,order_status,refund_status ,total_fee
        from orders
        <include refid="paidOutPayOrders"></include>
        order by time_end desc limit 300
    </select>

    <!-- 查询收款的退款订单 -->
    <select id="paidOutRefundOrdersPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.api.PaidOutOrdersPageRespVO">
        select a.order_id,b.refund_end_time pay_time,a.refund_money pay_money,(CASE WHEN a.device_info = NULL THEN '未配置分店' WHEN a.device_info = '' THEN '未命名分店' ELSE a.device_info END) device_name,b.order_type,b.refund_order_id,b.refund_status order_status
        from orders_refund b
        LEFT JOIN orders a ON a.order_id = b.order_id
        <include refid="paidOutRefundOrders"></include>
        order by b.refund_end_time desc
        limit #{pageVO.pageNo},#{pageVO.pageSize}
    </select>

    <!-- 查询收款成功订单详情 -->
    <select id="paidOutOrderDetail"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.api.PaidOutOrderDetailRespVO">
        SELECT o.order_id,
               o.time_end                      pay_time,
               o.order_type,
               o.transaction_id,
               o.shop_order_id,
               o.device_info                   device_name,
               o.short_key,
               o.openid,
               o.total_fee,
               (case when o.order_status !='1' then o.total_fee else o.total_normal end) pay_money,
               (case when o.commission != null then o.commission else '0.00' end) commission,
               (case when o.order_status ='1' then (o.total_normal - o.commission) else '0.00' end) paidout_money,
               COALESCE(o.activity_money, '0.00') activity_money,
               sdd1.label                      order_type_str,
               o.goods_content,
               o.refund_money,
               o.order_status
        FROM orders o
                 LEFT JOIN system_dict_data sdd1 ON sdd1.value = o.order_type and sdd1.dict_type = 'ordersType'
        WHERE o.order_id = #{orderId} and o.shop_id = #{shopId};
    </select>

    <select id="paidOutOrdersCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.api.StatisticsPaidOutOrdersVO">
        <choose>
            <when test="type == 'refund'">
                select sum(b.refund_money) total_fee,count(b.refund_order_id) total_fee_count
                from orders_refund b
                LEFT JOIN orders a ON a.order_id = b.order_id
                <include refid="paidOutRefundOrders"></include>
            </when>
            <when test="type == 'pay'">
                select sum(total_normal) total_fee,count(order_id) total_fee_count
                from orders
                <include refid="paidOutPayOrders"></include>
            </when>
        </choose>
    </select>

    <sql id="paidOutRefundOrders">
        <where>
            b.shop_id = #{pageVO.shopId} and b.refund_status = '2'
            <if test="pageVO.startTime != null and pageVO.startTime != ''">
                and b.refund_end_time <![CDATA[  >=  ]]> #{pageVO.startTime}
            </if>
            <if test="pageVO.endTime != null and pageVO.endTime != ''">
                and b.refund_end_time <![CDATA[  <=  ]]> #{pageVO.endTime}
            </if>
            <if test="pageVO.pay_money != null and pageVO.pay_money != ''">
                and a.total_normal = #{pageVO.pay_money}
            </if>
            <if test="pageVO.equipment != null and pageVO.equipment != '' and pageVO.equipment != 'all'">
                and a.short_key = #{pageVO.equipment}
            </if>
            <if test="pageVO.pay_type != null and pageVO.pay_type != '' and pageVO.pay_type != 'all'">
                and b.order_type = #{pageVO.pay_type}
            </if>
            <if test="pageVO.shop_name != null and pageVO.shop_name != '' and pageVO.shop_name != 'all'">
                and a.device_info = #{pageVO.shop_name}
            </if>
            <if test="pageVO.shortKeyList != null and pageVO.shortKeyList.size > 0">
                and a.short_key in
                <foreach collection="pageVO.shortKeyList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="pageVO.deviceList != null and pageVO.deviceList.size > 0">
                and a.device_info in
                <foreach collection="pageVO.deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="pageVO.pay_status != null and pageVO.pay_status != ''">
                <choose>
                    <when test="pageVO.pay_status == '1'.toString() and pageVO.pay_status == 'all'">
                        and a.order_status = 1
                    </when>
                    <when test="pageVO.pay_status == '0'.toString()">
                        and (a.order_status = 0 or a.order_status=2)
                    </when>
                    <when test="pageVO.pay_status == '3'.toString()">
                        and a.order_status = 3
                    </when>
                </choose>
            </if>
        </where>
    </sql>

    <sql id="paidOutPayOrders">
        <where>
            shop_id = #{pageVO.shopId}
            <if test="pageVO.startTime != null and pageVO.startTime != ''">
                and time_end <![CDATA[  >=  ]]> #{pageVO.startTime}
            </if>
            <if test="pageVO.endTime != null and pageVO.endTime != ''">
                and time_end <![CDATA[  <=  ]]> #{pageVO.endTime}
            </if>
            <if test="pageVO.pay_money != null and pageVO.pay_money != ''">
                and total_normal = #{pageVO.pay_money}
            </if>
            <if test="pageVO.equipment != null and pageVO.equipment != '' and pageVO.equipment != 'all'">
                and short_key = #{pageVO.equipment}
            </if>
            <if test="pageVO.pay_type != null and pageVO.pay_type != '' and pageVO.pay_type != 'all'">
                and order_type = #{pageVO.pay_type}
            </if>
            <if test="pageVO.shop_name != null and pageVO.shop_name != '' and pageVO.shop_name != 'all'">
                and device_info = #{pageVO.shop_name}
            </if>
            <if test="pageVO.shortKeyList != null and pageVO.shortKeyList.size > 0">
                and short_key in
                <foreach collection="pageVO.shortKeyList" item="item" index="index" open="(" separator="," close=")">
                    #{item.shortKey}
                </foreach>
            </if>
            <if test="pageVO.pay_status != null and pageVO.pay_status != ''">
                <choose>
                    <when test="pageVO.pay_status == '1'.toString()">
                        and order_status = 1
                    </when>
                    <when test="pageVO.pay_status == '0'.toString()">
                        and (order_status = 0 or order_status=2)
                    </when>
                    <when test="pageVO.pay_status == '3'.toString()">
                        and order_status = 3
                    </when>
                </choose>
            </if>
            <if test="pageVO.deviceList != null and pageVO.deviceList.size > 0">
                and device_info in
                <foreach collection="pageVO.deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
        </where>
    </sql>

</mapper>