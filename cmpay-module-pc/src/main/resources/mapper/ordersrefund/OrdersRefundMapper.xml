<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.ordersrefund.OrdersRefundMapper">


    <select id="getOrderId" resultType="java.lang.String">
        select order_id from orders_refund where refund_order_id = #{refundOrderId}
    </select>

    <select id="searchRefundOrderMoney"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.merchantdutyorders.vo.SearchOrderMoneyVo">
        select a.order_type,count(a.refund_order_id) num,sum(a.refund_money) money from orders_refund a,orders b
        <where>
            a.refund_status = 2
            and a.order_id=b.order_id
            and a.shop_id = #{insertDuty.shopId}
            and a.refund_end_time <![CDATA[  >=  ]]> #{insertDuty.startTime}
            and a.refund_end_time <![CDATA[  <=  ]]> #{insertDuty.endTime}
            <if test="insertDuty.device != null and insertDuty.device != '' and insertDuty.device != 'all'">
                and b.device_info = #{insertDuty.device}
            </if>
            <if test="insertDuty.shortKey != null and insertDuty.shortKey != '' and insertDuty.shortKey != 'all'">
                and b.short_key = #{insertDuty.shortKey}
            </if>
        </where>
        group by a.order_type
    </select>

    <!-- 根据订单id或者退款信息 -->
    <select id="getRefundOrders"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.orders.vo.RefundOrdersDetailVO">
        select orf.refund_order_id,
               orf.refund_money,
               sdd1.label                      order_type_str,
               orf.refund_status,
               orf.refund_end_time refund_pay_time,
               (case when orf.refund_status = '2' then '退款成功' else '退款失败' end) refund_status_str,
               orf.order_id,
               orf.refund_msg
        from orders_refund orf
         LEFT JOIN system_dict_data sdd1 ON sdd1.value = orf.order_type and sdd1.dict_type = 'ordersType'
        <where>
            <if test="refundOrderId != null and refundOrderId != ''">
                orf.refund_order_id = #{refundOrderId}
            </if>
            <if test="orderId != null and orderId != ''">
                orf.order_id = #{orderId}
            </if>
        </where>
    </select>
</mapper>