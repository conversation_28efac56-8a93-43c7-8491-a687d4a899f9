<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.paidout.PaidoutMapper">

    <select id="settlementDetail" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.paidout.vo.SettlementDetailVo">
        select (case when payment_status=0 then audit_time else payment_time end) payment_time,pay_type,payment_status,start_time,order_type,sum(money_paidout) paidout_money,sum(commission) commission,sid shop_id,payment_fail_msg,end_time
        FROM paidout
        <where>
            and sid = #{settlementReqVo.shopId}
            <if test="settlementReqVo.start != null and settlementReqVo.start != ''">
                and payment_time <![CDATA[ >= ]]> #{settlementReqVo.start}
            </if>
            <if test="settlementReqVo.end != null and settlementReqVo.end != ''">
                and payment_time <![CDATA[ < ]]> date_add(#{settlementReqVo.end}, INTERVAL 1 day)
            </if>
            <if test="settlementReqVo.paymentStatus != null and settlementReqVo.paymentStatus != '' and settlementReqVo.paymentStatus != 'all'">
                and payment_status = #{settlementReqVo.paymentStatus}
            </if>
        </where>
        group by (case when payment_status=0 then audit_time else payment_time end),pay_type
        order by audit_time desc
    </select>

    <select id="settlementOrderType"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.paidout.vo.settlementOrderTypeVo">
        select order_type,sum(money_paidout) paidout_money,sum(commission) commission,payment_time,pay_type
        from paidout
        <where>
            and sid = #{settlementDetailVo.shopId}
            <if test="settlementDetailVo.paymentTime != null and settlementDetailVo.paymentTime != ''">
                and payment_time <![CDATA[ >= ]]> #{settlementDetailVo.paymentTime}
                and payment_time <![CDATA[ < ]]> date_add(#{settlementDetailVo.paymentTime}, INTERVAL 1 day)
            </if>
            <if test="settlementDetailVo.paymentStatus != null and settlementDetailVo.paymentStatus != '' and settlementDetailVo.paymentStatus != 'all'">
                and payment_status = #{settlementDetailVo.paymentStatus}
            </if>
        </where>
        group by order_type,payment_time
    </select>
</mapper>