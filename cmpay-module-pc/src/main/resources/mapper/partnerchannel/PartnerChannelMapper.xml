<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.partnerchannel.PartnerChannelMapper">

    <!-- 根据大渠道id修改通道的保底费率 -->
    <update id="updateDefaultFeeRate">
        update partner_channel
        <set>
            <if test="settleChannel == 'ruiyinxin'">
                rate_ruiyinxin = #{minRate},
            </if>
            <if test="settleChannel == 'sxf_tq'">
                rate_sxf_tq = #{minRate},
            </if>
            <if test="settleChannel == 'hulu'">
                rate_hulu = #{minRate},
            </if>
            <if test="settleChannel == 'yz_pay'">
                rate_yz_pay = #{minRate},
            </if>
            <if test="settleChannel == 'ylsw'">
                rate_ylsw = #{minRate}
            </if>
        </set>
        where partner_id = #{partnerId}
    </update>

    <!-- 根据省级id查询机构 -->
    <select id="selectBranchUser"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO">
        select partner_id,company from partner_channel
        <where>
            <if test="statisticsYardReqVo.branchId != null and statisticsYardReqVo.branchId != ''">
                and branch_office_id = #{statisticsYardReqVo.branchId}
            </if>
        </where>
    </select>

    <!-- 初始化市级公司 -->
    <select id="initPartnerChannel"
            resultType="com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo">
        select pc.partner_id id,pc.company
        from partner_channel pc
        LEFT JOIN branch_office bo
        ON pc.branch_office_id = bo.id
        <where>
            pc.partner_id !='parent_channel_6216'
            <if test="initChannelReqVo.branchId != null and initChannelReqVo.branchId != '' and initChannelReqVo.branchId != 'all'">
                and pc.branch_office_id = #{initChannelReqVo.branchId}
            </if>
            <if test="initChannelReqVo.company != null and initChannelReqVo.company != ''">
                and pc.company like concat('%',#{initChannelReqVo.company},'%')
            </if>
            <if test="initChannelReqVo.isReport != null and initChannelReqVo.isReport != '' and initChannelReqVo.isReport == '1'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                <if test="initChannelReqVo.isGd != null and initChannelReqVo.isGd != '' and initChannelReqVo.isGd == '1'.toString()">
                    and bo.id = '2'
                </if>
                <if test="initChannelReqVo.isGd != null and initChannelReqVo.isGd != '' and initChannelReqVo.isGd == '0'.toString()">
                    and bo.id != '2'
                </if>
            </if>
            <if test="initChannelReqVo.isReport != null and initChannelReqVo.isReport != '' and initChannelReqVo.isReport == '2'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                and pc.branch_office_id in ('3','6','9')
            </if>
        </where>
    </select>

    <!-- 查询公司归属 -->
    <select id="getCompanyAffiliation"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo.CompanyAffiliationRespVo">
        select a.accept_id,a.company accept_company,a.yz_org_id accept_yz_org_id,
        b.agent_id,b.company agent_company,b.yz_org_id agent_yz_org_id,c.partner_id,
        c.company partner_company,c.yz_org_id partner_yz_org_id,d.id branch_id,d.company branch_company,d.keeper_phone
        branch_yz_org_id
        from accept a,agent b,partner_channel c,branch_office d
        <where>
            a.agent_id=b.agent_id
            and b.partner_id=c.partner_id
            and c.branch_office_id=d.id
            <if test="companyAffiliationVo.acceptId != null and companyAffiliationVo.acceptId != ''">
                and a.accept_id = #{companyAffiliationVo.acceptId}
            </if>
            <if test="companyAffiliationVo.agentId != null and companyAffiliationVo.agentId != ''">
                and a.agent_id = #{companyAffiliationVo.agentId}
            </if>
            <if test="companyAffiliationVo.partnerId != null and companyAffiliationVo.partnerId != ''">
                and b.partner_id = #{companyAffiliationVo.partnerId}
            </if>
            <if test="companyAffiliationVo.branchId != null and companyAffiliationVo.branchId != ''">
                and c.branch_office_id = #{companyAffiliationVo.branchId}
            </if>
        </where>
        limit 1
    </select>

    <!-- 查询省市区县网点信息 -->
    <select id="getHomeOrganization"
            resultType="com.cmpay.code.module.system.controller.admin.aochuangadmin.vo.InternalUserDetailsResp">
        SELECT ac.accept_id,ac.company accept_company,ag.agent_id,ag.company agent_company,pc.partner_id,pc.company
        partner_company,bo.id,bo.company branch_company
        from accept ac
        LEFT JOIN agent ag
        on ac.agent_id = ag.agent_id
        LEFT JOIN partner_channel pc
        on ag.partner_id = pc.partner_id
        LEFT JOIN branch_office bo
        on pc.branch_office_id = bo.id
        where
        <choose>
            <when test="platform == '1'.toString()">
                bo.id = #{platformId}
            </when>
            <when test="platform == '2'.toString()">
                pc.partner_id = #{platformId}
            </when>
            <when test="platform == '3'.toString()">
                ag.agent_id = #{platformId}
            </when>
            <when test="platform == '4'.toString()">
                ac.accept_id = #{platformId}
            </when>
        </choose>
    </select>

    <!-- 查询当前省下面的所有市 -->
    <select id="selectPartnerByBranchId"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO">
        select partner_id, company
        from partner_channel
        where branch_office_id = #{branchId}
    </select>

    <select id="selectByCompany"
            resultType="java.lang.String">
        select partner_id
        from partner_channel
        where company like concat('%', #{company}, '%')
    </select>


    <!--     大渠道地区管理查询-->

    <select id="getPartnerAreaManage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.vo.GetPartnerAreaManageRespVO">
        select a.id,a.partner_id,a.agent_id,a.province,a.province_id,a.city,a.city_id,b.company,b.keeper,b.keeperphone
        from pay.partner_area a,pay.partner_channel b where a.partner_id=b.partner_id and a.type = 'partner'
        <if test="getPartnerAreaManageReqVO.partnerId!=null and getPartnerAreaManageReqVO.partnerId!=''">
            and a.partner_id = #{getPartnerAreaManageReqVO.partnerId}
        </if>
        <if test="getPartnerAreaManageReqVO.keeperphone!=null and getPartnerAreaManageReqVO.keeperphone!=''">
            and b.keeperphone = #{getPartnerAreaManageReqVO.keeperphone}
        </if>
        <if test="getPartnerAreaManageReqVO.keeper!=null and getPartnerAreaManageReqVO.keeper!=''">
            and b.keeper like concat('%',#{getPartnerAreaManageReqVO.keeper},'%')
        </if>
        <if test="getPartnerAreaManageReqVO.company!=null and getPartnerAreaManageReqVO.company!=''">
            and b.company like concat('%',#{getPartnerAreaManageReqVO.company},'%')
        </if>
        order by a.id desc
    </select>

    <!-- 根据keeperPhone查询信息 -->
    <select id="findByKeeperPhone"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO">
        select *
        from partner_channel
        where keeperphone = #{account}
    </select>

    <select id="selectChannelPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.partnerchannel.dto.ChannelRespDTO">
        SELECT pc.*,if(pr.is_standby_channel IS NULL,0,pr.is_standby_channel) AS is_standby_channel,pr.ywy_max_subsidy_money,
        pr.is_visit_task,pr.is_target_mer,pr.is_account_subsidy,pr.pc_account_subsidy
        FROM partner_channel pc
        LEFT JOIN partner_right pr ON pc.partner_id = pr.partner_id
        <where>
            <if test="reqVO.company != null and reqVO.company != ''">
                and pc.company like concat('%', #{reqVO.company}, '%')
            </if>
            <if test="reqVO.partnerId != null and reqVO.partnerId != ''">
                and pc.partner_id = #{reqVO.partnerId}
            </if>
            <if test="reqVO.keeper != null and reqVO.keeper != ''">
                and pc.keeper = #{reqVO.keeper}
            </if>
            <if test="reqVO.keeperphone != null and reqVO.keeperphone != ''">
                and pc.keeperphone = #{reqVO.keeperphone}
            </if>
            <if test="reqVO.branchId != null and reqVO.branchId != ''">
                and pc.branch_office_id = #{reqVO.branchId}
            </if>
            <if test="reqVO.yzOrgId != null and reqVO.yzOrgId != ''">
                and pc.yz_org_id = #{reqVO.yzOrgId}
            </if>
        </where>
        ORDER BY pc.insert_time DESC
    </select>
    <select id="initPartnerUsers" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.partnerchannel.PartnerChannelDO">
        select * from partner_channel where branch_office_id = '20'
    </select>


</mapper>