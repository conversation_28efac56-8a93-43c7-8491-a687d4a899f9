<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.partnerright.PartnerRightMapper">

    <!-- 商户公共池——获取客户经理所在市的所有区县 -->
    <select id="getDistByPartnerId"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.partnerright.vo.DistRespVO">
        SELECT area_no area_code,dist,city,id
        FROM partner_right pr
        LEFT JOIN china_area_no ca
        ON ca.area_no = pr.city_code or ca.partner_id = pr.city_code
        WHERE pr.partner_id = #{partnerId} and dist is not null and dist != ''
    </select>
    <select id="getPartnerRightIsAccountSubsidy" resultType="java.lang.String">
       select  is_account_subsidy from partner_right where
        <if test="partnerId != null and partnerId != ''">
            partner_id = #{partnerId}
        </if>
    </select>
</mapper>