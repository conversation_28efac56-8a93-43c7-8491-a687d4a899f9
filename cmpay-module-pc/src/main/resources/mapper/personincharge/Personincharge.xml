<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.personincharge.PersonInChargeMapper">

    <select id="selectName" resultType="java.lang.String">
        select u.`name`
            from person_in_charge pic
                left join user u on pic.xmid = u.xmid
                    where pic.status=1
                      <if test="deviceId!=null and deviceId != ''">
                          and pic.device_id = #{deviceId}
                      </if>
                      <if test="phone!=null and phone != ''">
                          and u.phone = #{phone}
                      </if>

    </select>
</mapper>