<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.profitshareorders.ProfitShareOrdersMapper">


    <!-- 订单分账信息查询 -->
    <select id="searchProfitShareOrders"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo.ProfitShareOrdersVo">
        select p.ps_order_id,p.order_id,p.ps_money,p.ps_order_status,(case when p.ps_order_status = 1 then '成功' when p.ps_order_status = 0 then '失败' else '' end) ps_order_status_str,
               p.ps_end_time,p.channel,c.channel_abbreviation channel_str,
               p.ps_type,(case when p.ps_type = 'direct' then '实时分账' when p.ps_type = 'delay' then '延迟分账' else '' end) ps_type_str,
               p.shop_id, m1.trueid AS trueid,m1.shop_name AS shop_name, p.ps_shop_id, m2.trueid AS ps_trueid,m2.shop_name AS ps_shop_name,
               o.total_fee order_money
        FROM profit_share_orders p
        LEFT JOIN merchant m1 ON p.shop_id = m1.shop_id
        LEFT JOIN merchant m2 ON p.ps_shop_id = m2.shop_id
        LEFT JOIN orders o ON o.order_id = p.order_id
        LEFT JOIN channel c ON c.channel_value = p.channel
        <include refid="commonWhereBlock"/>
        order by p.ps_end_time desc
    </select>

    <!-- 获取出入账金额以及笔数 -->
    <select id="searchMerchantMoneySum"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.profitshareorders.vo.StatisticsProfitShareOrdersRespVo">
        SELECT
        SUM(o.total_fee) AS money_sum,
        SUM(p.ps_money) AS ps_money_sum,
        COUNT(*) AS orders_count
        FROM profit_share_orders p
        LEFT JOIN orders o ON o.order_id = p.order_id
        <include refid="commonWhereBlock"/>
    </select>

    <!-- 获取出账商户数量 -->
    <select id="getShopCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT p.ps_shop_id) AS shop_count
        FROM profit_share_orders p
        <include refid="commonWhereBlock"/>
    </select>

    <!-- 获取入账商户数量 -->
    <select id="getPsShopCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT p.ps_shop_id) AS shop_count
        FROM profit_share_orders p
        <include refid="commonWhereBlock"/>
    </select>

    <!-- 定义一个可重用的 where 条件块 -->
    <sql id="commonWhereBlock">
        <where>
            <if test="profitShareOrders.shopId != null and profitShareOrders.shopId != ''">
                and (p.shop_id = #{profitShareOrders.shopId} or p.ps_shop_id = #{profitShareOrders.shopId})
            </if>
            <if test="profitShareOrders.orderId != null and profitShareOrders.orderId != ''">
                and p.order_id = #{profitShareOrders.orderId}
            </if>
            <if test="profitShareOrders.psOrderId != null and profitShareOrders.psOrderId != ''">
                and p.ps_order_id = #{profitShareOrders.psOrderId}
            </if>
            <if test="profitShareOrders.start != null and profitShareOrders.start != ''">
                and p.ps_end_time <![CDATA[  >=  ]]> #{profitShareOrders.start}
            </if>
            <if test="profitShareOrders.end != null and profitShareOrders.end != ''">
                and p.ps_end_time <![CDATA[  <=  ]]> #{profitShareOrders.end}
            </if>
            <if test="profitShareOrders.psOrderStatus != null and profitShareOrders.psOrderStatus != ''">
                and p.ps_order_status = #{profitShareOrders.psOrderStatus}
            </if>
            <if test="profitShareOrders.agentSubId != null and profitShareOrders.agentSubId != ''">
                and p.sub_agent_id = #{profitShareOrders.agentSubId}
            </if>
        </where>
    </sql>
</mapper>
