<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.riskmanage.RiskManageMapper">

    <!-- 风险管理——商户风控管理——分页查询 -->
    <select id="searchRiskManageMerchant"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.riskmanage.vo.SearchRiskManageMerchantRespVO">
        SELECT a.risk_manage_id,a.shop_id,e.trueid,e.shop_name,a.risk_type,a.risk_reason,a.frozen_amount,(case when a.risk_time='0000-00-00 00:00:00' then '' else a.risk_time end ) risk_time,a.source,
        a.feedback_timeliness,a.complaint_handle_state,e.ywy_openid,a.channel,a.appeal_request,e.accept_id,b.company agent_name,c.company partner_name,d.company branch_name,a.remarks_msg,a.request_id
        from merchant e,risk_manage a,agent b,partner_channel c
        left join branch_office d
        on c.branch_office_id=d.id
        <where>
            a.shop_id=e.shop_id
            and e.agent_id=b.agent_id
            and b.partner_id=c.partner_id
            and a.insert_time != '0000-00-00 00:00:00'
            <if test="riskManageMerchantVO.acceptId != null and riskManageMerchantVO.acceptId != ''">
                and e.accept_id = #{riskManageMerchantVO.acceptId}
            </if>
            <if test="riskManageMerchantVO.agentId != null and riskManageMerchantVO.agentId != ''">
                and b.agent_id = #{riskManageMerchantVO.agentId}
            </if>
            <if test="riskManageMerchantVO.shopId != null and riskManageMerchantVO.shopId != ''">
                and a.shop_id = #{riskManageMerchantVO.shopId}
            </if>
            <if test="riskManageMerchantVO.riskType != null and riskManageMerchantVO.riskType != ''">
                and a.risk_type = #{riskManageMerchantVO.riskType}
            </if>
            <if test="riskManageMerchantVO.complaintHandleState != null and riskManageMerchantVO.complaintHandleState != ''">
                and a.complaint_handle_state = #{riskManageMerchantVO.complaintHandleState}
            </if>
            <if test="riskManageMerchantVO.shopName != null and riskManageMerchantVO.shopName != ''">
                and e.shop_name like concat('%',#{riskManageMerchantVO.shopName},'%')
            </if>
            <if test="riskManageMerchantVO.start != null and riskManageMerchantVO.start != ''">
                and a.risk_time <![CDATA[  >=  ]]> #{riskManageMerchantVO.start}
            </if>
            <if test="riskManageMerchantVO.end != null and riskManageMerchantVO.end != ''">
                and a.risk_time <![CDATA[  <=  ]]> #{riskManageMerchantVO.end}
            </if>
            <if test="riskManageMerchantVO.source != null and riskManageMerchantVO.source != ''">
                and a.source = #{riskManageMerchantVO.source}
            </if>
            <if test="riskManageMerchantVO.partnerId != null and riskManageMerchantVO.partnerId != ''">
                and c.partner_id = #{riskManageMerchantVO.partnerId}
            </if>
            <if test="riskManageMerchantVO.branchId != null and riskManageMerchantVO.branchId != ''">
                and d.id = #{riskManageMerchantVO.branchId}
            </if>
        </where>
        order by a.insert_time desc
    </select>

    <!-- 风险管理——商户风控管理——根据id查询 -->
    <select id="findById" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.riskmanage.RiskManageDO">
        SELECT * FROM risk_manage WHERE risk_manage_id = #{riskManageId}
    </select>
</mapper>