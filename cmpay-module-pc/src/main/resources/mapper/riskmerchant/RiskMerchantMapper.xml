<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.riskmerchant.RiskMerchantMapper">

    <!-- 风险管理——订单投诉管理——新增订单投诉 -->
    <insert id="insertRiskMerchant">
        insert into risk_merchant (
                        shop_id,
                        order_id,
                        channel,
                        risk_type,
                        complaint_handle_state,
                        payer_phone,
                        remarks_msg,
                        risk_reason,
                        risk_time,
                        end_time,
                        insert_time,
                        risk_context,
                        risk_images
                    )
                    values (
                        #{riskMerchantVO.shopId},
                        #{riskMerchantVO.orderId},
                        #{riskMerchantVO.channel},
                        #{riskMerchantVO.riskType},
                        #{riskMerchantVO.complaintHandleState},
                        #{riskMerchantVO.payerPhone},
                        #{riskMerchantVO.remarksMsg},
                        #{riskMerchantVO.riskReason},
                        #{riskMerchantVO.riskTime},
                        #{riskMerchantVO.endTime},
                        #{riskMerchantVO.insertTime},
                        #{riskMerchantVO.riskContext},
                        #{riskMerchantVO.riskImages}
                   )
    </insert>

    <!-- 更新回复内容和图片存储路径 -->
    <update id="updateRiskContextImages">
        update risk_merchant set risk_context = #{context},risk_images = #{images}
                             where shop_id = #{shopId} and channel = #{channel} and order_id = #{orderId}
    </update>

    <!-- 风险管理——订单投诉管理——分页查询 -->
    <select id="getRiskMerchantPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.riskmerchant.vo.SearchRiskMerchantRespVo">
        SELECT a.risk_id, a.shop_id, e.trueid, e.shop_name, a.order_id, a.risk_type, a.payer_phone, a.channel risk_channel, a.risk_reason,
        (CASE WHEN a.risk_time = '0000-00-00 00:00:00' THEN '' ELSE a.risk_time END) risk_time,cha.channel_abbreviation risk_channel_str,
        a.complaint_handle_state, e.ywy_openid, e.accept_id, b.company agent_name, c.company partner_name,sdd.label complaint_handle_state_str,
        d.company branch_name, a.remarks_msg, a.risk_images, a.risk_context,mc.channel_mch_id channel_mch_id
        FROM risk_merchant a
        JOIN merchant e ON a.shop_id = e.shop_id
        JOIN agent b ON e.agent_id = b.agent_id
        JOIN partner_channel c ON b.partner_id = c.partner_id
        LEFT JOIN branch_office d ON c.branch_office_id = d.id
        LEFT JOIN merchant_channel mc on mc.shop_id = a.shop_id and mc.channel = a.channel
        Left join channel cha ON a.channel = cha.channel_value
        left join system_dict_data sdd on a.complaint_handle_state = sdd.value and sdd.dict_type = 'complaint_handle_state'
        <where>
            <if test="riskMerchantReqVo.shopId != null and riskMerchantReqVo.shopId !=''">
                and a.shop_id = #{riskMerchantReqVo.shopId}
            </if>
            <if test="riskMerchantReqVo.orderId != null and riskMerchantReqVo.orderId != ''">
                and a.order_id = #{riskMerchantReqVo.orderId}
            </if>
            <if test="riskMerchantReqVo.riskType != null and riskMerchantReqVo.riskType != ''">
                and a.risk_type = #{riskMerchantReqVo.riskType}
            </if>
            <if test="riskMerchantReqVo.complaintHandleState != null and riskMerchantReqVo.complaintHandleState != ''">
                and a.complaint_handle_state = #{riskMerchantReqVo.complaintHandleState}
            </if>
            <if test="riskMerchantReqVo.start != null and riskMerchantReqVo.start != ''">
                and a.risk_time <![CDATA[  >=  ]]> #{riskMerchantReqVo.start}
            </if>
            <if test="riskMerchantReqVo.end != null and riskMerchantReqVo.end != ''">
                and a.risk_time <![CDATA[  <=  ]]> #{riskMerchantReqVo.end}
            </if>
            <if test="riskMerchantReqVo.agentId != null and riskMerchantReqVo.agentId != ''">
                and b.agent_id = #{riskMerchantReqVo.agentId}
            </if>
            <if test="riskMerchantReqVo.acceptId != null and riskMerchantReqVo.acceptId != ''">
                and e.accept_id = #{riskMerchantReqVo.acceptId}
            </if>
            <if test="riskMerchantReqVo.shopName != null and riskMerchantReqVo.shopName != ''">
                and e.shop_name like concat('%',#{riskMerchantReqVo.shopName},'%')
            </if>
            <if test="riskMerchantReqVo.branchId != null and riskMerchantReqVo.branchId != ''">
                and d.id = #{riskMerchantReqVo.branchId}
            </if>
            <if test="riskMerchantReqVo.partnerId != null and riskMerchantReqVo.partnerId != ''">
                and c.partner_id = #{riskMerchantReqVo.partnerId}
            </if>
        </where>
        order by a.insert_time desc
    </select>
</mapper>
