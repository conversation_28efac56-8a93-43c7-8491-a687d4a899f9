<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.scenetemple.SceneTempleMapper">

    <!-- 更新寺庙场景项目 -->
    <update id="updateTemple">
        update scene_temple
        <set>
            <if test="updateTempleReqVo.alms != null and updateTempleReqVo.alms != ''">
                alms = #{updateTempleReqVo.alms},
            </if>
            <if test="updateTempleReqVo.status != null">
                status = #{updateTempleReqVo.status},
            </if>
            <if test="updateTempleReqVo.money != null and updateTempleReqVo.money != ''">
                money = #{updateTempleReqVo.money}
            </if>
        </set>
        where shop_id = #{updateTempleReqVo.shopId} and id = #{updateTempleReqVo.id}
    </update>


    <!-- 寺庙场景分页 -->
    <select id="getTemplePage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo.SceneTempleVO">
        select id,shop_id,alms,status,insert_time,money
        from scene_temple
        <where>
            <if test="pageReqVO.shopId != null and pageReqVO.shopId != ''">
                and shop_id = #{pageReqVO.shopId}
            </if>
            <if test="pageReqVO.alms != null and pageReqVO.alms != ''">
                and alms like concat('%',#{pageReqVO.alms},'%')
            </if>
        </where>
        order by id desc
    </select>

    <!-- 行善详情——获得详情分页 -->
    <select id="getTempleOrdersPage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo.TempleOrdersPageVo">
        select u.name,u.phone,o.total_normal,o.shoppro_content,o.time_end,o.goods_content,o.order_id
        from orders o
        right join `user` u
        on o.openid = u.openid
        <where>
            <if test="templeOrdersPage.shopId != null and templeOrdersPage.shopId != ''">
                and o.shop_id = #{templeOrdersPage.shopId}
            </if>
            <if test="templeOrdersPage.start != null and templeOrdersPage.start != ''">
                and o.time_end <![CDATA[  >=  ]]> #{templeOrdersPage.start}
            </if>
            <if test="templeOrdersPage.end != null and templeOrdersPage.end != ''">
                and o.time_end <![CDATA[  <=  ]]> #{templeOrdersPage.end}
            </if>
            <if test="templeOrdersPage.alms != null and templeOrdersPage.alms != ''">
                and o.shoppro_content = #{templeOrdersPage.alms}
            </if>
        </where>
        order by o.time_end desc
    </select>

    <!-- 行善详情——统计详情 -->
    <select id="statisticsTempleOrders"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.scenetemple.vo.StatisticsTempleOrders">
        select SUM(o.total_normal) total_normal,COUNT(o.order_id) count
        from orders o
        right join `user` u
        on o.openid = u.openid
        <where>
            <if test="templeOrdersPage.shopId != null and templeOrdersPage.shopId != ''">
                and o.shop_id = #{templeOrdersPage.shopId}
            </if>
            <if test="templeOrdersPage.start != null and templeOrdersPage.start != ''">
                and o.time_end <![CDATA[  >=  ]]> #{templeOrdersPage.start}
            </if>
            <if test="templeOrdersPage.end != null and templeOrdersPage.end != ''">
                and o.time_end <![CDATA[  <=  ]]> #{templeOrdersPage.end}
            </if>
            <if test="templeOrdersPage.alms != null and templeOrdersPage.alms != ''">
                and o.shoppro_content = #{templeOrdersPage.alms}
            </if>
        </where>
        order by o.time_end desc
    </select>

    <!-- 查询该寺庙项目是否已存在 -->
    <select id="selectTempleName"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.scenetemple.SceneTempleDO">
        select id,shop_id,alms,status,insert_time,money
        from scene_temple
        <where>
            <if test="shopId != null and shopId != ''">
                shop_id = #{shopId}
            </if>
            <if test="alms != null and alms != ''">
                and alms = #{alms}
            </if>
            <if test="id != null and id != ''">
                and id != #{id}
            </if>
        </where>
    </select>
</mapper>
