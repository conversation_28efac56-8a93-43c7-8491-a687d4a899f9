<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.shorturl.ShortUrlMapper">

    <!-- 查询收银员分店信息 -->
    <select id="initDevicesCashier"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.devices.vo.InitDevicesRespVo">
        select distinct device,device_id from shorturl
        where shop_id = #{devicesReqVo.shopId} and xmid = #{devicesReqVo.xmid} and status=1 and device is not null and device != ''
    </select>

    <!-- 查询负责人，超管设备信息 -->
    <select id="shortUrlAll"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.InitShortUrlRespVo">
        select DISTINCT (case when short_key_name is null then short_key when short_key_name='' then short_key else short_key_name end) short_key_name,short_key
        from shorturl
        <where>
            shop_id = #{shopId}
            <if test="device != null and device != '' and device != 'all'">
                and device_id = #{device}
            </if>
        </where>
        ORDER BY time desc
    </select>

    <!-- 查询店长设备信息 -->
    <select id="shortUrlStore"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.InitShortUrlRespVo">
        select DISTINCT (case when a.short_key_name is null then a.short_key when a.short_key_name='' then a.short_key else a.short_key_name end) short_key_name,a.short_key
        from shorturl a,person_in_charge b
        <where>
            and a.shop_id = #{shortUrlReqVo.shopId}
            and a.device_id = b.device_id
            and b.xmid = #{shortUrlReqVo.xmid}
            <if test="shortUrlReqVo.device != null and shortUrlReqVo.device != '' and shortUrlReqVo.device != 'all'">
                and a.device_id = #{shortUrlReqVo.device}
            </if>
        </where>
        ORDER BY a.time desc
    </select>

    <!-- 查询收银员设备信息 -->
    <select id="shortUrlCashier"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.InitShortUrlRespVo">
        select DISTINCT (case when short_key_name is null then short_key when short_key_name='' then short_key else short_key_name end) short_key_name,short_key
        from shorturl
        <where>
            and shop_id = #{shortUrlReqVo.shopId}
            and xmid = #{shortUrlReqVo.xmid}
            <if test="shortUrlReqVo.device != null and shortUrlReqVo.device != '' and shortUrlReqVo.device != 'all'">
                and device_id = #{shortUrlReqVo.device}
            </if>
        </where>
        ORDER BY time desc
    </select>

    <!-- 查询设备名称 -->
    <select id="getShortKeyName" resultType="java.lang.String">
        select short_key_name from shorturl where short_key = #{shortKey}
    </select>

    <select id="selectShortUrlPage" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.ShortUrlRespVO">
        select a.short_key,c.device,a.nickname,b.name,a.time,a.trade_id,
               a.print_device_id,a.print_device_status,a.wsy_device_id,
               a.print_brand,a.print_device_number,a.print_order_refund,
               a.audio_horn_id,a.openid,a.register_brand,a.ylb_brand,
               a.lock_cashier,a.xmid,a.device_id,a.short_key_name,
               a.wsy_device_key,a.shop_id,a.is_play_cannel_order,
               a.device_seq,b.nickname as nick_user
            from shorturl a left join devices c on c.id = a.device_id
			left join user b on a.xmid=b.xmid
                <where>
			    <if test="reqVO.shopId != null and reqVO.shopId != ''">
                    a.shop_id = #{reqVO.shopId}
                </if>
                <if test="reqVO.shortKey != null and reqVO.shortKey != ''">
                    and a.short_key =#{reqVO.shortKey}
                </if>
                <if test="reqVO.deviceId != null and reqVO.deviceId != ''">
                    and c.id = #{reqVO.deviceId}
                </if>
                <if test="reqVO.cashRegister != null and reqVO.cashRegister != ''">
                    and b.xmid = #{reqVO.cashRegister}
                </if>
                </where>
        order by a.time desc
    </select>
    <!-- 查询设备绑定的分店 -->
    <select id="getDevice" resultType="java.lang.String">
        select device from shorturl where short_key = #{shortKey}
    </select>

    <!-- 查询分店的收银员xmid -->
    <select id="selectDeviceByXmid" resultType="java.lang.String">
        SELECT distinct xmid FROM shorturl
        <where>
            <if test="device != null and device != ''">
                device = #{device}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and device in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
        </where>
        ORDER BY `time` DESC
    </select>
    <select id="checkWsyDevice" resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.shorturl.ShortUrlDO">
        select * from shorturl
        where short_key != #{shortKey}
            <if test="wsyDeviceId !=null and wsyDeviceId != ''">
                and wsy_device_id=#{wsyDeviceId}
            </if>
    </select>

    <select id="selectShortUrlAgentPage" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.ShortUrlRespVO">
        select a.short_key,d.device,a.nickname,a.time,a.print_device_id,a.print_device_status,a.wsy_device_id,a.print_brand,
		a.print_device_number,a.print_order_refund,a.audio_horn_id,a.shop_id,a.register_brand,a.ylb_brand,a.lock_cashier,
		a.device_id,c.agent_id,a.openid,a.short_key_name,a.wsy_device_key,c.partner_id,a.is_play_cannel_order,a.device_seq,
        b.trueid,b.shop_name,a.trade_id,a.xmid
        from  shorturl a left join merchant b on a.shop_id=b.shop_id left join agent c on b.agent_id = c.agent_id
        left join devices d on a.device_id=d.id
        <where>
            a.shop_id is not null
        <if test="reqVO.agentId != null and reqVO.agentId != ''">
            and c.agent_id=#{reqVO.agentId}
        </if>
        <if test="reqVO.shortKey != null and reqVO.shortKey != ''">
            and a.short_key = #{reqVO.shortKey}
        </if>
        <if test="reqVO.shopId != null and reqVO.shopId != ''">
            and a.shop_id = #{reqVO.shopId}
        </if>
        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'wsy'">
            and a.wsy_device_id = #{reqVO.simpleCodeId}
        </if>
<!--        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'wsy'">-->
<!--            and a.wsy_device_key = #{reqVO.simpleCodeId}-->
<!--        </if>-->
        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'ylb'">
            and a.audio_horn_id = #{reqVO.simpleCodeId}
        </if>
<!--        <if test="reqVO.ydyAgain != null and reqVO.ydyAgain != ''">-->
<!--            and locate(#{reqVO.simpleCodeId},a.audio_horn_id)-->
<!--        </if>-->
        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'ydy'">
            and a.print_device_id = #{reqVO.simpleCodeId}
        </if>
        <if test="reqVO.acceptId != null and reqVO.acceptId != ''">
            and b.accept_id = #{reqVO.acceptId}
        </if>
        <if test="reqVO.partnerId != null and reqVO.partnerId != ''">
            and b.partner_id = #{reqVO.partnerId}
        </if>
        <if test="reqVO.branchId != null and reqVO.branchId != ''">
            and b.branch_id = #{reqVO.branchId}
        </if>
        </where>
        order by a.time desc
    </select>

    <select id="selectShortUrlAgentPageLike" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.ShortUrlRespVO">
        select a.short_key,d.device,a.nickname,a.time,a.print_device_id,a.print_device_status,a.wsy_device_id,a.print_brand,
        a.print_device_number,a.print_order_refund,a.audio_horn_id,a.shop_id,a.register_brand,a.ylb_brand,a.lock_cashier,
        a.device_id,c.agent_id,a.openid,a.short_key_name,a.wsy_device_key,c.partner_id,a.is_play_cannel_order,a.device_seq,
        b.trueid,a.trade_id,a.xmid
        from  shorturl a left join merchant b on a.shop_id=b.shop_id left join agent c on b.agent_id = c.agent_id
        left join devices d on a.device_id=d.id
       <where>
           a.shop_id is not null
        <if test="reqVO.agentId != null and reqVO.agentId != ''">
            and c.agent_id=#{reqVO.agentId}
        </if>
        <if test="reqVO.shortKey != null and reqVO.shortKey != ''">
            and a.short_key = #{reqVO.shortKey}
        </if>
        <if test="reqVO.shopId != null and reqVO.shopId != ''">
            and a.shop_id = #{reqVO.shopId}
        </if>
        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'wsy'">
            and locate(#{reqVO.simpleCodeId},a.wsy_device_id)
        </if>
        <!--        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'wsy'">-->
        <!--            and a.wsy_device_key = #{reqVO.simpleCodeId}-->
        <!--        </if>-->
        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'ylb'">
            and locate(#{reqVO.simpleCodeId},a.audio_horn_id)
        </if>
        <!--        <if test="reqVO.ydyAgain != null and reqVO.ydyAgain != ''">-->
        <!--            and locate(#{reqVO.simpleCodeId},a.audio_horn_id)-->
        <!--        </if>-->
        <if test="reqVO.simpleCodeType != null and reqVO.simpleCodeType == 'ydy'">
            and locate(#{reqVO.simpleCodeId},a.print_device_id)
        </if>
        <if test="reqVO.acceptId != null and reqVO.acceptId != ''">
            and b.accept_id = #{reqVO.acceptId}
        </if>
        <if test="reqVO.partnerId != null and reqVO.partnerId != ''">
            and b.partner_id = #{reqVO.partnerId}
        </if>
        <if test="reqVO.branchId != null and reqVO.branchId != ''">
            and b.branch_id = #{reqVO.branchId}
        </if>
       </where>
        order by a.time desc
    </select>

    <!-- 根据商户号和品牌号获取设备 -->
    <select id="searchHasThisBrandHornMsg"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.shorturl.ShortUrlDO">
        select short_key, shop_id, device, nickname, trade_id, order_id, time, goods_number, total_fee, type, fixed,
        openid, xmid, agent_id, sub_appid, sub_appidse, sub_mid, salesman, rate, rate_quota, page, pp_device_no,
        agent_sub_openid, print_device_id, print_device_status, wsy_device_id, wsy_device_key, switch_time, print_brand,
        scene_type, print_device_number, print_order_refund, audio_horn_id, lock_cashier, register_brand, ylb_brand,
        device_id, short_key_name, audio_horn_token, arf_a8_anyhow, audio_horn_app_type, status, is_play_cannel_order,
        device_seq
        from shorturl
        where shop_id = #{shopId} and ylb_brand in(#{brand},'things','shengt','zht') and audio_horn_id != '' and audio_horn_id is not null
        <if test="derepeat != null and derepeat != '' and derepeat == '1'.toString()">
            group by audio_horn_id
        </if>
    </select>

    <select id="selectByShopIdAndDevices" resultType="string">
        SELECT short_key FROM shorturl WHERE shop_id = #{shopId} AND device_id IN
        <foreach collection="devices" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectInitShortUrl" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.shorturl.vo.InitShortUrlRespVo">
        SELECT DISTINCT
        (CASE WHEN a.short_key_name IS NULL THEN a.short_key
        WHEN a.short_key_name = '' THEN a.short_key
        ELSE a.short_key_name END) AS short_key_name,
        a.short_key
        FROM shorturl a
        <if test="dutyDevicesAndShortUrl.isBoss == 2">
            JOIN person_in_charge b ON a.device_id = b.device_id
        </if>
        <where>
            a.shop_id = #{dutyDevicesAndShortUrl.shopId}
            <if test="dutyDevicesAndShortUrl.isBoss == 2">
                AND b.xmid = #{dutyDevicesAndShortUrl.xmid}
            </if>
            <if test="dutyDevicesAndShortUrl.deviceIds != null and !dutyDevicesAndShortUrl.deviceIds.isEmpty()">
                AND a.device_id IN
                <foreach collection="dutyDevicesAndShortUrl.deviceIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.time DESC
    </select>
</mapper>