<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.statisticsMerchant.StatisticsMerchantMapper">


    <!-- 商户交易查询 -->
    <select id="merchantTransaction"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.MerchantOrderVo">
        <choose>
            <when test="type == 'search'">
                select sm.shop_id,sum(sm.order_count) order_count,sum(sm.money_payable-sm.refund_money) money_payable,m.accept_id,m.agent_id,m.partner_id,sm.channel,count(distinct sm.stat_day) transaction_day,m.shop_keeper,m.phone,m.trueid,m.shop_nickname
                from merchant m
                LEFT JOIN statistics_merchant sm ON m.shop_id = sm.shop_id
            </when>
            <when test="type == 'export'">
                select sm.shop_id,sum(sm.order_count) order_count,sum(sm.money_payable-sm.refund_money) money_payable,m.accept_id,m.agent_id,m.partner_id,sm.channel,count(distinct sm.stat_day) transaction_day,m.shop_keeper,m.phone,m.trueid,m.shop_name,m.shop_nickname,(case when register_time = '0000-00-00 00:00:00' then '' else register_time end) register_time,m.ywy_openid,m.pay_type,c.channel_abbreviation channel_name
                from merchant m
                LEFT JOIN statistics_merchant sm ON m.shop_id = sm.shop_id
                LEFT JOIN channel c ON c.channel_value = sm.channel
            </when>
        </choose>
        <where>
            and sm.partner_id!='parent_channel_6216' and sm.partner_id!='parent_channel_4988'
            <if test="merchantTransactionReqVo.shopNickname != '' and merchantTransactionReqVo.shopNickname != null">
                and sm.shop_name like concat("%",#{merchantTransactionReqVo.shopNickname},"%")
            </if>
            <if test="merchantTransactionReqVo.trueid != '' and merchantTransactionReqVo.trueid != null">
                and sm.shop_id = #{merchantTransactionReqVo.trueid}
            </if>
            <if test="merchantTransactionReqVo.channel != '' and merchantTransactionReqVo.channel != null and merchantTransactionReqVo.channel != 'all'">
                and sm.channel = #{merchantTransactionReqVo.channel}
            </if>
            <if test="merchantTransactionReqVo.agentId != '' and merchantTransactionReqVo.agentId != null and merchantTransactionReqVo.agentId != 'all'">
                and sm.agent_id = #{merchantTransactionReqVo.agentId}
            </if>
            <if test="merchantTransactionReqVo.partnerId != '' and merchantTransactionReqVo.partnerId != null and merchantTransactionReqVo.partnerId != 'all'">
                and sm.partner_id = #{merchantTransactionReqVo.partnerId}
            </if>
            <if test="merchantTransactionReqVo.branchId != '' and merchantTransactionReqVo.branchId != null and merchantTransactionReqVo.branchId != 'all'">
                and sm.branch_id = #{merchantTransactionReqVo.branchId}
            </if>
            <if test="merchantTransactionReqVo.acceptId != '' and merchantTransactionReqVo.acceptId != null and merchantTransactionReqVo.acceptId != 'all'">
                and sm.accept_id = #{merchantTransactionReqVo.acceptId}
            </if>
            <if test=" merchantTransactionReqVo.start != '' and merchantTransactionReqVo.start != null">
                and sm.stat_day <![CDATA[  >=  ]]> #{merchantTransactionReqVo.start}
            </if>
            <if test="merchantTransactionReqVo.end != '' and merchantTransactionReqVo.end != null">
                and sm.stat_day <![CDATA[  <=  ]]> #{merchantTransactionReqVo.end}
            </if>
        </where>
        group by sm.shop_id order by sm.shop_id,sm.stat_day desc
        <if test="type == 'search'">
            limit 300
        </if>
    </select>

    <!-- 统计管理——商户交易统计查询——查询商户交易的信息 -->
    <select id="merchantTransactionMessage"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.MerchantTransactionMessageVo">
        select trueid,
               shop_name,
               shop_nickname,
               shop_keeper,
               phone,
               (case when register_time = '0000-00-00 00:00:00' then '' else register_time end) register_time,
               ywy_openid,
               pay_type
        from merchant
        where shop_id = #{shopId};
    </select>

    <select id="getWeekly"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count) as orders ,sum(money_payable-refund_money) as total
        from statistics_merchant
        where stat_day <![CDATA[ >= ]]> DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) - 0 DAY)
        and stat_day <![CDATA[ <= ]]> DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) - 6 DAY)
        <if test="acceptId != null and acceptId != ''">
            and accept_id = #{acceptId}
        </if>
        <if test="agentId != null and agentId != ''">
            and agent_id = #{agentId}
        </if>
    </select>

    <select id="getDaily"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count) as orders ,sum(money_payable-refund_money) as total
        from statistics_merchant
        where stat_day = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        <if test="acceptId != null and acceptId != ''">
            and accept_id = #{acceptId}
        </if>
        <if test="agentId != null and agentId != ''">
            and agent_id = #{agentId}
        </if>
    </select>

    <select id="getMonthly"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count) as orders ,sum(money_payable-refund_money) as total
        from statistics_merchant
        where stat_day <![CDATA[ >= ]]> DATE_ADD(curdate(),interval -day(curdate())+1 day)
        and stat_day <![CDATA[ <= ]]> last_day(curdate())
        <if test="acceptId != null and acceptId != ''">
            and accept_id = #{acceptId}
        </if>
        <if test="agentId != null and agentId != ''">
            and agent_id = #{agentId}
        </if>
    </select>

    <!-- 搜索框模糊查询商户信息 -->
    <select id="message"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.MerchantMessageRespVo">
        select shop_name,shop_id,shop_keeper,phone,shop_nickname,trueid from merchant
        <where>
            status != 99
            and (shop_name like concat('%',#{merchantMessageReqVo.shopName},'%')
            or shop_nickname like concat('%',#{merchantMessageReqVo.shopName},'%'))
            <if test="merchantMessageReqVo.branchId != '' and merchantMessageReqVo.branchId != null">
                and branch_id = #{merchantMessageReqVo.branchId}
            </if>
            <if test="merchantMessageReqVo.parentId != '' and merchantMessageReqVo.parentId != null">
                and partner_id = #{merchantMessageReqVo.parentId}
            </if>
            <if test="merchantMessageReqVo.agentId != '' and merchantMessageReqVo.agentId != null">
                and agent_id = #{merchantMessageReqVo.agentId}
            </if>
        </where>
    </select>

    <!-- 统计省级换码数量 -->
    <select id="branchYardCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.AddYardCounts">
        select count(shop_id) counts,partner_id as id from merchant
        <where>
            and is_yz_swap=1
            <if test="statisticsYardReqVo.branchId != null and statisticsYardReqVo.branchId != ''">
                and branch_id = #{statisticsYardReqVo.branchId}
            </if>
            <if test="isOneDay == ''">
                and yz_swap_time <![CDATA[ < ]]> DATE_ADD(#{statisticsYardReqVo.startTime}, INTERVAL 1 DAY)
            </if>
            <if test="isOneDay != ''">
                and yz_swap_time <![CDATA[ >= ]]> #{statisticsYardReqVo.startTime}
            </if>
        </where>
        group by partner_id
    </select>

    <!-- 统计市级换码数量 -->
    <select id="partnerYardCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.AddYardCounts">
        select count(shop_id) counts,accept_id as id from merchant
        <where>
            and is_yz_swap=1
            and partner_id = #{statisticsYardReqVo.partnerId}
            <if test="isOneDay == ''">
                and yz_swap_time <![CDATA[ < ]]> DATE_ADD(#{statisticsYardReqVo.startTime}, INTERVAL 1 DAY)
            </if>
            <if test="isOneDay != ''">
                and yz_swap_time <![CDATA[ >= ]]> #{statisticsYardReqVo.startTime}
            </if>
        </where>
        group by agent_id
    </select>

    <!-- 统计区县换码数量 -->
    <select id="agentYardCount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.AddYardCounts">
        select count(shop_id) counts,accept_id as id from merchant
        <where>
            and is_yz_swap = 1
            and agent_id = #{statisticsYardReqVo.agentId}
            <if test="isOneDay == ''">
                and yz_swap_time <![CDATA[ < ]]> DATE_ADD(#{statisticsYardReqVo.startTime}, INTERVAL 1 DAY)
            </if>
            <if test="isOneDay != ''">
                and yz_swap_time <![CDATA[ >= ]]> #{statisticsYardReqVo.startTime}
            </if>
        </where>
        group by accept_id
    </select>

    <select id="agentStatistics"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.StatisticsMonthRespVO">
        select DATE_FORMAT(stat_day,'%Y-%m-%d') as stat_day,sum(order_count) as
        order_total,sum(money_payable-refund_money-commission+refund_return_commission) as money
        from statistics_merchant
        where stat_day <![CDATA[ >= ]]> DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        and stat_day <![CDATA[ < ]]> curdate()
          and branch_id <![CDATA[ < ]]> 99
        <if test="agentId != null and agentId !=''">
            and agent_id=#{agentId}
        </if>
        <if test="acceptId != null and acceptId !=''">
            and accept_id=#{acceptId}
        </if>
        <if test="partnerId != null and partnerId !=''">
            and partner_id=#{partnerId}
        </if>
        <if test=" branchId != null and branchId != ''">
            and branch_id=#{branchId}
        </if>
        <if test="agentSuId != null and agentSuId.size()>0">
            and shop_id in
            <foreach collection="agentSuId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by stat_day
    </select>
    <select id="agentDay"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count) as orders,sum(money_payable-refund_money) as total
        from statistics_merchant
        where stat_day=DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        and branch_id <![CDATA[  <  ]]> 99
        <if test=" agentId != null and agentId != ''">
            and agent_id=#{agentId}
        </if>
        <if test=" acceptId != null and agentId != ''">
            and accept_id = #{acceptId}
        </if>
        <if test="partnerId != null and partnerId !=''">
            and partner_id=#{partnerId}
        </if>
        <if test=" branchId != null and branchId != ''">
            and branch_id=#{branchId}
        </if>
        <if test="agentSuId != null and agentSuId.size()>0">
            and shop_id in
            <foreach collection="agentSuId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="agentWeekly"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count)as orders,sum(money_payable-refund_money)as total
        from statistics_merchant
        where stat_day<![CDATA[  >=  ]]>DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) - 0 DAY)
        and stat_day<![CDATA[  <=  ]]>DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) - 6 DAY)
        and branch_id <![CDATA[  <  ]]> 99
        <if test=" agentId != null and agentId != ''">
            and agent_id=#{agentId}
        </if>
        <if test=" acceptId != null and agentId != ''">
            and accept_id = #{acceptId}
        </if>
        <if test="partnerId != null and partnerId !=''">
            and partner_id=#{partnerId}
        </if>
        <if test=" branchId != null and branchId != ''">
            and branch_id=#{branchId}
        </if>
        <if test="agentSuId != null">
            and shop_id in
            <foreach collection="agentSuId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="agentMonthly"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count)as orders,sum(money_payable-refund_money)as total
        from statistics_merchant
        where stat_day<![CDATA[  >=  ]]>DATE_ADD(curdate(),interval -day(curdate())+1 day)
        and stat_day<![CDATA[  <=  ]]>last_day(curdate())
        and branch_id <![CDATA[  <  ]]> 99
        <if test=" agentId != null and agentId != ''">
            and agent_id=#{agentId}
        </if>
        <if test=" acceptId != null and agentId != ''">
            and accept_id = #{acceptId}
        </if>
        <if test="partnerId != null and partnerId !=''">
            and partner_id=#{partnerId}
        </if>
        <if test=" branchId != null and branchId != ''">
            and branch_id=#{branchId}
        </if>
        <if test="agentSuId != null">
            and shop_id in
            <foreach collection="agentSuId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--  统计管理——商户交易统计查询——统计笔数和金额 -->
    <select id="statisticsTransactionNumber"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.TransactionNumberVo">
        select sum(order_count) order_count,sum(money_payable-refund_money) money_payable
        from statistics_merchant
        <where>
            partner_id!='parent_channel_6216' and partner_id!='parent_channel_4988'
            <if test="merchantTransactionReqVo.shopNickname != '' and merchantTransactionReqVo.shopNickname != null">
                and shop_name like concat("%",#{merchantTransactionReqVo.shopNickname},"%")
            </if>
            <if test="merchantTransactionReqVo.trueid != '' and merchantTransactionReqVo.trueid != null">
                and shop_id = #{merchantTransactionReqVo.trueid}
            </if>
            <if test="merchantTransactionReqVo.channel != '' and merchantTransactionReqVo.channel != null and merchantTransactionReqVo.channel != 'all'">
                and channel = #{merchantTransactionReqVo.channel}
            </if>
            <if test="merchantTransactionReqVo.agentId != '' and merchantTransactionReqVo.agentId != null and merchantTransactionReqVo.agentId != 'all'">
                and agent_id = #{merchantTransactionReqVo.agentId}
            </if>
            <if test="merchantTransactionReqVo.partnerId != '' and merchantTransactionReqVo.partnerId != null and merchantTransactionReqVo.partnerId != 'all'">
                and partner_id = #{merchantTransactionReqVo.partnerId}
            </if>
            <if test="merchantTransactionReqVo.branchId != '' and merchantTransactionReqVo.branchId != null and merchantTransactionReqVo.branchId != 'all'">
                and branch_id = #{merchantTransactionReqVo.branchId}
            </if>
            <if test="merchantTransactionReqVo.acceptId != '' and merchantTransactionReqVo.acceptId != null and merchantTransactionReqVo.acceptId != 'all'">
                and accept_id = #{merchantTransactionReqVo.acceptId}
            </if>
            <if test=" merchantTransactionReqVo.start != '' and merchantTransactionReqVo.start != null">
                and stat_day <![CDATA[  >=  ]]> #{merchantTransactionReqVo.start}
            </if>
            <if test="merchantTransactionReqVo.end != '' and merchantTransactionReqVo.end != null">
                and stat_day <![CDATA[  <=  ]]> #{merchantTransactionReqVo.end}
            </if>
        </where>
    </select>
    <select id="selectStatisticsMerchantDay"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        SELECT DATE_FORMAT(stat_day,'%Y-%m-%d'),sum(order_count) as orders, sum(money_payable - refund_money) as total
        FROM statistics_merchant
        WHERE stat_day =#{date}
        <if test=" agentId != null and agentId != ''">
            AND agent_id=#{agentId}
        </if>
        <if test=" acceptId != null and agentId != ''">
            AND accept_id = #{acceptId}
        </if>
        <if test="partnerId != null and partnerId !=''">
            AND partner_id=#{partnerId}
        </if>
        <if test=" branchId != null and branchId != ''">
            AND branch_id=#{branchId}
        </if>
    </select>

    <select id="selectStatisticsMerchantDaySalesAmount"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        SELECT sum(order_count) as orders, sum(money_payable-refund_money-commission+refund_return_commission) as total
        FROM statistics_merchant
        WHERE stat_day =#{date};
    </select>
</mapper>