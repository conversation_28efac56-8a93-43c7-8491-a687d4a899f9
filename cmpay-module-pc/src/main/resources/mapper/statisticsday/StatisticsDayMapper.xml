<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.statisticsday.StatisticsDayMapper">

    <select id="seletcMerchantDay" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count) as orders,sum(money_paidout) as total
            from statistics_day
                where stat_day=DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        <if test=" shopId != null ">
            and shop_id=#{shopId}
        </if>
<!--        <if test=" xmid != null ">-->
<!--            and cashier = #{xmid}-->
<!--        </if>-->
        <if test="deviceList != null and deviceList.size > 0">
            and device in
            <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                #{item.device}
            </foreach>
        </if>
        <if test="openId!=null and openId!=''">
            and cashier = #{openId}
        </if>
    </select>

    <select id="seletcMerchantWeekly" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count)as orders,sum(money_paidout)as total
            from statistics_day
                where stat_day<![CDATA[  >=  ]]>DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) - 0 DAY)
                  and stat_day<![CDATA[  <=  ]]>DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) - 6 DAY)
        <if test=" shopId != null ">
            and shop_id=#{shopId}
        </if>
<!--        <if test=" xmid != null ">-->
<!--            and cashier = #{xmid}-->
<!--        </if>-->
        <if test="deviceList != null and deviceList.size > 0">
            and device in
            <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                #{item.device}
            </foreach>
        </if>
        <if test="openId!=null and openId!=''">
            and cashier = #{openId}
        </if>
    </select>

    <select id="seletcMerchantMonthly" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsMerchant.vo.HomeStatisticsRespVO">
        select sum(order_count)as orders,sum(money_paidout)as total
            from statistics_day
                where stat_day<![CDATA[  >=  ]]>DATE_ADD(curdate(),interval -day(curdate())+1 day)
                  and stat_day<![CDATA[  <=  ]]>last_day(curdate())
        <if test=" shopId != null ">
            and shop_id=#{shopId}
        </if>
<!--        <if test=" xmid != null ">-->
<!--            and cashier = #{xmid}-->
<!--        </if>-->
        <if test="deviceList != null and deviceList.size > 0">
            and device in
            <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                #{item.device}
            </foreach>
        </if>
        <if test="openId!=null and openId!=''">
            and cashier = #{openId}
        </if>
    </select>

    <!-- 商户交易统计查询：全列模式 -->
    <select id="statisticsDayMapperAll"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.MerchantOrdersDetailVo">
        select device,cashier,order_type,DATE_FORMAT(stat_day,'%Y-%m-%d') stat_day,sum(order_count) order_count,sum(money_payable) money_payable,
        sum(money_refund) money_refund,sum(money_activity) money_activity,sum(commission) commission,sum(money_paidout) money_paidout,sum(money_profit_share) money_profit_share,start_time,end_time,channel
        from statistics_day
        <where>
            and shop_id = #{merchantOrders.shopId}
            and stat_day <![CDATA[  >=  ]]> #{merchantOrders.start}
            and stat_day <![CDATA[  <=  ]]> #{merchantOrders.end}
            <if test="merchantOrders.cashierId != null and merchantOrders.cashierId != '' and merchantOrders.cashierId != 'all'">
                and cashier = #{merchantOrders.cashierId}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and device in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="merchantOrders.device != null and merchantOrders.device != '' and merchantOrders.device != 'all'">
                and device = #{merchantOrders.device}
            </if>
            <if test="merchantOrders.orderType != null and merchantOrders.orderType != '' and merchantOrders.orderType != 'all'">
                and order_type = #{merchantOrders.orderType}
            </if>
        </where>
        group by device,cashier,order_type,stat_day,start_time
        <choose>
            <when test="merchantOrders.refundAmount == 'refund'">
                HAVING money_refund > 0
            </when>
            <when test="merchantOrders.refundAmount == 'noRefund'">
                HAVING money_refund = 0
            </when>
        </choose>
        order by stat_day desc,device,cashier desc
    </select>

    <!-- 商户交易统计查询：分店模式 -->
    <select id="statisticsDayMapperDevice"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.MerchantOrdersDetailVo">
        select device,cashier,CONCAT_WS('至',MIN(DATE_FORMAT(stat_day,'%Y-%m-%d')), MAX(DATE_FORMAT(stat_day,'%Y-%m-%d'))) stat_day,sum(order_count) order_count,
        sum(money_payable) money_payable,sum(money_refund) money_refund,sum(money_activity) money_activity,sum(commission) commission,sum(money_paidout) money_paidout,sum(money_profit_share) money_profit_share
        from statistics_day
        <where>
            and shop_id = #{merchantOrders.shopId}
            and stat_day <![CDATA[  >=  ]]> #{merchantOrders.start}
            and stat_day <![CDATA[  <=  ]]> #{merchantOrders.end}
            <if test="merchantOrders.cashierId != null and merchantOrders.cashierId != '' and merchantOrders.cashierId != 'all'">
                and cashier = #{merchantOrders.cashierId}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and device in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="merchantOrders.device != null and merchantOrders.device != '' and merchantOrders.device != 'all'">
                and device = #{merchantOrders.device}
            </if>
            <if test="merchantOrders.orderType != null and merchantOrders.orderType != '' and merchantOrders.orderType != 'all'">
                and order_type = #{merchantOrders.orderType}
            </if>
        </where>
        group by device
        <choose>
            <when test="merchantOrders.refundAmount == 'refund'">
                HAVING money_refund > 0
            </when>
            <when test="merchantOrders.refundAmount == 'noRefund'">
                HAVING money_refund = 0
            </when>
        </choose>
        order by device
    </select>

    <select id="statisticsMonth" resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.StatisticsMonthRespVO">
        select DATE_FORMAT(stat_day,'%Y-%m-%d') as stat_day,sum(order_count) as order_total,sum(money_paidout) as money
        from statistics_day
        where stat_day <![CDATA[  >=  ]]> DATE_SUB(CURDATE(), INTERVAL 7 DAY) and stat_day <![CDATA[  <  ]]> curdate()
        and shop_id = #{shopId}
        <if test="device != null and device.size > 0">
        and device in
        <foreach collection="device" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        </if>
        <if test=" xmid != null and xmid != ''">
            and cashier = #{xmid}
        </if>
        group by DATE_FORMAT(stat_day,'%Y-%m-%d')
    </select>
    <!-- 商户交易统计结果查询 -->
    <select id="statisticsMerchantTradingOrders"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.statisticsday.vo.MerchantOrdersNumberVo">
        select sum(order_count) order_count,sum(money_payable) money_payable,
               sum(money_refund) money_refund,sum(money_activity) money_activity,sum(commission) commission,sum(money_paidout) money_paidout,sum(money_profit_share) money_profit_share
        from statistics_day
        <where>
            and shop_id = #{merchantOrders.shopId}
            and stat_day <![CDATA[  >=  ]]> #{merchantOrders.start}
            and stat_day <![CDATA[  <=  ]]> #{merchantOrders.end}
            <if test="merchantOrders.cashierId != null and merchantOrders.cashierId != '' and merchantOrders.cashierId != 'all'">
                and cashier = #{merchantOrders.cashierId}
            </if>
            <if test="deviceList != null and deviceList.size > 0">
                and device in
                <foreach collection="deviceList" item="item" index="index" open="(" separator="," close=")">
                    #{item.device}
                </foreach>
            </if>
            <if test="merchantOrders.device != null and merchantOrders.device != '' and merchantOrders.device != 'all'">
                and device = #{merchantOrders.device}
            </if>
            <if test="merchantOrders.orderType != null and merchantOrders.orderType != '' and merchantOrders.orderType != 'all'">
                and order_type = #{merchantOrders.orderType}
            </if>
        </where>
    </select>
</mapper>
