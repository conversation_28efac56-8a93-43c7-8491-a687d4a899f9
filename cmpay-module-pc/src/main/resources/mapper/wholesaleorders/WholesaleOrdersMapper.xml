<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.wholesaleorders.WholesaleOrdersMapper">


    <update id="updateWholesaleStatus">
        update wholesale.orders set status = #{wholesaleStatusReqVo.status}  where order_id = #{wholesaleStatusReqVo.orderId}
    </update>


    <!-- 批发订单分页 -->
    <select id="searchWholesaleOrders"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.wholesaleorders.vo.WholesaleOrderPage">
        select order_id,buyer_name,buyer_phone,goods_msg,total_money,status,insert_time,(case when end_time='0000-00-00 00:00:00' then '' else end_time end ) end_time
        from wholesale.orders
        <where>
            <if test="wholesaleOrder.shopId != null and wholesaleOrder.shopId != ''">
                and shop_id = #{wholesaleOrder.shopId}
            </if>
            <if test="wholesaleOrder.orderId != null and wholesaleOrder.orderId != ''">
                and order_id = #{wholesaleOrder.orderId}
            </if>
            <if test="wholesaleOrder.scene != null and wholesaleOrder.scene != ''">
                and scene = #{wholesaleOrder.scene}
            </if>
            <if test="wholesaleOrder.phone != null and wholesaleOrder.phone != ''">
                and buyer_phone = #{wholesaleOrder.phone}
            </if>
            <if test="wholesaleOrder.start != null and wholesaleOrder.start != ''">
                and insert_time <![CDATA[  >=  ]]> #{wholesaleOrder.start}
            </if>
            <if test="wholesaleOrder.end != null and wholesaleOrder.end != ''">
                and insert_time <![CDATA[  <=  ]]> #{wholesaleOrder.end}
            </if>
        </where>
        order by insert_time desc
    </select>
</mapper>
