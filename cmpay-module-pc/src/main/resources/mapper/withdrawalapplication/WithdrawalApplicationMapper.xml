<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.withdrawalapplication.WithdrawalApplicationMapper">

    <!-- 根据商户号与通道查询商户是否存在 -->
    <select id="findByShopIdAndChannel"
            resultType="com.cmpay.code.cmpaymodulepc.dal.dataobject.withdrawalapplication.WithdrawalApplicationDO">
        SELECT id, shop_id, channel, is_apply, is_activate, update_time, insert_time
        FROM withdrawal_application
        WHERE shop_id = #{shopId} AND channel = #{channel}
    </select>
</mapper>
