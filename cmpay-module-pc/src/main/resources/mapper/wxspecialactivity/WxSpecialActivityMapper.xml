<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.cmpaymodulepc.dal.mysql.wxspecialactivity.WxSpecialActivityMapper">

    <select id="queryWxSpecialActivityList"
            resultType="com.cmpay.code.cmpaymodulepc.controller.admin.wxspecialactivity.vo.WxSpecialActivityMerchantRespVO">
        SELECT
            m.trueid,
            rm.mcc_type,
            m.shop_id,
            m.shop_nickname,
            m.partner_id,
            m.agent_id,
            m.accept_id,
            wx.apply_status,
            wx.start_time,
            wx.end_time,
            mc.channel as pay_type,
            m.rate,
            rm.threshold_rate as rateRuiyinxin,
            m.register_time,
            wx.account,
            wx.error_msg
        FROM ryx_mcc rm
                 JOIN merchant_extend me ON rm.ryx_mcc = me.mcc_code
                 JOIN merchant m ON me.shop_id = m.shop_id
                JOIN merchant_channel mc ON m.shop_id = mc.shop_id AND mc.`status` = 1 AND channel = 'guotong'
                LEFT JOIN partner_channel c ON m.partner_id = c.partner_id
                 LEFT JOIN wx_special_activity wx ON wx.shop_id = m.shop_id
        <where>
           <if test="activityMerchantReqVO.mccType==null or activityMerchantReqVO.mccType==''">
               AND rm.mcc_type IN (1, 4, 5)
           </if>
            <if test="activityMerchantReqVO.mccType!=null and activityMerchantReqVO.mccType!=''">
                AND rm.mcc_type = #{activityMerchantReqVO.mccType}
            </if>
            <if test="activityMerchantReqVO.partnerId!=null and activityMerchantReqVO.partnerId!=''">
                AND m.partner_id = #{activityMerchantReqVO.partnerId}
            </if>
            <if test="activityMerchantReqVO.agentId!=null and activityMerchantReqVO.agentId!=''">
                AND m.agent_id = #{activityMerchantReqVO.agentId}
            </if>
            <if test="activityMerchantReqVO.acceptId!=null and activityMerchantReqVO.acceptId!=''">
                AND m.accept_id = #{activityMerchantReqVO.acceptId}
            </if>
            <if test="activityMerchantReqVO.branchId!=null and activityMerchantReqVO.branchId!=''">
                AND m.branch_id = #{activityMerchantReqVO.branchId}
            </if>
            <if test="activityMerchantReqVO.shopId!=null and activityMerchantReqVO.shopId!=''">
                AND m.shop_id = #{activityMerchantReqVO.shopId}
            </if>
            <if test="activityMerchantReqVO.applyStatus!=null and activityMerchantReqVO.applyStatus!=''">
                <choose>
                    <when test="activityMerchantReqVO.applyStatus=='03'">
                        AND (wx.apply_status = '03' OR wx.apply_status IS NULL)
                    </when>
                    <otherwise>
                        AND wx.apply_status = #{activityMerchantReqVO.applyStatus}
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY m.trueid,rm.mcc_type
        order by m.register_time desc
    </select>
</mapper>
