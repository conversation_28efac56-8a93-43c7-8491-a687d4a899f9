package com.cmpay.code.module.system.controller.admin.aochuangadmin;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import com.cmpay.code.module.system.controller.admin.aochuangadmin.vo.*;
import com.cmpay.code.module.system.convert.aochuangadmin.AochuangAdminConvert;
import com.cmpay.code.module.system.dal.dataobject.aochuangadmin.AochuangAdminDO;
import com.cmpay.code.module.system.service.aochuangadmin.AochuangAdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 省市超管用户")
@RestController
@RequestMapping("/aochuangadmin/aochuang-admin")
@Validated
public class AochuangAdminController {

    @Resource
    private AochuangAdminService aochuangAdminService;


    @PutMapping("/update")
    @Operation(summary = "更新渠道用户")
//    @PreAuthorize("@ss.hasPermission('aochuangadmin:aochuang-admin:update')")
    public CommonResult<Boolean> updateAochuangAdmin(@Valid @RequestBody AochuangAdminUpdateReqVO updateReqVO) {
        aochuangAdminService.updateAochuangAdmin(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除省市超管用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('aochuangadmin:aochuang-admin:delete')")
    public CommonResult<Boolean> deleteAochuangAdmin(@RequestParam("id") String id) {
        aochuangAdminService.deleteAochuangAdmin(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得省市超管用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('aochuangadmin:aochuang-admin:query')")
    public CommonResult<AochuangAdminRespVO> getAochuangAdmin(@RequestParam("id") String id) {
        AochuangAdminDO aochuangAdmin = aochuangAdminService.getAochuangAdmin(id);
        return success(AochuangAdminConvert.INSTANCE.convert(aochuangAdmin));
    }

    @GetMapping("/list")
    @Operation(summary = "获得省市超管用户列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('aochuangadmin:aochuang-admin:query')")
    public CommonResult<List<AochuangAdminRespVO>> getAochuangAdminList(@RequestParam("ids") Collection<String> ids) {
        List<AochuangAdminDO> list = aochuangAdminService.getAochuangAdminList(ids);
        return success(AochuangAdminConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得省市超管用户分页")
    @PreAuthorize("@ss.hasPermission('aochuangadmin:aochuang-admin:query')")
    public CommonResult<PageResult<AochuangAdminRespVO>> getAochuangAdminPage(@Valid InternalUserPageReqVO pageVO) {
        PageResult<AochuangAdminDO> pageResult = aochuangAdminService.getAochuangAdminPage(pageVO);
        return success(AochuangAdminConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出省市超管用户 Excel")
    @PreAuthorize("@ss.hasPermission('aochuangadmin:aochuang-admin:export')")
    @OperateLog(type = EXPORT)
    public void exportAochuangAdminExcel(@Valid AochuangAdminExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AochuangAdminDO> list = aochuangAdminService.getAochuangAdminList(exportReqVO);
        // 导出 Excel
        List<AochuangAdminExcelVO> datas = AochuangAdminConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "省市超管用户.xls", "数据", AochuangAdminExcelVO.class, datas);
    }

}
