package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 省市超管用户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AochuangAdminBaseVO {

    @Schema(description = "密码", required = true)
    @NotNull(message = "密码不能为空")
    private String password;

    @Schema(description = "adminright", required = true)
    @NotNull(message = "adminright不能为空")
    private String adminright;

    @Schema(description = "用户名", required = true, example = "张三")
    @NotNull(message = "用户名不能为空")
    private String username;

    @Schema(description = "渠道id", example = "25992")
    private String partnerId;

    @Schema(description = "分公司id", example = "17773")
    private String branchId;

    @Schema(description = "创建时间", required = true)
    @NotNull(message = "创建时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date time;

}
