package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 省市超管用户创建 Request VO")
@Data
@ToString(callSuper = true)
public class AochuangAdminCreateReqVO {

    @Schema(description = "账号", example = "25992")
    private String account;

    @Schema(description = "用户名", example = "张三")
    private String username;

    @Schema(description = "角色id", example = "2048")
    private Long roleId;

    @Schema(description = "渠道id", example = "25992")
    private String partnerId;

}
