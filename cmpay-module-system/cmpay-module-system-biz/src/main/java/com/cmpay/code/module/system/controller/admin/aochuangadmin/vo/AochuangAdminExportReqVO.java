package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 省市超管用户 Excel 导出 Request VO，参数和 AochuangAdminPageReqVO 是一致的")
@Data
public class AochuangAdminExportReqVO {

    @Schema(description = "密码")
    private String password;

    @Schema(description = "adminright")
    private String adminright;

    @Schema(description = "用户名", example = "张三")
    private String username;

    @Schema(description = "渠道id", example = "25992")
    private String partnerId;

    @Schema(description = "分公司id", example = "17773")
    private String branchId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] time;

}
