package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 省市超管用户更新 Request VO")
@Data
@ToString(callSuper = true)
public class AochuangAdminUpdateReqVO {

    @Schema(description = "用户id",required = true,example = "2048")
    @NotNull(message = "用户id不能为空")
    private String userId;
    @Schema(description = "账号",required = true,example = "admin")
    @NotNull(message = "账号不能为空")
    private String account;
    @Schema(description = "权限", required = true, example = "25098")
    @NotNull(message = "权限不能为空")
    private String roleId;
    @Schema(description = "渠道", required = true, example = "25098")
    @NotNull(message = "渠道不能为空")
    private String partnerId;

}
