package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-08-21 17:40:48
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InternalCreateUser {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "账号")
    private String account;

    @Schema(description = "归属渠道id")
    private String companyId;

    @Schema(description = "归属渠道名称")
    private String companyName;

    @Schema(description = "角色id")
    private List<Long> roleId;

    @Schema(description = "是否更新用户名：1-更新，0-不更新")
    private String isUpdate;

    @Schema(description = "平台")
    private Integer platform2;
}
