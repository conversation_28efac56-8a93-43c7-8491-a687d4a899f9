package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-08-21 15:08:06
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InternalUserDetailsResp {

    @Schema(description = "角色id")
    private String roleIds;

    @Schema(description = "手机号")
    private String account;

    @Schema(description = "名称")
    private String username;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "归属渠道id")
    private String platformId;

    @Schema(description = "归属渠道名称")
    private String companyName;

    @Schema(description = "用户角色id")
    private String userRoleIds;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "安全手机号")
    private String phone;
}
