package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 省市超管用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InternalUserPageReqVO extends PageParam {

    @Schema(description = "账号")
    private String username;

    @Schema(description = "用户名")
    private String nickname;

    @Schema(description = "权限")
    private String roleName;

    @Schema(description = "归属")
    private List<String> orangeIds;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "归属平台")
    private String platformId;

    @Schema(description = "归属机构")
    private String companyId;
}
