package com.cmpay.code.module.system.controller.admin.aochuangadmin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "管理后台 - 渠道用户列表 Response VO")
@Data
public class InternalUserRespVO {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "账号")
    private String username;

    @Schema(description = "用户名称")
    private String nickname;

    @Schema(description = "角色名称")
    private String roleNames;

    @Schema(description = "所属渠道id")
    private String companyId;

    @Schema(description = "所属渠道")
    private String companyName;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "平台")
    private String platform2;

    @Schema(description = "安全手机")
    private String phone;
}
