package com.cmpay.code.module.system.controller.admin.assistantuser;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.controller.admin.assistantuser.vo.AssistantUserCreateReqVO;
import com.cmpay.code.module.system.controller.admin.assistantuser.vo.AssistantUserUpdateReqVO;
import com.cmpay.code.module.system.service.assistantuser.AssistantUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 邮付小助手商户用户")
@RestController
@RequestMapping("/assistantuser/assistant-user")
@Validated
public class AssistantUserController {

    @Resource
    private AssistantUserService assistantUserService;

    @PostMapping("/create")
    @Operation(summary = "创建邮付小助手商户用户")
    @PreAuthorize("@ss.hasPermission('assistantuser:assistant-user:create')")
    public CommonResult<String> createAssistantUser(@Valid @RequestBody AssistantUserCreateReqVO createReqVO) {
        return success(assistantUserService.createAssistantUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新邮付小助手商户用户")
    @PreAuthorize("@ss.hasPermission('assistantuser:assistant-user:update')")
    public CommonResult<Boolean> updateAssistantUser(@Valid @RequestBody AssistantUserUpdateReqVO updateReqVO) {
        assistantUserService.updateAssistantUser(updateReqVO);
        return success(true);
    }



}
