package com.cmpay.code.module.system.controller.admin.assistantuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 邮付小助手商户用户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AssistantUserBaseVO {

    @Schema(description = "xmid", required = true, example = "31182")
    private String xmid;

    @Schema(description = "openid", required = true, example = "31182")
    private String openid;

    @Schema(description = "name", example = "王五")
    private String name;

    @Schema(description = "phone")
    private String phone;

    @Schema(description = "birthday")
    private String birthday;

    @Schema(description = "company")
    private String company;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "工作")
    private String work;

    @Schema(description = "identity")
    private String identity;

    @Schema(description = "school")
    private String school;

    @Schema(description = "first_time", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime firstTime;

    @Schema(description = "nickname", example = " ")
    private String nickname;

    @Schema(description = "sex")
    private Integer sex;

    @Schema(description = "headimgurl", example = "https://www.iocoder.cn")
    private String headimgurl;

    @Schema(description = "country")
    private String country;

    @Schema(description = "province")
    private String province;

    @Schema(description = "city")
    private String city;

    @Schema(description = "unionid", example = "16608")
    private String unionid;

    @Schema(description = "subscribe")
    private Integer subscribe;

    @Schema(description = "type", required = true, example = "1")
    @NotNull(message = "type不能为空")
    private String type;

    @Schema(description = "loginid", example = "30462")
    private String loginid;

    @Schema(description = "密码")
    private String password;

}
