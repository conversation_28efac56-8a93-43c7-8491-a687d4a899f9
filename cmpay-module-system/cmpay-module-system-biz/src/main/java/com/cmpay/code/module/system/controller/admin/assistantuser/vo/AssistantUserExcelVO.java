package com.cmpay.code.module.system.controller.admin.assistantuser.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邮付小助手商户用户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AssistantUserExcelVO {

    @ExcelProperty("主键id")
    private String xmid;

    @ExcelProperty("openid")
    private String openid;

    @ExcelProperty("name")
    private String name;

    @ExcelProperty("phone")
    private String phone;

    @ExcelProperty("birthday")
    private String birthday;

    @ExcelProperty("company")
    private String company;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("工作")
    private String work;

    @ExcelProperty("identity")
    private String identity;

    @ExcelProperty("school")
    private String school;

    @ExcelProperty("first_time")
    private LocalDateTime firstTime;

    @ExcelProperty("nickname")
    private String nickname;

    @ExcelProperty("sex")
    private Integer sex;

    @ExcelProperty("headimgurl")
    private String headimgurl;

    @ExcelProperty("country")
    private String country;

    @ExcelProperty("province")
    private String province;

    @ExcelProperty("city")
    private String city;

    @ExcelProperty("unionid")
    private String unionid;

    @ExcelProperty("subscribe")
    private Integer subscribe;

    @ExcelProperty("type")
    private String type;

    @ExcelProperty("loginid")
    private String loginid;

    @ExcelProperty("密码")
    private String password;

}
