package com.cmpay.code.module.system.controller.admin.assistantuser.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 邮付小助手商户用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssistantUserPageReqVO extends PageParam {

    @Schema(description = "openid", example = "31182")
    private String openid;

    @Schema(description = "name", example = "王五")
    private String name;

    @Schema(description = "phone")
    private String phone;

    @Schema(description = "birthday")
    private String birthday;

    @Schema(description = "company")
    private String company;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "工作")
    private String work;

    @Schema(description = "identity")
    private String identity;

    @Schema(description = "school")
    private String school;

    @Schema(description = "first_time")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] firstTime;

    @Schema(description = "nickname", example = " ")
    private String nickname;

    @Schema(description = "sex")
    private Integer sex;

    @Schema(description = "headimgurl", example = "https://www.iocoder.cn")
    private String headimgurl;

    @Schema(description = "country")
    private String country;

    @Schema(description = "province")
    private String province;

    @Schema(description = "city")
    private String city;

    @Schema(description = "unionid", example = "16608")
    private String unionid;

    @Schema(description = "subscribe")
    private Integer subscribe;

    @Schema(description = "type", example = "1")
    private String type;

    @Schema(description = "loginid", example = "30462")
    private String loginid;

    @Schema(description = "密码")
    private String password;

}
