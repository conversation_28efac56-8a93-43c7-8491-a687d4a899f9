package com.cmpay.code.module.system.controller.admin.assistantuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 邮付小助手商户用户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssistantUserRespVO extends AssistantUserBaseVO {

    @Schema(description = "主键id", required = true, example = "22177")
    private String xmid;

}
