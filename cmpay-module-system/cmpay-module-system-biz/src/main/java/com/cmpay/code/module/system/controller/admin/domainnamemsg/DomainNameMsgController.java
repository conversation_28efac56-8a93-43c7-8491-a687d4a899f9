package com.cmpay.code.module.system.controller.admin.domainnamemsg;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.controller.admin.domainnamemsg.vo.DomainNameMsgRespVO;
import com.cmpay.code.module.system.service.domainnamemsg.DomainNameMsgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 系统标识信息")
@RestController
@RequestMapping("/domain-name-msg")
@Validated
public class DomainNameMsgController {

    @Resource
    private DomainNameMsgService domainNameMsgService;

    @GetMapping("/get-by-domainname")
    @Operation(summary = "查询系统标识信息")
    @PermitAll
    public CommonResult<DomainNameMsgRespVO> getByDomainName(String domainName){
            return success(domainNameMsgService.getByDomainName(domainName));
    }
}
