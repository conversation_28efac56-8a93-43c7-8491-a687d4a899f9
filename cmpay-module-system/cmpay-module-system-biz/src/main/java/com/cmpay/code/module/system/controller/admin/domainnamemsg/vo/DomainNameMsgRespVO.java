package com.cmpay.code.module.system.controller.admin.domainnamemsg.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统标识信息 - response - VO
 */
@Data
public class DomainNameMsgRespVO {

    @Schema(description = "id")
    private Long id;
    @Schema(description = "品牌名称")
    private String brandName;
    @Schema(description = "品牌地址")
    private String brandUrl;
    @Schema(description = "品牌logo")
    private String brandLogo;
    @Schema(description = "域名截取唯一字符")
    private String domainName;
    @Schema(description = "网页标题logo")
    private String icoHref;
    @Schema(description = "机构或渠道")
    private String adminName;
    @Schema(description = "手机号公钥")
    private String phonePublicKey;
}
