package com.cmpay.code.module.system.controller.admin.init;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.service.init.InitConstantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

/**
 * author suohongbo
 * date 2023/4/25 15:45
 * version 1.0
 */
@Tag(name = "初始化")
@RestController
@RequestMapping("/system/init")
@Validated
@AllArgsConstructor
@Slf4j
public class InitConstantController {

    /**
     * 构造方法注入
     */
    private final InitConstantService initConstantService;


    /**
     * 初始化常量接口，如果涉及到常量信息修改，则需要访问此接口进行刷新
     */
    @RequestMapping(value = "/constant", method = RequestMethod.GET)
    @Operation(summary = "初始化常量信息")
    @PermitAll
    public CommonResult<Object> initConstant() {
        initConstantService.initProjectMap();
        initConstantService.initConstantMap();
        return success("初始化成功");
    }
}
