package com.cmpay.code.module.system.controller.admin.minipcloginuser;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * pc上一次登录状态表请求控制器
 * Created by 创建人 on 2023-08-25 11:24:58.
 */
@Tag(name = "管理后台 - 上一次登录记录")
@RestController
@Validated
@RequestMapping(value = "/miniPcLoginUser/")
public class MiniPcLoginUserController {


}