package com.cmpay.code.module.system.controller.admin.minipcloginuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MinPcLoginUserRespVO {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "角色id")
    private Long roleId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台")
    private Integer platform;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "名称")
    private String nickname;

    @Schema(description = "创建时间")
    private String insertTime;
}
