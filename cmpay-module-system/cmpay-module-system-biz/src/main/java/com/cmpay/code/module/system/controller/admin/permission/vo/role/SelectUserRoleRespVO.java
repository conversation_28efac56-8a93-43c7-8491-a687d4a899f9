package com.cmpay.code.module.system.controller.admin.permission.vo.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-08-22 10:33:43
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SelectUserRoleRespVO {

    @Schema(description = "角色id")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;
}
