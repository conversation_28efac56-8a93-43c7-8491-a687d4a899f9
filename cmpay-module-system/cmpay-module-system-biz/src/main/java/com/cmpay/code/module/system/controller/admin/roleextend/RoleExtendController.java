package com.cmpay.code.module.system.controller.admin.roleextend;


import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.module.system.controller.admin.roleextend.vo.RoleExtendCreateReqVO;
import com.cmpay.code.module.system.controller.admin.roleextend.vo.RoleExtendDeleteReqVO;
import com.cmpay.code.module.system.controller.admin.roleextend.vo.RoleExtendPageReqVO;
import com.cmpay.code.module.system.controller.admin.roleextend.vo.RoleExtendUpdateReqVO;
import com.cmpay.code.module.system.service.roleextend.SystemRoleExtendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Set;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 角色扩展")
@RestController
@RequestMapping("/system/role-extend")
@Validated
public class RoleExtendController {

    @Resource
    private SystemRoleExtendService roleExtendService;

    @PostMapping("/create")
    @Operation(summary = "添加角色扩展")
    public CommonResult create(@RequestBody RoleExtendCreateReqVO reqVO){
        roleExtendService.addRoleExtend(reqVO);
        return success("添加成功",null);
    }
    @GetMapping("/page")
    @Operation(summary = "角色扩展列表")
    public CommonResult<PageResult> selectRoleExtendPage(RoleExtendPageReqVO reqVO){
        return success(roleExtendService.selectRoleExtendPage(reqVO));
    }

    @GetMapping("/get-org-menu")
    @Operation(summary = "获得机构拥有的菜单扩展集合")
    public CommonResult<Set<Long>> getRoleMenuIds(@RequestParam String orgId,Long roleId){
        return success(roleExtendService.getRoleMenuIds(orgId,roleId));
    }
    @PostMapping("/update-org-menu")
    @Operation(summary = "修改机构菜单")
    public CommonResult<Boolean> updateOrgMenu(@RequestBody RoleExtendUpdateReqVO reqVO){
        roleExtendService.updateOrgMenu(reqVO.getOrgId(),reqVO.getMenuList(),reqVO.getRoleId());
        return success(true);
    }

    @PostMapping("/delete-by-orgid")
    @Operation(summary = "删除角色扩展")
    public CommonResult<Boolean> deleteByOrgId(@RequestBody RoleExtendDeleteReqVO reqVO){
        roleExtendService.deleteByOrgId(reqVO.getOrgId(),reqVO.getRoleId());
        return success(true);
    }
}
