package com.cmpay.code.module.system.controller.admin.roleextend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RoleExtendRespVO {

    @Schema(description = "id")
    private Long id;
    @Schema(description = "角色id")
    private Long roleId;

    @Schema(description = "机构号")
    private String orgId;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "创建时间")
    private String insertTime;
}
