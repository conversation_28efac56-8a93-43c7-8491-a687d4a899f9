package com.cmpay.code.module.system.controller.admin.sysaccept;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitAcceptReqVo;
import com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo;
import com.cmpay.code.module.system.service.sysaccept.SysAcceptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Tag(name = "管理后台 - 网点用户")
@RestController
@RequestMapping("/accept/")
@Validated
public class SysAcceptController {

    @Resource
    private SysAcceptService sysAcceptService;


    /**
     * 初始化区县
     * @param initAcceptReqVo 省id  市id  区县id
     * @return
     */
    @PostMapping("/initaccept")
    @Operation(summary = "初始化区县")
    public CommonResult<List<InitMenuListRespVo>> initAccept(@RequestBody(required = false) InitAcceptReqVo initAcceptReqVo) {
        return sysAcceptService.initAccept(initAcceptReqVo);
    }


}
