package com.cmpay.code.module.system.controller.admin.sysaccept.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-04-06 16:00:19
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitAcceptReqVo {

    @Schema(description = "省id")
    private String branchId;

    @Schema(description = "市id")
    private String partnerId;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "名称")
    private String company;

    @Schema(description = "是否报表：1-是；0-否；2-是否收支结算汇总表省外")
    private String isReport;

    @Schema(description = "除报表外不需要传；是否广东省：1-是；0-否")
    private String isGd;

    @Schema(description = "区县集合")
    private List<String> agentIds;
}

