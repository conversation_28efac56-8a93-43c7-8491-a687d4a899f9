package com.cmpay.code.module.system.controller.admin.sysagent;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo;
import com.cmpay.code.module.system.controller.admin.sysagent.vo.InitAgentReqVo;
import com.cmpay.code.module.system.service.sysagent.SysAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Tag(name = "管理后台 - 区县分公司")
@RestController
@RequestMapping("/agent/")
@Validated
public class SysAgentController {

    @Resource
    private SysAgentService sysAgentService;


    @PostMapping("/initagent")
    @Operation(summary = "初始化区县")
    public CommonResult<List<InitMenuListRespVo>> initAgent(@RequestBody(required = false) InitAgentReqVo initAgentReqVo) {
        return sysAgentService.initAgent(initAgentReqVo);
    }

    /**
     * 根据区县获取网点
     */
    @PostMapping("/getAccepts")
    @Operation(summary = "初始化区县")
    public CommonResult<List<InitMenuListRespVo>> getAccepts(@RequestBody(required = false) InitAgentReqVo initAgentReqVo) {
        return sysAgentService.selectAgentIds(initAgentReqVo);
    }
}
