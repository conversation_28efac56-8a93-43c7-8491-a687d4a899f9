package com.cmpay.code.module.system.controller.admin.sysagentcashier;

import com.cmpay.code.module.system.service.sysagentcashier.SysAgentCashierService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Tag(name = "管理后台 - 区县网点业务员用户")
@RestController
@RequestMapping("/agentcashier/agent-cashier")
@Validated
public class SysAgentCashierController {

    @Resource
    private SysAgentCashierService agentCashierService;


}
