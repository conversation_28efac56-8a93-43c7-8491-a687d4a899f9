package com.cmpay.code.module.system.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023-09-09 10:37:16
 * @version: 1.0
 */
@Accessors(chain = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportDeleteExcel {

    @ExcelProperty("区县id")
    private String agentId;
}
