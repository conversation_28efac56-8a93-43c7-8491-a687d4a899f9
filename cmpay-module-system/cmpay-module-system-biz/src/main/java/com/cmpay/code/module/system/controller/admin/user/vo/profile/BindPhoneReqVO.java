package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * @Title: BindPhoneReqVO
 * <AUTHOR>
 * @Package com.cmpay.code.cmpaymoduleagent.controller.admin.user.vo
 * @Date 2025/5/6 14:15
 * @description: 绑定手机号请求对象
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BindPhoneReqVO {
    @Schema(description = "手机号", required = true, example = "18888888888")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @NotEmpty(message = "参数不能为空(phone)")
    private String phone;
    @Schema(description = "验证码", required = true, example = "123123")
    @NotEmpty(message = "参数不能为空(code)")
    private String code;
}
