package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Title: BindWechatReqVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/5/6 16:48
 * @description: 获取微信二维码
 */
@Data
@Schema(description = "管理后台 - 获取微信绑定/解绑二维码请求对象")
public class BindWechatReqVO {
    @Schema(description = "二维码类型", required = true, example = "unbindWechat")
    @NotEmpty(message = "参数不能为空(qrCodeType)")
    private String qrCodeType;
}
