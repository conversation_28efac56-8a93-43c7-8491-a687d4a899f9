package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Title: BindWechatRespVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/5/6 14:55
 * @description: 绑定微信响应对象
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BindWechatRespVO {
    @Schema(description = "授权ID", required = true, example = "18888888888")
    private String authId;
    @Schema(description = "授权地址", required = true, example = "https://xxx")
    private String weChatAuthUrl;

}
