package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * @Title: ProfileChangeSecurityPhoneReqVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/4/15 14:37
 * @description: 个人信息修改密码
 */
@Data
@Schema(description = "个人信息修改安全手机号请求对象")
@Accessors(chain = true)
public class ProfileChangeSecurityPhoneReqVO {
    @Schema(description = "原手机号",required = true,example = "18888888888")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @NotEmpty(message = "参数不能为空(oldPhone)")
    private String oldPhone;
    @Schema(description = "原手机号验证码",required = true,example = "123123")
    @NotEmpty(message = "参数不能为空(oldCode)")
    private String oldCode;
    @Schema(description = "新手机号",required = true,example = "18888888888")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @NotEmpty(message = "参数不能为空(newPhone)")
    private String newPhone;
    @Schema(description = "新手机号验证码",required = true,example = "123123")
    @NotEmpty(message = "参数不能为空(newCode)")
    private String newCode;
}
