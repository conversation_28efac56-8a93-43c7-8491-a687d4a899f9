package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * @Title: ProfileSendCodeReqVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/4/15 14:13
 * @description: 个人信息发送验证码接口
 */
@Data
@Schema(description = "个人信息发送验证码请求对象")
@Accessors(chain = true)
public class ProfileSendCodeReqVO {
    @Schema(description = "手机号",required = true,example = "18888888888")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @NotEmpty(message = "参数不能为空(phone)")
    private String phone;
}
