package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.cmpay.code.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * @Title: WechatAuthListRespVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/5/6 16:30
 * @description: 微信授权列表响应对象
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "管理后台 - 微信授权列表响应对象")
public class WechatAuthListRespVO {
    @Schema(description = "头像地址", required = true, example = "123123123")
    private String headimgurl;
    @Schema(description = "昵称", required = true, example = "123123123")
    private String nickname;
    @Schema(description = "绑定时间", required = true, example = "123123123")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private Date bindTime;
}
