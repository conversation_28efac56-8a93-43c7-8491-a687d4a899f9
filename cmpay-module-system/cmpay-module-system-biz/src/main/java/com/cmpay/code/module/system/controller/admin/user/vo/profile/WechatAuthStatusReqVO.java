package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Title: WechatAuthStatusReqVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/5/6 15:41
 * @description: 微信授权状态请求对象
 */
@Data
@Schema(description = "管理后台 - 微信授权状态请求对象")
public class WechatAuthStatusReqVO {
    private String authId;
}
