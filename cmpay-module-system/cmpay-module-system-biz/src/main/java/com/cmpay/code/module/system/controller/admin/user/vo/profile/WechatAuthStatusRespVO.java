package com.cmpay.code.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Title: WechatAuthStatusRespVO
 * <AUTHOR>
 * @Package com.cmpay.code.module.system.controller.admin.user.vo.profile
 * @Date 2025/5/6 15:42
 * @description: 微信授权状态响应对象
 */
@Data
@Schema(description = "管理后台 - 微信授权状态响应对象")
public class WechatAuthStatusRespVO {
    @Schema(description = "状态；0过期；1未扫码；2已扫码；3通过", required = true, example = "123123123")
    private String status;
}
