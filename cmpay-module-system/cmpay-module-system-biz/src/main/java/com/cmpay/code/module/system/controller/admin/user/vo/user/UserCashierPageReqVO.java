package com.cmpay.code.module.system.controller.admin.user.vo.user;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserCashierPageReqVO extends PageParam {

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "姓名")
    private String nickname;

}
