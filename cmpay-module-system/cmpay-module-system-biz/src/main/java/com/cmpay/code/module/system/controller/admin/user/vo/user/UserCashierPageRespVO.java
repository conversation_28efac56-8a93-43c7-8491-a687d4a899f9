package com.cmpay.code.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserCashierPageRespVO {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "账号/手机号")
    private String username;

    @Schema(description = "区县id")
    private String agentId;

    @Schema(description = "区县名称")
    private String agentName;

    @Schema(description = "网点id")
    private String acceptId;

    @Schema(description = "网店名称")
    private String acceptName;

    @Schema(description = "名称")
    private String nickname;

    @Schema(description = "平台")
    private Integer platform;

    @Schema(description = "平台id")
    private String platformId;

}
