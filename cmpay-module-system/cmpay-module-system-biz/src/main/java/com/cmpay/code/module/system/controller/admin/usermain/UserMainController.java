package com.cmpay.code.module.system.controller.admin.usermain;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import com.cmpay.code.module.system.controller.admin.usermain.vo.*;
import com.cmpay.code.module.system.convert.usermain.UserMainConvert;
import com.cmpay.code.module.system.dal.dataobject.usermain.UserMainDO;
import com.cmpay.code.module.system.service.usermain.UserMainService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 所有商户用户")
@RestController
@RequestMapping("/usermain/user-main")
@Validated
public class UserMainController {

    @Resource
    private UserMainService userMainService;

    @PostMapping("/create")
    @Operation(summary = "创建所有商户用户")
    @PreAuthorize("@ss.hasPermission('usermain:user-main:create')")
    public CommonResult<String> createUserMain(@Valid @RequestBody UserMainCreateReqVO createReqVO) {
        return success(userMainService.createUserMain(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新所有商户用户")
    @PreAuthorize("@ss.hasPermission('usermain:user-main:update')")
    public CommonResult<Boolean> updateUserMain(@Valid @RequestBody UserMainUpdateReqVO updateReqVO) {
        userMainService.updateUserMain(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除所有商户用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('usermain:user-main:delete')")
    public CommonResult<Boolean> deleteUserMain(@RequestParam("id") String id) {
        userMainService.deleteUserMain(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得所有商户用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('usermain:user-main:query')")
    public CommonResult<UserMainRespVO> getUserMain(@RequestParam("id") String id) {
        UserMainDO userMain = userMainService.getUserMain(id);
        return success(UserMainConvert.INSTANCE.convert(userMain));
    }

    @GetMapping("/list")
    @Operation(summary = "获得所有商户用户列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('usermain:user-main:query')")
    public CommonResult<List<UserMainRespVO>> getUserMainList(@RequestParam("ids") Collection<String> ids) {
        List<UserMainDO> list = userMainService.getUserMainList(ids);
        return success(UserMainConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得所有商户用户分页")
    @PreAuthorize("@ss.hasPermission('usermain:user-main:query')")
    public CommonResult<PageResult<UserMainRespVO>> getUserMainPage(@Valid UserMainPageReqVO pageVO) {
        PageResult<UserMainDO> pageResult = userMainService.getUserMainPage(pageVO);
        return success(UserMainConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出所有商户用户 Excel")
    @PreAuthorize("@ss.hasPermission('usermain:user-main:export')")
    @OperateLog(type = EXPORT)
    public void exportUserMainExcel(@Valid UserMainExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<UserMainDO> list = userMainService.getUserMainList(exportReqVO);
        // 导出 Excel
        List<UserMainExcelVO> datas = UserMainConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "所有商户用户.xls", "数据", UserMainExcelVO.class, datas);
    }

}
