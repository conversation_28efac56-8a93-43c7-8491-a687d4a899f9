package com.cmpay.code.module.system.controller.admin.usermain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 所有商户用户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class UserMainBaseVO {

    @Schema(description = "xmid")
    private String xmid;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "名称", example = " ")
    private String name;

    @Schema(description = "性别", required = true)
    @NotNull(message = "性别不能为空")
    private String sex;

    @Schema(description = "身份")
    private String identity;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区县")
    private String county;

    @Schema(description = "创建时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime insertTime;

}
