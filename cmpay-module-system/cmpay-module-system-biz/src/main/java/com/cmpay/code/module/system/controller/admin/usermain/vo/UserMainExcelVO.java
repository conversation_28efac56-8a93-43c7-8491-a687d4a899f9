package com.cmpay.code.module.system.controller.admin.usermain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 所有商户用户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class UserMainExcelVO {

    @ExcelProperty("id")
    private String xmid;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("性别")
    private String sex;

    @ExcelProperty("身份")
    private String identity;

    @ExcelProperty("生日")
    private String birthday;

    @ExcelProperty("省")
    private String province;

    @ExcelProperty("市")
    private String city;

    @ExcelProperty("区县")
    private String county;

    @ExcelProperty("创建时间")
    private LocalDateTime insertTime;

}
