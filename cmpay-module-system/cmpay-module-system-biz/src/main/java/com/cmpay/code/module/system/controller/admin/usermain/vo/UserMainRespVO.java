package com.cmpay.code.module.system.controller.admin.usermain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 所有商户用户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserMainRespVO extends UserMainBaseVO {

    @Schema(description = "id", required = true, example = "15274")
    private String xmid;

}
