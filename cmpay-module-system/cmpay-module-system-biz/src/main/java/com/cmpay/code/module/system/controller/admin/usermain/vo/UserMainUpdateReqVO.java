package com.cmpay.code.module.system.controller.admin.usermain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 所有商户用户更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserMainUpdateReqVO extends UserMainBaseVO {

    @Schema(description = "id", required = true, example = "15274")
    @NotNull(message = "id不能为空")
    private String xmid;

}
