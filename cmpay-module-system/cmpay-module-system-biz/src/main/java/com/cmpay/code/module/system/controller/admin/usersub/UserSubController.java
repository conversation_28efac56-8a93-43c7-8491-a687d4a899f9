package com.cmpay.code.module.system.controller.admin.usersub;

import com.cmpay.code.framework.common.pojo.CommonResult;
import com.cmpay.code.framework.common.pojo.PageResult;
import com.cmpay.code.framework.excel.core.util.ExcelUtils;
import com.cmpay.code.framework.operatelog.core.annotations.OperateLog;
import com.cmpay.code.module.system.controller.admin.usersub.vo.*;
import com.cmpay.code.module.system.convert.usersub.UserSubConvert;
import com.cmpay.code.module.system.dal.dataobject.usersub.UserSubDO;
import com.cmpay.code.module.system.service.usersub.UserSubService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.cmpay.code.framework.common.pojo.CommonResult.success;
import static com.cmpay.code.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;


@Tag(name = "管理后台 - 小程序绑定用户")
@RestController
@RequestMapping("/usersub/user-sub")
@Validated
public class UserSubController {

    @Resource
    private UserSubService userSubService;

    @PostMapping("/create")
    @Operation(summary = "创建小程序绑定用户")
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:create')")
    public CommonResult<String> createUserSub(@Valid @RequestBody UserSubCreateReqVO createReqVO) {
        return success(userSubService.createUserSub(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新小程序绑定用户")
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:update')")
    public CommonResult<Boolean> updateUserSub(@Valid @RequestBody UserSubUpdateReqVO updateReqVO) {
        userSubService.updateUserSub(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除小程序绑定用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:delete')")
    public CommonResult<Boolean> deleteUserSub(@RequestParam("id") String id) {
        userSubService.deleteUserSub(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得小程序绑定用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:query')")
    public CommonResult<UserSubRespVO> getUserSub(@RequestParam("id") String id) {
        UserSubDO userSub = userSubService.getUserSub(id);
        return success(UserSubConvert.INSTANCE.convert(userSub));
    }

    @GetMapping("/list")
    @Operation(summary = "获得小程序绑定用户列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:query')")
    public CommonResult<List<UserSubRespVO>> getUserSubList(@RequestParam("ids") Collection<String> ids) {
        List<UserSubDO> list = userSubService.getUserSubList(ids);
        return success(UserSubConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得小程序绑定用户分页")
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:query')")
    public CommonResult<PageResult<UserSubRespVO>> getUserSubPage(@Valid UserSubPageReqVO pageVO) {
        PageResult<UserSubDO> pageResult = userSubService.getUserSubPage(pageVO);
        return success(UserSubConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出小程序绑定用户 Excel")
    @PreAuthorize("@ss.hasPermission('usersub:user-sub:export')")
    @OperateLog(type = EXPORT)
    public void exportUserSubExcel(@Valid UserSubExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<UserSubDO> list = userSubService.getUserSubList(exportReqVO);
        // 导出 Excel
        List<UserSubExcelVO> datas = UserSubConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "小程序绑定用户.xls", "数据", UserSubExcelVO.class, datas);
    }

}
