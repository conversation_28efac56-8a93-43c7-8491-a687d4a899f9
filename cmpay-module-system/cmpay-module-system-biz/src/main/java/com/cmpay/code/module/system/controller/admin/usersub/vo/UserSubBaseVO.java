package com.cmpay.code.module.system.controller.admin.usersub.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 小程序绑定用户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class UserSubBaseVO {

    @Schema(description = "用户唯一id，v3系统作废", required = true, example = "21948")
    @NotNull(message = "用户唯一id，v3系统作废不能为空")
    private String xmid;

    @Schema(description = "公众号或小程序appid", required = true, example = "488")
    @NotNull(message = "公众号或小程序appid不能为空")
    private String appid;

    @Schema(description = "支付宝loginid", example = "17254")
    private String loginId;

    @Schema(description = "开放平台id", example = "25654")
    private String unionid;

    @Schema(description = "昵称", example = "赵六")
    private String nickname;

    @Schema(description = "头像", example = "https://www.iocoder.cn")
    private String headimgurl;

    @Schema(description = "是否关注，0：未关注；1：关注")
    private Integer subscribe;

    @Schema(description = "入库时间", required = true)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime insertTime;

}
