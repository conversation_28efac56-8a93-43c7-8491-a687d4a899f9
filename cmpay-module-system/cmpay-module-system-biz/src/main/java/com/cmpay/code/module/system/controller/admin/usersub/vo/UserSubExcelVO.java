package com.cmpay.code.module.system.controller.admin.usersub.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小程序绑定用户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class UserSubExcelVO {

    @ExcelProperty("用户唯一id，v3系统作废")
    private String xmid;

    @ExcelProperty("公众号或小程序appid")
    private String appid;

    @ExcelProperty("openid")
    private String openid;

    @ExcelProperty("支付宝loginid")
    private String loginId;

    @ExcelProperty("开放平台id")
    private String unionid;

    @ExcelProperty("昵称")
    private String nickname;

    @ExcelProperty("头像")
    private String headimgurl;

    @ExcelProperty("是否关注，0：未关注；1：关注")
    private Integer subscribe;

    @ExcelProperty("入库时间")
    private LocalDateTime insertTime;

}
