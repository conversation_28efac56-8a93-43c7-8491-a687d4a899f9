package com.cmpay.code.module.system.controller.admin.usersub.vo;

import com.cmpay.code.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.cmpay.code.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 小程序绑定用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserSubPageReqVO extends PageParam {

    @Schema(description = "用户唯一id，v3系统作废", example = "21948")
    private String xmid;

    @Schema(description = "公众号或小程序appid", example = "488")
    private String appid;

    @Schema(description = "支付宝loginid", example = "17254")
    private String loginId;

    @Schema(description = "开放平台id", example = "25654")
    private String unionid;

    @Schema(description = "昵称", example = "赵六")
    private String nickname;

    @Schema(description = "头像", example = "https://www.iocoder.cn")
    private String headimgurl;

    @Schema(description = "是否关注，0：未关注；1：关注")
    private Integer subscribe;

    @Schema(description = "入库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] insertTime;

}
