package com.cmpay.code.module.system.controller.admin.usersub.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 小程序绑定用户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserSubRespVO extends UserSubBaseVO {

    @Schema(description = "openid", required = true, example = "25582")
    private String openid;

}
