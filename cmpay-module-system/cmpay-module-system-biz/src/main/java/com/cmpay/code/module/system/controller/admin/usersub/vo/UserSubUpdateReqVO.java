package com.cmpay.code.module.system.controller.admin.usersub.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 小程序绑定用户更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserSubUpdateReqVO extends UserSubBaseVO {

    @Schema(description = "openid", required = true, example = "25582")
    @NotNull(message = "openid不能为空")
    private String openid;

}
