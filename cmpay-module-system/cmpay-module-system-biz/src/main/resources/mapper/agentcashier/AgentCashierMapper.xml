<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.sysagentcashier.SysAgentCashierMapper">

    <select id="selectYwyId" resultType="com.cmpay.code.module.system.dal.dataobject.sysagentcashier.SysAgentCashierDO">
        select ac.* ,u.name ,u.xmid from user u left join agent_cashier ac on u.openid=ac.openid where status = 1
            and agent_id = #{agentId}
    </select>

    <select id="selectYwyByAcceptId" resultType="com.cmpay.code.module.system.dal.dataobject.sysagentcashier.SysAgentCashierDO">
        select ac.*,u.name ,u.xmid from user u left join agent_cashier ac on u.openid=ac.openid where status = 1
                    and accept_id = #{acceptId}
    </select>

    <select id="selectAllYwy" resultType="com.cmpay.code.module.system.dal.dataobject.sysagentcashier.SysAgentCashierDO">
        select ac.*,u.name ,u.xmid from user u left join agent_cashier ac on u.openid=ac.openid where accept_id = #{acceptId}
    </select>
</mapper>