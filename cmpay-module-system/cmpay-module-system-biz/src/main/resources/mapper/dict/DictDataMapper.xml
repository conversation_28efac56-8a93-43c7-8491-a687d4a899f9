<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.dict.DictDataMapper">

    <!-- 查询字典值 -->
    <select id="initDictType"  resultType="com.cmpay.code.module.system.controller.admin.dict.vo.type.InitDictTypeRespVo">
         SELECT label,`value` FROM system_dict_data WHERE dict_type = #{dictType} and status = 0
    </select>
    <!-- 根据字典键查询字典值 -->
    <select id="keyByValue" resultType="java.lang.String">
        SELECT label FROM system_dict_data WHERE value = #{dictKey} and status = 0
    </select>

    <select id="getLabelByValue" resultType="java.lang.String">
        SELECT label FROM system_dict_data WHERE value = #{dictKey} and dict_type = #{dictType} and status = 0
    </select>
</mapper>