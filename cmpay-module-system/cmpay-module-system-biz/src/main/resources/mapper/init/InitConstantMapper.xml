<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.init.InitConstantMapper">


    <select id="initProjectMap" resultType="com.cmpay.code.module.system.controller.admin.init.vo.ProjectInfosVO">
        select address, type
        from project_infos;
    </select>
    <select id="initConstantMap"
            resultType="com.cmpay.code.module.system.controller.admin.init.vo.ConstantInfosVO">
        select constant_type, constant_value
        from constant_infos;
    </select>
</mapper>