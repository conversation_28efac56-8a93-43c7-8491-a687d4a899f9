<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.minipcloginuser.MiniPcLoginUserMapper">

    <!-- 删除用户最后一次登录 -->
    <delete id="deleteLoginUser">
        DELETE FROM mini_pc_login_user
        where platform_id = #{oldPlatformId}
        and user_id = #{oldUserId}
        and platform = #{oldPlatform}
    </delete>

    <!-- 查询用户最后一次登录 -->
    <select id="findByLoginUser"
            resultType="com.cmpay.code.module.system.dal.dataobject.minipcloginuser.MiniPcLoginUserDO">
        select id,username,user_id,platform_id,role_id,platform_name,platform,role_name,nickname,insert_time
        from mini_pc_login_user
        where platform_id = #{oldPlatformId}
        and user_id = #{oldUserId}
        and platform = #{oldPlatform}
    </select>
</mapper>