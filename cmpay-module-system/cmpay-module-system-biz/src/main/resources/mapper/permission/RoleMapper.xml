<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.permission.RoleMapper">

    <!-- 用户管理——用户角色下拉 -->

    <select id="selectUserRole"
            resultType="com.cmpay.code.module.system.controller.admin.permission.vo.role.SelectUserRoleRespVO">
        SELECT id role_id,`name` role_name
        FROM system_role
        <where>
            <if test="roleList != null and roleList.size > 0">
                and id in
                <foreach collection="roleList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>