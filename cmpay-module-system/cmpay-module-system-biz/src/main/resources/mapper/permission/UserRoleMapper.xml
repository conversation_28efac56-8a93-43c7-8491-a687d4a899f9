<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.permission.UserRoleMapper">

    <delete id="deleteUserRole">
        delete from system_user_role
        <where>
            <if test="userRoleIdList != null and userRoleIdList.size > 0">
                and id in
                <foreach collection="userRoleIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteUserRoleOne">
        delete from system_user_role where id = #{userRoleId}
    </delete>

    <!-- 查询用户角色信息 -->
    <select id="searchUserRole"
            resultType="com.cmpay.code.module.system.controller.admin.aochuangadmin.vo.InternalUserDetailsResp">
        SELECT GROUP_CONCAT(DISTINCT sr.id) AS roleIds,su.username account,su.nickname username,sur.platform,GROUP_CONCAT(sur.id) AS userRoleIds,sur.platform_id,sur.company_name,su.id user_id,su.mobile phone
        FROM system_users su
        JOIN system_user_role sur
        ON su.id = sur.user_id
        JOIN system_role sr
        ON sur.role_id = sr.id
        where su.id = #{id} and sur.platform_id = #{platformId}
        GROUP BY su.username,sur.platform_id
    </select>

    <!-- 查询这个人是否在当前机构存在 -->
    <select id="searchOrangeUser"
            resultType="com.cmpay.code.module.system.dal.dataobject.permission.UserRoleDO">
        SELECT id,user_id,role_id,platform_id,creator,create_time,updater,update_time,deleted,tenant_id,company_name,platform
        FROM system_user_role
        where user_id = #{userId}
        and platform_id = #{platformId}
        and platform = #{platform}
    </select>

    <!-- 查询机构下面是否只有一个超管 -->
    <select id="findIsOneAdmin" resultType="com.cmpay.code.module.system.dal.dataobject.permission.UserRoleDO">
        SELECT id,user_id,role_id,platform_id,creator,create_time,updater,update_time,deleted,tenant_id,company_name,platform
        FROM system_user_role
        where platform_id = #{oldPlatformId}
        and platform = #{oldPlatform}
        <choose>
            <when test="oldPlatform == '1'.toString()">
                and role_id = '119'
            </when>
            <when test="oldPlatform == '2'.toString()">
                and role_id = '121'
            </when>
            <when test="oldPlatform == '3'.toString()">
                and role_id = '123'
            </when>
            <when test="oldPlatform == '4'.toString()">
                and role_id = '126'
            </when>
            <when test="oldPlatform == '5'.toString()">
                and role_id = '129'
            </when>
        </choose>
    </select>

    <!-- 查询公海商户给那些市开放了权限（引入不到partner_right表，随机写的） -->
    <select id="getPartnerRight" resultType="java.lang.String">
        SELECT partner_id FROM `partner_right`
        <where>
            <if test="type == 'is_application_open_account'">
                and is_application_open_account = '1'
            </if>
            <if test="type == 'is_visit_task'">
                and is_visit_task = '1'
            </if>
            <if test="type == 'is_target_mer'">
                and is_target_mer = '1'
            </if>
            <if test="type == 'pc_account_subsidy'">
                and pc_account_subsidy = '1'
            </if>
        </where>
    </select>

    <!-- 查询公海商户给那些省开放了权限（引入不到partner_right表，随机写的） -->
    <select id="getBranchRight" resultType="java.lang.String">
        SELECT DISTINCT  pc.branch_office_id
        FROM `partner_right` pr
        LEFT JOIN partner_channel pc ON pr.partner_id = pc.partner_id
        <where>
            <if test="type == 'is_application_open_account'">
                and pr.is_application_open_account = '1'
            </if>
            <if test="type == 'is_visit_task'">
                and pr.is_visit_task = '1'
            </if>
        </where>
    </select>

    <select id="getBranchRightByType" resultType="java.lang.String">
        SELECT branch_id
        FROM branch_right
        <where>
            <if test="type == 'is_target_mer'">
                and is_target_mer = '1'
            </if>
            <if test="type == 'is_visit_task'">
                and is_visit_task = '1'
            </if>
            <if test="type == 'is_account_subsidy'">
                and is_account_subsidy = '1'
            </if>
        </where>
    </select>

    <!-- 查询公海商户给那些区县开放了权限（引入不到partner_right表，随机写的） -->
    <select id="getAgentRight" resultType="java.lang.String">
        SELECT DISTINCT ag.agent_id
        FROM agent ag
        LEFT JOIN partner_right pr ON ag.partner_id = pr.partner_id
        <where>
            <if test="type == 'is_application_open_account'">
                and pr.is_application_open_account = '1'
            </if>
            <if test="type == 'is_visit_task'">
                and pr.is_visit_task = '1'
            </if>
        </where>
    </select>

    <!-- 查询公海商户给那些网点开放了权限（引入不到partner_right表，随机写的） -->
    <select id="getAcceptRight" resultType="java.lang.String">
        SELECT DISTINCT ac.accept_id
        FROM `accept` ac
                 LEFT JOIN  agent ag ON ac.agent_id = ag.agent_id
                 LEFT JOIN partner_right pr ON ag.partner_id = pr.partner_id
        <where>
            <if test="type == 'is_application_open_account'">
                and pr.is_application_open_account = '1'
            </if>
            <if test="type == 'is_visit_task'">
                and pr.is_visit_task = '1'
            </if>
        </where>
    </select>

    <!-- 根据用户id和平台查询用户归属机构 -->
    <select id="getPlatformId" resultType="com.cmpay.code.module.system.dal.dataobject.permission.UserRoleDO">
        SELECT * FROM system_user_role WHERE user_id = #{userId} and platform = #{platform} LIMIT 1
    </select>


    <select id="getBranchIdByOrg" resultType="java.lang.String">
        <choose>
            <when test="platform == '1'.toString()">
                SELECT id FROM branch_office WHERE id = #{org}
            </when>
            <when test="platform == '2'.toString()">
                SELECT branch_office_id FROM partner_channel WHERE partner_id = #{org}
            </when>
            <when test="platform == '3'.toString()">
                SELECT pc.branch_office_id FROM agent ag LEFT JOIN partner_channel pc ON ag.partner_id = pc.partner_id
                WHERE agent_id = #{org}
            </when>
            <when test="platform == '4'.toString()">
                SELECT pc.branch_office_id FROM accept ac LEFT JOIN agent ag ON ac.agent_id = ag.agent_id LEFT JOIN
                partner_channel pc ON ag.partner_id = pc.partner_id
                WHERE accept_id = #{org}
            </when>
        </choose>
    </select>
    <select id="getUserRoleByUserId"
            resultType="com.cmpay.code.module.system.dal.dataobject.permission.UserRoleDO">
        select * from system_user_role where user_id = #{userId}
    </select>
</mapper>