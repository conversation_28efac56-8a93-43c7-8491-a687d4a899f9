<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.roleextend.SystemRoleExtendMapper">

    <select id="selectRoleExtendPage" resultType="com.cmpay.code.module.system.controller.admin.roleextend.vo.RoleExtendRespVO">
        select * from system_role_extend
            <where>
            <if test="orgId!=null and orgId!=''">
                org_id = #{orgId}
            </if>
                <if test="roleId!=null and roleId!=''">
                    and role_id = #{roleId}
                </if>
            </where>
    </select>
</mapper>