<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.sysaccept.SysAcceptMapper">

    <!-- 初始化区县 -->
    <select id="initAccept" resultType="com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo">
        SELECT ac.accept_id id,ac.company
        FROM accept ac
        LEFT JOIN agent ag
        ON ac.agent_id = ag.agent_id
        LEFT JOIN  partner_channel pc
        ON ag.partner_id=pc.partner_id
        LEFT JOIN branch_office bo
        ON pc.branch_office_id = bo.id
        <where>
            and pc.partner_id !='parent_channel_6216' and pc.partner_id !='parent_channel_4988'
            <if test="initAcceptReqVo.partnerId != null and initAcceptReqVo.partnerId != '' and initAcceptReqVo.partnerId !='all'">
                and ag.partner_id = #{initAcceptReqVo.partnerId}
            </if>
            <if test="initAcceptReqVo.branchId != null and initAcceptReqVo.branchId != '' and initAcceptReqVo.branchId !='all'">
               and  pc.branch_office_id = #{initAcceptReqVo.branchId}
            </if>
            <if test="initAcceptReqVo.agentId != null and initAcceptReqVo.agentId != '' and initAcceptReqVo.agentId !='all'">
                and ac.agent_id = #{initAcceptReqVo.agentId}
            </if>
            <if test="initAcceptReqVo.company != null and initAcceptReqVo.company != ''">
                and (ac.company like concat('%',#{initAcceptReqVo.company},'%') or ac.accept_id = #{initAcceptReqVo.company})
            </if>
            <if test="initAcceptReqVo.isReport != null and initAcceptReqVo.isReport != '' and initAcceptReqVo.isReport == '1'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                <if test="initAcceptReqVo.isGd != null and initAcceptReqVo.isGd != '' and initAcceptReqVo.isGd == '1'.toString()">
                    and bo.id = '2'
                </if>
                <if test="initAcceptReqVo.isGd != null and initAcceptReqVo.isGd != '' and initAcceptReqVo.isGd == '0'.toString()">
                    and bo.id != '2'
                </if>
            </if>
            <if test="initAcceptReqVo.isReport != null and initAcceptReqVo.isReport != '' and initAcceptReqVo.isReport == '2'.toString()">
            and bo.status = '1'
            and bo.id != '1'
            and pc.branch_office_id in ('3','6','9')
            </if>
        </where>
    </select>
    <!-- 根据区县id查询机构 -->
    <select id="selectAgentUser" resultType="com.cmpay.code.module.system.dal.dataobject.sysaccept.SysAcceptDO">
        select accept_id,company from accept where agent_id = #{agentId}
    </select>

    <select id="selectAcceptById" resultType="com.cmpay.code.module.system.dal.dataobject.sysaccept.SysAcceptDO">
        select accept_id,agent_id,password,keeper,keeperphone,company,phone,address,rate_yirongma,rate_yirongma_300,
               rate_yirongma_300up,rate_shoubei,insert_time,rate_fufeitong,rate_wx_micro,rate_jialian,rate_cloudpay,
               rate_cloudpay_50down,rate_chuanhua,rate_huifu,rate_suixingfu,rate_duolabao,rate_sub,rate_subalipay,
               rate_xinhui,rate_liandong,rate_ruiyinxin,rate_haike,rate_ccb,rate_subunionpay,rate_sjb,rate_wft_hs,
               rate_fubei,rate_pufabank,rate_sxf_tq,rate_pabank,rate_tonglian,rate_zjnx,rate_lakala,rate_wangkebao,
               rate_lakala_mis,rate_zhifu,yz_org_id,rate_xiandai,up_pwd_time,sjb_pid,sjb_account,UPDATE_TIME,rate_dianxin,
               rate_youzheng,rate_yz_pay,rate_ylsw,rate_cmb
        from accept
        where accept_id = #{acceptId}
    </select>

    <select id="selectAccpetByAgentIds" resultType="com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo">
        select accept_id as id ,company
        from accept
        <where>
            <if test="agentIds != null and agentIds.size > 0">
                and agent_id in
                <foreach collection="agentIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>