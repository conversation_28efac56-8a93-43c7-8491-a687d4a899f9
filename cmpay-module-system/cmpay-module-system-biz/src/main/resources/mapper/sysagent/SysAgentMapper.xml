<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.sysagent.SysAgentMapper">
    <update id="updateAgentAccountSubsidy">
        UPDATE agent a
            JOIN agent_right ac ON a.agent_id = ac.agent_id
            SET ac.is_account_subsidy = 0
        WHERE
            <if test="partnerId != null and partnerId != ''">
                a.partner_id = #{partnerId}
            </if>
    </update>

    <!-- 初始化区县 -->
    <select id="initAgent" resultType="com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo">
        select a.agent_id id,a.company
        from agent a
        LEFT JOIN  partner_channel pc
        ON a.partner_id=pc.partner_id
        LEFT JOIN branch_office bo
        ON pc.branch_office_id = bo.id
        <where>
            and pc.partner_id !='parent_channel_6216' and pc.partner_id !='parent_channel_4988'
            <if test="initAgentReqVo.partnerId != '' and initAgentReqVo.partnerId != null and initAgentReqVo.partnerId != 'all'">
                and a.partner_id = #{initAgentReqVo.partnerId}
            </if>
            <if test="initAgentReqVo.branchId != '' and initAgentReqVo.branchId != null and initAgentReqVo.branchId != 'all'">
                and pc.branch_office_id = #{initAgentReqVo.branchId}
            </if>
            <if test="initAgentReqVo.company != null and initAgentReqVo.company != ''">
                and (a.company like concat('%',#{initAgentReqVo.company},'%') or a.agent_id = #{initAgentReqVo.company})
            </if>
            <if test="initAgentReqVo.isReport != null and initAgentReqVo.isReport != '' and initAgentReqVo.isReport == '1'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                <if test="initAgentReqVo.isGd != null and initAgentReqVo.isGd != '' and initAgentReqVo.isGd == '1'.toString()">
                    and bo.id = '2'
                </if>
                <if test="initAgentReqVo.isGd != null and initAgentReqVo.isGd != '' and initAgentReqVo.isGd == '0'.toString()">
                    and bo.id != '2'
                </if>
            </if>
            <if test="initAgentReqVo.isReport != null and initAgentReqVo.isReport != '' and initAgentReqVo.isReport == '2'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                and  pc.branch_office_id in ('3','6','9')
            </if>
        </where>
    </select>

    <!-- 根据市级id查询机构 -->
    <select id="selectPartnerUser" resultType="com.cmpay.code.module.system.dal.dataobject.sysagent.SysAgentDO">
        select agent_id,company from agent where partner_id = #{partnerId}
    </select>
    <!-- 获取这个商户的apiKey -->
    <select id="getApiKey" resultType="java.lang.String">
        select a.api_key from agent a,merchant b where b.shop_id = #{shopId} and a.agent_id = b.agent_id
    </select>


    <!-- 初始化区县 -->
    <select id="selectAgentIds" resultType="com.cmpay.code.module.system.controller.admin.sysaccept.vo.InitMenuListRespVo">
        select a.agent_id id,a.company
        from agent a
        LEFT JOIN  partner_channel pc
        ON a.partner_id=pc.partner_id
        LEFT JOIN branch_office bo
        ON pc.branch_office_id = bo.id
        <where>
            <if test="initAgentReqVo.partnerId != '' and initAgentReqVo.partnerId != null and initAgentReqVo.partnerId != 'all'">
                and a.partner_id = #{initAgentReqVo.partnerId}
            </if>
            <if test="initAgentReqVo.branchId != '' and initAgentReqVo.branchId != null and initAgentReqVo.branchId != 'all'">
                and pc.branch_office_id = #{initAgentReqVo.branchId}
            </if>
            <if test="initAgentReqVo.company != null and initAgentReqVo.company != ''">
                and (a.company like concat('%',#{initAgentReqVo.company},'%') or a.agent_id = #{initAgentReqVo.company})
            </if>
            <if test="initAgentReqVo.isReport != null and initAgentReqVo.isReport != '' and initAgentReqVo.isReport == '1'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                <if test="initAgentReqVo.isGd != null and initAgentReqVo.isGd != '' and initAgentReqVo.isGd == '1'.toString()">
                    and bo.id = '2'
                </if>
                <if test="initAgentReqVo.isGd != null and initAgentReqVo.isGd != '' and initAgentReqVo.isGd == '0'.toString()">
                    and bo.id != '2'
                </if>
            </if>
            <if test="initAgentReqVo.isReport != null and initAgentReqVo.isReport != '' and initAgentReqVo.isReport == '2'.toString()">
                and bo.status = '1'
                and bo.id != '1'
                and  pc.branch_office_id in ('3','6','9')
            </if>
        </where>
    </select>
</mapper>