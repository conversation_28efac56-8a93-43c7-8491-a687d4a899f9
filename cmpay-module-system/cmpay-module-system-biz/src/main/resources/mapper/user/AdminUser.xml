<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.user.AdminUserMapper">

    <!-- 根据id修改用户名 -->
    <update id="updateNameById">
        update system_users set nickname = #{nickname} where id = #{userId}
    </update>

    <update id="updateNameByXmid">
        update system_users set nickname = #{name} where user_id_old = #{xmid}
    </update>

    <update id="updateUsernameByXmid">
        update system_users
        <set>
            <if test="name != null and name != ''">
                nickname = #{name},
            </if>
            <if test="phone != null and phone != ''">
                username = #{phone},
            </if>
        </set>
        where user_id_old = #{xmid}
    </update>

    <!-- 是否已添加该用户 -->
    <select id="isUserExists" resultType="com.cmpay.code.module.system.dal.dataobject.user.AdminUserDO">
        select id,username,password,nickname,user_id_old,platform,dept_id,post_ids,email,mobile,avatar,status,
        login_ip,login_date,tenant_id,creator,create_time,updater,update_time,deleted
        from system_users
        where user_id_old = #{xmid}
    </select>

    <!-- 根据xmid查询名称 -->
    <select id="selectXmidByName" resultType="com.cmpay.code.module.system.dal.dataobject.user.AdminUserDO">
        select user_id_old,nickname FROM system_users WHERE user_id_old = #{xmid}
    </select>

    <!-- ——查询用户分页 -->
    <!-- and su.is_merchant = 0-->
    <select id="getInternalUserPage"
            resultType="com.cmpay.code.module.system.controller.admin.aochuangadmin.vo.InternalUserRespVO">
        SELECT su.id user_id,su.username,su.nickname,sur.role_id,sur.platform_id company_id,sur.platform as platform2,sur.company_name,sur.create_time,GROUP_CONCAT(sr.name) AS role_names,su.mobile AS phone
        FROM system_users su
        JOIN system_user_role sur
        ON su.id = sur.user_id
        JOIN system_role sr
        ON sur.role_id = sr.id
        <where>
            and sur.is_merchant = 0
            <if test="userPageReqVO.username != null and userPageReqVO.username != ''">
                and su.username = #{userPageReqVO.username}
            </if>
            <if test="userPageReqVO.nickname != null and userPageReqVO.nickname != ''">
                and su.nickname like concat('%',#{userPageReqVO.nickname},'%')
            </if>
            <if test="userPageReqVO.roleName != null and userPageReqVO.roleName != ''">
                and sr.name = #{userPageReqVO.roleName}
            </if>
            <if test="userPageReqVO.platformId != null and userPageReqVO.platformId != ''">
                and sur.platform = #{userPageReqVO.platformId}
            </if>
            <if test="userPageReqVO.companyId != null and userPageReqVO.companyId != ''">
                and sur.platform_id = #{userPageReqVO.companyId}
            </if>
            <if test="userPageReqVO.start != null and userPageReqVO.start != ''">
                and su.create_time <![CDATA[  >=  ]]> #{userPageReqVO.start}
            </if>
            <if test="userPageReqVO.end != null and userPageReqVO.end != ''">
                and su.create_time <![CDATA[  <=  ]]> #{userPageReqVO.end}
            </if>
            <if test="orange != null and orange != ''">
                and sur.platform_id = #{orange}
            </if>
        </where>
        GROUP BY su.username,sur.platform_id
        ORDER BY sur.create_time desc
    </select>

    <select id="importDeleteExcel" resultType="com.cmpay.code.module.system.dal.dataobject.permission.UserRoleDO">
        SELECT * FROM system_user_role WHERE user_id = #{userId} and platform_id = #{shopId}
        order by role_id desc
        limit 1
    </select>
    <delete id="deleteUserRole">
        delete from system_user_role where id = #{id}
    </delete>

    <select id="cashierPage"
            resultType="com.cmpay.code.module.system.controller.admin.user.vo.user.UserCashierPageRespVO">
        select su.id as user_id,su.username,sur.platform_id,sur.platform,su.nickname from system_users su left join system_user_role sur on su.id = sur.user_id
        <where>
            sur.role_id in(125,128)
            <if test="ucprv.agentId != null and ucprv.agentId != ''">
                and sur.platform_id = #{ucprv.agentId}
            </if>
            <if test="ucprv.acceptId != null and ucprv.acceptId != ''">
                and sur.platform_id = #{ucprv.acceptId}
            </if>
            <if test="ucprv.phone != null and ucprv.phone != ''">
                and su.username = #{ucprv.phone}
            </if>
            <if test="ucprv.nickname != null and ucprv.nickname != ''">
                and su.nickname like concat('%',#{ucprv.nickname},'%')
            </if>
        </where>
    </select>
    <select id="selectUserByPlatformId"
            resultType="com.cmpay.code.module.system.controller.admin.user.vo.user.UserCashierPageRespVO">
        select su.id as user_id,su.username,sur.platform_id,sur.platform,su.nickname
        from system_users su
        left join system_user_role sur
        on su.id = sur.user_id
        <where>
            sur.role_id = 119
            and su.username like concat('%',#{username},'%')
            and sur.platform_id = #{platformId}
        </where>
    </select>

    <!-- 根据id或者账号查询用户信息 -->
    <select id="getUserByIdOrUsername"
            resultType="com.cmpay.code.module.system.dal.dataobject.user.AdminUserDO">
        select * from system_users
        <where>
            and (user_id_old = '' or user_id_old is null)
            <if test="account != null and account != ''">
                and (username = #{account} or id = #{account})
            </if>
        </where>
    </select>
    <select id="selectByPlatformId" resultType="com.cmpay.code.module.system.dal.dataobject.user.AdminUserDO">
        select su.* from system_users su left join system_user_role sur on su.id = sur.user_id where sur.platform_id = #{platformId} and su.username=#{username} GROUP BY su.username
    </select>
</mapper>