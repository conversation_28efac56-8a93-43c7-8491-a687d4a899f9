<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.code.module.system.dal.mysql.assistantuser.AssistantUserMapper">

    <!-- 修改xmid -->
    <update id="updateByXmid">
        update `user` set xmid = #{xmid} where phone = #{phone}
    </update>


    <!--    <select id="searchUserInfoByPhone"-->
<!--            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">-->
<!--        select xmid,-->
<!--               openid,-->
<!--               name,-->
<!--               phone,-->
<!--               birthday,-->
<!--               company,-->
<!--               address,-->
<!--               work,-->
<!--               identity,-->
<!--               school,-->
<!--               first_time,-->
<!--               nickname,-->
<!--               sex,-->
<!--               headimgurl,-->
<!--               country,-->
<!--               province,-->
<!--               city,-->
<!--               unionid,-->
<!--               subscribe,-->
<!--               type,-->
<!--               loginid,-->
<!--               password,-->
<!--               UPDATE_TIME-->
<!--        from user-->
<!--        where phone = #{phone}-->
<!--    </select>-->
    <select id="searchUserInfoByPhone"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select xmid,
               openid,
               `name`,
               phone,
               birthday,
               company,
               address,
               `work`,
               identity,
               school,
               first_time,
               nickname,
               sex,
               headimgurl,
               country,
               province,
               city,
               unionid,
               subscribe,
               type,
               loginid,
               `password`,
               UPDATE_TIME
        from user
        where phone = #{phone}
    </select>

    <!-- 批量查询user表数据 -->
    <select id="getAll" resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select xmid,openid,`name`,phone,birthday,company,address,`work`,`identity`,school,first_time,nickname,sex,
               headimgurl,country,province,city,unionid,subscribe,`type`,loginid,password,update_time
        from user
        LIMIT #{pageNo},#{pageSize}
    </select>

    <select id="getCashierName"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select xmid,openid,`name`,phone,birthday,company,address,`work`,`identity`,school,first_time,nickname,sex,
               headimgurl,country,province,city,unionid,subscribe,`type`,loginid,password,update_time
        from user
        WHERE xmid = #{xmid}
    </select>


    <select id="getUser"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select xmid,openid,`name`,phone,birthday,company,address,`work`,`identity`,school,first_time,nickname,sex,
               headimgurl,country,province,city,unionid,subscribe,`type`,loginid,password,update_time
        from user
        where phone = #{phone}
    </select>

    <!-- 根据openid查询user用户 -->
    <select id="getAccountUser"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select xmid,openid,`name`,phone,birthday,company,address,`work`,`identity`,school,first_time,nickname,sex,
               headimgurl,country,province,city,unionid,subscribe,`type`,loginid,password,update_time
        from user
        where openid = #{openid}
        order by first_time desc
    </select>
    <select id="findXmidByPhone"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select xmid,openid,`name`,phone,birthday,company,address,`work`,`identity`,school,first_time,nickname,sex,
               headimgurl,country,province,city,unionid,subscribe,`type`,loginid,password,update_time
        from user
        where phone = #{phone}
        order by first_time desc
    </select>

    <!-- 获取所有用户 -->
    <select id="getUserAll"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        select * from user
    </select>

    <!--  -->
    <select id="synchronizeMerchantUserRole"
            resultType="com.cmpay.code.module.system.dal.dataobject.assistantuser.AssistantUserDO">
        SELECT u.* from user u LEFT JOIN system_users su ON u.xmid = su.user_id_old WHERE su.id is null
    </select>


    <select id="getPhone" parameterType="map" resultType="string">
        SELECT DISTINCT phone FROM user
        <where>
            <if test="openids != null and openids.size() > 0">
                <choose>
                    <when test="openids.size() == 1">
                        openid = #{openids[0]}
                    </when>
                    <otherwise>
                        openid IN
                        <foreach item="item" collection="openids" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
</mapper>