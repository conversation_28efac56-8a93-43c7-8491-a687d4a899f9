<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>cmpay</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmpay-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <properties>
        <project_name>${project.artifactId}</project_name>
    </properties>
    <description>
        后端 Server 的主项目，通过引入需要 cmpay-module-xxx 的依赖，
        从而实现提供 RESTful API 给 cmpay-ui-admin、cmpay-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-module-pc</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-module-agent</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-login</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-spring-boot-starter-biz-error-code</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmpay.code</groupId>
            <artifactId>cmpay-module-internal</artifactId>
            <version>${revision}</version>
        </dependency>
<!--        api工程-->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-api-web</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 应用管理工程 -->
        <dependency>
            <groupId>com.cmpay.code</groupId>
            <artifactId>cmpay-appliedmanagement</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 数据报表 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-report-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 工作流 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-bpm-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 支付服务 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-pay-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 微信公众号模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-mp-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 商城相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-promotion-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-product-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>cmpay-module-trade-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-spring-boot-starter-banner</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>cmpay-spring-boot-starter-protection</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>


    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <activatedProfile>local</activatedProfile>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <activatedProfile>test</activatedProfile>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <activatedProfile>dev</activatedProfile>
            </properties>
        </profile>
        <profile>
            <id>cmflocal</id>
            <properties>
                <activatedProfile>cmflocal</activatedProfile>
            </properties>
        </profile>
        <profile>
            <id>cmftest</id>

            <properties>
                <activatedProfile>cmftest</activatedProfile>
            </properties>
        </profile>
    </profiles>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project_name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
                    <include>application-${activatedProfile}.yml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>static/**</include>
                    <include>*.xml</include>
                    <include>*.txt</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <delimiters>
                        <delimiter>@</delimiter>  <!-- 使用 @ 作为占位符分隔符 -->
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>  <!-- 禁用默认的 ${} 分隔符 -->
                </configuration>
            </plugin>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.8</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
