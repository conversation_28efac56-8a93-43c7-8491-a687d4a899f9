server:
  port: 48080

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration # 排除积木报表带来的 MongoDB 的自动配置
  datasource:
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 10 # 初始连接数
        min-idle: 5 # 最小连接池数量
        max-active: 30 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 600000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 600000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        remove-abandoned: true
      primary: master
      datasource:
        master:
          name: pay
          url: ************************************************/${spring.datasource.dynamic.datasource.master.name}?zeroDateTimeBehavior=CONVERT_TO_NULL&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&autoReconnect=true&autoReconnectForPools=true&connectTimeout=300000&socketTimeout=600000 # MySQL Connector/J 8.X 连接的示例
#          url: *****************************************/${spring.datasource.dynamic.datasource.master.name}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true # MySQL Connector/J 8.X 连接的示例
#          url: ************************************************/${spring.datasource.dynamic.datasource.master.name}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true # MySQL Connector/J 8.X 连接的示例
#          url: jdbc:mysql://************/${spring.datasource.dynamic.datasource.master.name}?useSSL=false&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai # MySQL Connector/J 5.X 连接的示例
          username: newmanage
          password: NewManage123

  #          username: pb_admin
#          password: pb_admin123
#          username: root
#          password: root
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: ************* # 地址
    port: 6379 # 端口
    database: 1 # 数据库索引
    password: 1qaz@WSX!23 # 密码，建议生产环境开启
--- #################### 定时任务相关配置 ####################

# Quartz 配置项，对应 QuartzProperties 配置类
spring:
  quartz:
    auto-startup: false # 本地开发环境，尽量不要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

# Resilience4j 配置项
resilience4j:
  ratelimiter:
    instances:
      backendA:
        limit-for-period: 1 # 每个周期内，允许的请求数。默认为 50
        limit-refresh-period: 60s # 每个周期的时长，单位：微秒。默认为 500
        timeout-duration: 1s # 被限流时，阻塞等待的时长，单位：微秒。默认为 5s
        register-health-indicator: true # 是否注册到健康监测

--- #################### 监控相关配置 ####################


# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
     #配置自己写的 MyBatis Mapper 打印日志
#    com.cmpay.code.module.bpm.dal.mysql: debug
#    com.cmpay.code.module.infra.dal.mysql: debug
#    com.cmpay.code.module.infra.dal.mysql.job.JobLogMapper: INFO # 配置 JobLogMapper 的日志级别为 info
#    com.cmpay.code.module.pay.dal.mysql: debug
#    com.cmpay.code.module.pay.dal.mysql.notify.PayNotifyTaskMapper: INFO # 配置 JobLogMapper 的日志级别为 info
    com.cmpay.code.module.system.dal.mysql: debug
#    com.cmpay.code.module.tool.dal.mysql: debug
#    com.cmpay.code.module.member.dal.mysql: debug
#    com.cmpay.code.module.trade.dal.mysql: debug
#    com.cmpay.code.module.promotion.dal.mysql: debug
    com.cmpay.code.cmpaymodulepc.dal.mysql: debug
    com.cmpay.code.cmpayinternal.dal.mysql: debug
    com.cmpay.code.cmpaymoduleagent.dal.mysql: debug

debug: true

# cmpay配置项，设置当前项目所有自定义的配置
cmpay:
  captcha:
    enable: true # 本地环境，暂时关闭图片验证码，方便登录等接口的测试；
  security:
    mock-enable: true

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui