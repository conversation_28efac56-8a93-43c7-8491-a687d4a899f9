platform:
  cmpay
spring:
  application:
    name: cmpay-server

  profiles:
    active: @activatedProfile@

  main:
    # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-circular-references: true

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 20MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
  #    throw-exception-if-no-handler-found: true # 404 错误时抛出异常，方便统一处理
  #    static-path-pattern: /static/** # 静态资源路径; 注意：如果不配置，则 throw-exception-if-no-handler-found 不生效！！！ TODO ：不能配置，会导致 swagger 不生效

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********  .401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: false
    path: /v3/api-docs
  swagger-ui:
    enabled: false
    path: /swagger-ui

knife4j:
  enable: true
  setting:
    language: zh_cn


# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  type-aliases-package: ${cmpay.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark:  # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 10 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 60 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 10 # get 接口一分钟内请求数限制
    req-check-minute-limit: 10 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 10 # verify 接口一分钟内请求数限制

--- #################### cmpay相关配置 ####################
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 1

# 自定义接口拦截配置
interfaceAccess:
  second: 60     # 接口访问second秒内最多只能访问requestCount次
  requestCount: 200    # 请求20次
  lockTime: 60      # 禁用时长--单位/秒


cmpay:
  info:
    version: 1.0.0
    base-package: com.cmpay.code
  web:
    admin-ui:
      url: http://dashboard.cmpay.iocoder.cn # Admin 管理后台 UI 的地址
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
#  websocket:
#    enable: false # websocket的开关
#    path: /websocket/message # 路径
#    maxOnlineCount: 0 # 最大连接人数
#    sessionMap: true # 保存sessionMap
  swagger:
    title: cmpay快速开发平台
    description: 提供管理后台、用户 App 的所有功能
    version: ${cmpay.info.version}
    url: ${cmpay.web.admin-ui.url}
    email: <EMAIL>
    license: MIT
    license-url: https://gitee.com/zhijiantianya/ruoyi-vue-pro/blob/master/LICENSE
  captcha:
    enable: true # 验证码的开关，默认为 true
  codegen:
    base-package: ${cmpay.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
  error-code: # 错误码相关配置项
    constants-class-list:
      - com.cmpay.code.module.bpm.enums.ErrorCodeConstants
      - com.cmpay.code.module.infra.enums.ErrorCodeConstants
      - com.cmpay.code.module.member.enums.ErrorCodeConstants
      - com.cmpay.code.module.pay.enums.ErrorCodeConstants
      - com.cmpay.code.module.system.enums.ErrorCodeConstants
      - com.cmpay.code.module.mp.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    enable: false
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /admin-api/pay/notify/callback/* # 支付回调通知，不携带租户编号
      - /jmreport/* # 积木报表，无法携带租户编号
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，无法携带租户编号
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - infra_codegen_column
      - infra_codegen_table
      - infra_test_demo
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - jimu_dict
      - jimu_dict_item
      - jimu_report
      - jimu_report_data_source
      - jimu_report_db
      - jimu_report_db_field
      - jimu_report_db_param
      - jimu_report_link
      - jimu_report_map
      - jimu_report_share
      - rep_demo_dxtj
      - rep_demo_employee
      - rep_demo_gongsi
      - rep_demo_jianpiao
      - tmp_report_data_1
      - tmp_report_data_income
#  sms-code: # 短信验证码相关的配置项
#    expire-times: 10m
#    send-frequency: 1m
#    send-maximum-quantity-per-day: 10
#    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
#    end-code: 9999 # 这里配置 9999 的原因是applicationsStream，测试方便。
  trade:
    order:
      app-id: 1 # 商户编号
      expire-time: 2h # 支付的过期时间

debug: false
