<?xml version="1.0" encoding="UTF-8" ?>

<configuration>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <property name="PATTERN_DEFAULT"
              value="[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%p] [%tid] [%t:%r] [%logger{50}.%M:%L] - %m%n"/>

    <!-- 控制台 Appender -->
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">　　　　　
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
    </appender>

    <!--RollingFileAppender 可以做到每天输出一个日志文件-->
    <!-- 正常日志-->
    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--输出路径-->
            <!-- 线上使用 -->
            <fileNamePattern>/data/logs/tomcat/%d{yyyy-MM-dd}/info.log</fileNamePattern>
            <!-- 本地使用 -->
            <!--            <fileNamePattern>D:\logs/%d{yyyy-MM-dd}/info.log</fileNamePattern>-->
        </rollingPolicy>
    </appender>
    <!-- 错误日志-->
    <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--ThresholdFilter 根据范围过滤，即大于等于 error 的日志都记录-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 线上使用 -->
            <fileNamePattern>/data/logs/tomcat/%d{yyyy-MM-dd}/error.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <appender name="grpcLog" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="consoleLog"></appender-ref>
        <appender-ref ref="fileInfoLog"></appender-ref>
        <appender-ref ref="fileErrorLog"></appender-ref>
        <appender-ref ref="grpcLog"></appender-ref>
    </root>
</configuration>
