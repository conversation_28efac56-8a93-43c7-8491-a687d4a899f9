<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
    <title>支付测试页</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.js"></script>
    <script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
</head>
<body>
<div>点击如下按钮，发起支付的测试</div>
<div>
    <button id="wx_pub">微信公众号</button>
</div>
</body>
<script>
    let shopOrderId = undefined;
    let payOrderId = undefined;
    // let server = 'http://127.0.0.1:48080';
    let server = 'http://niubi.natapp1.cc';
    // TODO openid
    let openid = "ockUAwIZ-0OeMZl9ogcZ4ILrGba0";
    $(function() {
        // 获得 JsapiTicket
        // 参考 https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html 文档
        $.ajax({
            url: server + "/app-api/wx/mp/create-jsapi-signature?url=" + document.location.href,
            method: 'POST',
            success: function( result ) {
                if (result.code !== 0) {
                    alert('获取 JsapiTicket 失败，原因：' + result.msg)
                    return;
                }
                var jsapiTicket = result.data;
                jsapiTicket.jsApiList = ['chooseWXPay'];
                jsapiTicket.debug = false;

                // 初始化 JS
                wx.config(jsapiTicket);
            }
        });

        // 自动发起商城订单编号
        $.ajax({
            url: server + "/app-api/shop/order/create",
            method: 'POST',
            success: function( result ) {
                if (result.code !== 0) {
                    alert('创建商城订单失败，原因：' + result.msg)
                    return;
                }
                shopOrderId = result.data.id;
                payOrderId = result.data.payOrderId;
                console.log("商城订单：" + shopOrderId)
                console.log("支付订单：" + payOrderId)
            }
        })
    })

    // 微信公众号
    $( "#wx_pub").on( "click", function() {
        if (typeof WeixinJSBridge == "undefined") {
            // if (document.addEventListener) {
            //     document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
            // } else if (document.attachEvent) {
            //     document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
            //     document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
            // }
            alert('微信支付，只支持在微信客户端中使用！');
            return;
        }
        if (navigator.userAgent.indexOf('wechatdevtools') >= 0) {
            alert('微信支付，无法在微信开发者工具中使用！请使用微信客户端！');
            return;
        }

        // 提交支付
        // 参考 https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=7_7&index=6 文档
        // 参考 https://segmentfault.com/a/1190000020704650 文档
        $.ajax({
            url: server + "/app-api/pay/order/submit",
            method: 'POST',
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify({
                "id": payOrderId,
                "channelCode": 'wx_pub',
                "channelExtras": {
                    "openid": openid
                }
            }),
            success: function( result ) {
                if (result.code !== 0) {
                    alert('提交支付订单失败，原因：' + result.msg)
                    return;
                }
                alert('点击确定，开始微信支付');
                // 开始调用微信支付
                let data = result.data.invokeResponse;
                wx.chooseWXPay({
                    timestamp: data.timeStamp,
                    nonceStr: data.nonceStr,
                    package: data.packageValue,
                    signType: data.signType,
                    paySign: data.paySign,
                    success: function (res) {
                        alert(JSON.stringify(res));
                    },
                    error: function(e) {
                        alert(JSON.stringify(e));
                    }
                });
            }
        })
    });
</script>
</html>
