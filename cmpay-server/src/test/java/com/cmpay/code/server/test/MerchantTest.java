package com.cmpay.code.server.test;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cmpay.code.api.service.merchantimagesapi.MerchantImagesApiServiceImpl;
import com.cmpay.code.api.service.merchantimagesapi.vo.MerchantImagesInfo;
import com.cmpay.code.cmpayinternal.controller.admin.merchantsubsidymsg.MerchantSubsidyMsgAsync;
import com.cmpay.code.cmpayinternal.dal.mysql.merchantsubsidymsg.MerchantSubsidyMsgMapper;
import com.cmpay.code.cmpayinternal.service.merchantsubsidycache.MerchantSubsidyCacheService;
import com.cmpay.code.cmpaymoduleagent.controller.admin.advertisement.dto.SelectByShopIdAndPlatformDTO;
import com.cmpay.code.cmpaymoduleagent.dal.dataobject.merchantindustry.MerchantIndustryDO;
import com.cmpay.code.cmpaymoduleagent.service.advertisementbpaa.AdvertisementBpaaService;
import com.cmpay.code.cmpaymodulepc.dal.dataobject.merchantchannel.MerchantChannelDO;
import com.cmpay.code.cmpaymodulepc.dal.mysql.merchant.MerchantMapper;
import com.cmpay.code.cmpaymodulepc.dal.mysql.merchantchannel.MerchantChannelMapper;
import com.cmpay.code.cmpaymodulepc.dal.mysql.statisticsMerchant.StatisticsMerchantMapper;
import com.cmpay.code.cmpaymodulepc.service.environmentconfig.EnvironmentConfigService;
import com.cmpay.code.framework.aliyun.client.antifake.AntiFakeClient;
import com.cmpay.code.framework.aliyun.model.Config;
import com.cmpay.code.framework.aliyun.model.antifake.AntiFakeRequest;
import com.cmpay.code.framework.aliyun.model.antifake.AntiFakeResponse;
import com.cmpay.code.framework.common.config.SystemConfig;
import com.cmpay.code.framework.common.util.collection.CollectionUtils;
import com.cmpay.code.framework.common.util.id.IdUtil;
import com.cmpay.code.module.system.service.assistantuser.AssistantUserService;
import com.cmpay.code.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.cmpay.code.framework.common.constant.ChannelConstant.CHANNEL_SXF_TQ_VALUE;

/**
 * author suohongbo
 * date 2024/1/15 14:54
 * version 1.0
 */
// 因为test是独立于springboot运行的，所以需要单独指定一个web服务器
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class MerchantTest {
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private StatisticsMerchantMapper statisticsMerchantMapper;
    @Resource
    private MerchantSubsidyCacheService merchantSubsidyCacheService;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private EnvironmentConfigService environmentConfigService;
    @Resource
    private MerchantSubsidyMsgAsync merchantSubsidyMsgAsync;
    @Resource
    private MerchantSubsidyMsgMapper merchantSubsidyMsgMapper;
    @Resource
    private AdvertisementBpaaService advertisementBpaaService;
    @Resource
    private MerchantChannelMapper merchantChannelMapper;
    @Resource
    private AssistantUserService assistantUserService;
    @Resource
    private AdminUserService adminUserService;
    @Resource
    private MerchantImagesApiServiceImpl merchantImagesApiService;


    @Test
    public void test1() {
        merchantMapper.updateMerchant("63bb84e78e1c9bd211c614b1e51fd6da", "oSNFZ0_OMsWh9K3CtYBFe8avhJAU", "123", "agent_8217_8419", null);
    }

    @Test
    public void test2() {
        JSONObject o = new JSONObject();
        JSONArray arr = new JSONArray();
        arr.add(1);

        o.put("arr", arr);
        JSONArray jsonArray1 = o.getJSONArray("arr");
        JSONArray jsonArray2 = o.getJSONArray("arr");
        for (int i = 0; i < jsonArray1.size(); i++) {
            Integer o1 = jsonArray1.getInteger(i);
            System.out.println(o1);
            jsonArray2.add(o1);
        }

    }


    @Test
    public void test3() {
        List<MerchantIndustryDO> industryDOList = new ArrayList<>();
        MerchantIndustryDO merchantIndustryDO1 = new MerchantIndustryDO();
        MerchantIndustryDO merchantIndustryDO2 = new MerchantIndustryDO();
        MerchantIndustryDO merchantIndustryDO3 = new MerchantIndustryDO();
        merchantIndustryDO1.setIndId(1);
        merchantIndustryDO2.setIndId(2);
        merchantIndustryDO3.setIndId(3);
        industryDOList.add(merchantIndustryDO1);
        industryDOList.add(merchantIndustryDO2);
        industryDOList.add(merchantIndustryDO3);

        String industryIds = industryDOList.stream().map(o -> String.valueOf(o.getIndId())).collect(Collectors.joining(","));
        System.out.println(industryIds);
    }


    /**
     * 阿里云调用图片审核demo
     */
    @Test
    public void test4() {
        Config config = new Config();
        config.setAccessKeyId("LTAI5tEChhFC49wtCWNTmsuy");
        config.setAccessKeySecret("******************************");
        long l1 = 0;
        for (int i = 0; i < 1; i++) {
            long l2 = System.currentTimeMillis();
            AntiFakeRequest antiFakeRequest = new AntiFakeRequest();
            antiFakeRequest.setCredentialType("0101");
            antiFakeRequest.setImage("http://ossmerchant.gdwxyf.com/5b88666733124580536e703b80e1fe73/identityFaceCopy.png?timestamp=1718939023792");
            AntiFakeClient antiFakeClient = new AntiFakeClient(config);
            AntiFakeResponse antiFakeResponse = antiFakeClient.credentialVerify(antiFakeRequest);
            long l3 = System.currentTimeMillis();
            l1 += (l3 - l2);
        }
        log.info("{}", l1);


    }


    @Test
    public void test5() {
        String shopId = "63bb84e78e1c9bd211c614b1e51fd6da";
        String type = "A";
        Date ywySubmitTime = new Date();
        merchantSubsidyMsgMapper.updateStatusYwySubmitTimeByShopIdAndType(shopId, type, 1, "", ywySubmitTime, null);

    }

    @Test
    public void test6() {
        HashSet<String> strings = new HashSet<>();
        strings.add("0");
        if (strings.contains("10")) {
            System.out.println("haha");
        }
    }

    @Test
    public void test7() {
        Long bizId = IdUtil.getBizId();
        System.out.println(bizId);
        System.out.println(String.valueOf(bizId).length());
    }


    @Test
    public void test8() {
        SelectByShopIdAndPlatformDTO device = advertisementBpaaService.getByShopIdAndPlatform("63bb84e78e1c9bd211c614b1e51fd6da", "device");
        System.out.println(device);
    }

    @Test
    public void test9() {
        String cycle = "90";
        LambdaUpdateWrapper<MerchantChannelDO> merchantChannelDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        merchantChannelDOLambdaUpdateWrapper
                .set(MerchantChannelDO::getMerchantOther11, cycle)
                .eq(MerchantChannelDO::getShopId, "63bb84e78e1c9bd211c614b1e51fd6da")
                .eq(MerchantChannelDO::getChannel, CHANNEL_SXF_TQ_VALUE);
        merchantChannelMapper.update(null, merchantChannelDOLambdaUpdateWrapper);
    }

    @Test
    public void test10() {
        List<String> a = new ArrayList<>();
        a.add("1");
        a.add("2");
        a.add("3");
        List<String> b = new ArrayList<>();
        b.add("1");
        b.add("2");
        b.add("4");
        List<String> list = CollectionUtils.notExistElement(a, b);
        System.out.println(list);
        List<String> list2 = CollectionUtils.notExistElement(b, a);
        System.out.println(list2);

    }


    @Test
    public void test11() {
        List<String> a = new ArrayList<>();
        a.add("");
        a.removeIf(String::isEmpty);
        System.out.println(a);

    }

    @Test
    public void test12() {
        String xmid = UUID.randomUUID().toString().replace("-", "");

    }

    @Test
    public void test13() {
        //获取商户的图片
        String s = "4fce69249fbda48f508d8617e3160b1e";
        String t = "cardFace";
        List<MerchantImagesInfo> merchantImages = merchantImagesApiService.getMerchantImages(s);
        String imageUrl = "";
        for (MerchantImagesInfo merchantImage : merchantImages) {
            if (merchantImage.getType().equals(t)) {
                imageUrl = merchantImage.getValue();
            }
        }
    }
}
