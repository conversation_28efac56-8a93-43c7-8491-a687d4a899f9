package com.cmpay.code.server.test;

import com.cmpay.code.cmpaymoduleagent.service.merchantratepackage.MerchantRatePackageRelationService;
import com.cmpay.code.framework.common.util.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Title: RatePackageTest
 * <AUTHOR>
 * @Package com.cmpay.code.server.test
 * @Date 2025/7/11 16:05
 * @description: 测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class RatePackageTest {

    @Resource
    private MerchantRatePackageRelationService merchantRatePackageRelationService;



    @Test
    public void test1() {
        merchantRatePackageRelationService.removeRatePackageRecoverRate("b02b2fafb58caf1145708e5dd64cad02");
    }

    @Test
    public void test2() {
        boolean b = merchantRatePackageRelationService.verifyRatePackageValidity("4fce69249fbda48f508d8617e3160b1e");
        System.out.println(b);
    }
}
