package com.cmpay.code.server.test;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.cmpay.code.cmpaymoduleagent.service.api.ApplicationService;
import com.cmpay.code.cmpaymoduleagent.service.api.dto.ApplicationResultDTO;
import com.cmpay.code.cmpaymoduleagent.service.api.dto.TestApplication;
import com.cmpay.code.cmpaymodulepc.service.chinaareano.ChinaAreaNoService;
import com.cmpay.code.framework.common.constant.LoginConstant;
import com.cmpay.code.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * author suohong<PERSON>
 * date 2023/12/28 9:29
 * version 1.0
 */
// 因为test是独立于springboot运行的，所以需要单独指定一个web服务器
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class SignTest {
    @Resource
    private ApplicationService applicationService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ChinaAreaNoService chinaAreaNoService;

    @Test
    public void test1() {
        TestApplication testApplication = new TestApplication();
        testApplication.setShopId("");
        testApplication.setProgramId("202311792173507165");
        testApplication.setRemark("测试");
        testApplication.setSign("1904E7B2C465A2D15DC5DFE4348C9C0D");
        String basicValue = "queryShopBindState";


        ApplicationResultDTO applicationResultDTO = applicationService.checkSecurity(testApplication, basicValue);
        if (!applicationResultDTO.getSuccess()) {
            System.out.println(applicationResultDTO.getMessage());
            return;
        }
        String key = applicationResultDTO.getMessage();
        // 返回结果
        CommonResult<Object> success = CommonResult.success(null);
        JSONObject responseBody = applicationService.encryResponse(success, key);
        Integer a = 1;
        Integer b = 2;
        System.out.println(a == b);
    }

    @Test
    public void test2() {
        String loginKey = "verify_login:20250414103934175166_38f579db50c043098bc1232f3d153e95";
        String auth = String.valueOf(stringRedisTemplate.opsForHash().get(loginKey, LoginConstant.VERIFY_LOGIN_AUTH_REDIS_KEY));
        com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(auth);
    }

    @Test
    public void test3() {
        String cityCode = chinaAreaNoService.getCityCode("111.225.241.50");
        System.out.println(cityCode);
    }


}
